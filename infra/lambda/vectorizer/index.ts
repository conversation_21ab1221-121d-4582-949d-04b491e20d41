import { type APIGatewayProxyResult, type Context } from "aws-lambda";
// import { db } from "~/server/db";
import {
  InvokeCommand,
  type InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import { SecretsManagerClient } from "@aws-sdk/client-secrets-manager";
import { env } from "~/env";
import { processSingleDDQSection } from "~/lib/ddq";
import { initWithSecrets } from "../../../src/lib/integrations/aws/utils";
import { type DocumentSection } from "../../../src/lib/summarizer";
import {
  generateChunksLambda,
  processDDQLambda,
  segmentDocumentLambda,
} from "../../../src/lib/vectorizer";
import { type ClassifierLambdaEvent } from "../classifier";
export type LambdaEvent = {
  documentId?: string;
  orgId: string;
  bucket?: string;
  egnyteAccessToken?: string;
  azureAccessToken?: string;
  secretId: string;
  docsPath?: string;
  section?: DocumentSection;
  sectionIndex?: number;
  totalSections?: number;
  stage:
    | "GENERATE_CHUNKS"
    | "SEGMENT_DOCUMENT"
    | "PROCESS_DDQ"
    | "PROCESS_DDQ_SECTION";
};

export const handler = async (
  event: LambdaEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  const client = new SecretsManagerClient({
    region: env.AWS_LAMBDA_REGION,
  });

  const lambdaClient = new LambdaClient();

  try {
    console.log("EVENT", JSON.stringify(event, null, 2));
    console.log("CONTEXT", JSON.stringify(context, null, 2));

    const { db, secret } = await initWithSecrets(event.secretId);

    const result =
      event.stage === "GENERATE_CHUNKS"
        ? await generateChunksLambda({
            db: db,
            orgId: event.orgId,
            bucket: event.bucket,
            documentId: event.documentId,
            egnyteAccessToken: event.egnyteAccessToken,
            azureAccessToken: event.azureAccessToken,
            secret: secret,
          })
        : event.stage === "SEGMENT_DOCUMENT"
          ? await segmentDocumentLambda({
              db: db,
              orgId: event.orgId,
              bucket: event.bucket,
              documentId: event.documentId,
              egnyteAccessToken: event.egnyteAccessToken,
              azureAccessToken: event.azureAccessToken,
              secretId: event.secretId,
              docsPath: event.docsPath,
            })
          : event.stage === "PROCESS_DDQ_SECTION"
            ? await processSingleDDQSection({
                db: db,
                orgId: event.orgId,
                documentId: event.documentId ?? "",
                section: event.section,
                sectionIndex: event.sectionIndex ?? 0,
                totalSections: event.totalSections ?? 0,
                secret: secret,
              })
            : event.stage === "PROCESS_DDQ"
              ? await processDDQLambda({
                  db: db,
                  orgId: event.orgId,
                  bucket: event.bucket,
                  documentId: event.documentId,
                  egnyteAccessToken: event.egnyteAccessToken,
                })
              : null;

    if (event.stage === "GENERATE_CHUNKS") {
      try {
        const invokeParams: InvokeCommandInput = {
          FunctionName: `classifier-${env.LAMBDA_ENV}`,
          InvocationType: "Event",
          Payload: JSON.stringify({
            documentId: event.documentId,
            orgId: event.orgId,
            secretId: event.secretId,
            type: "questions",
          } as ClassifierLambdaEvent),
        };

        const command = new InvokeCommand(invokeParams);
        const response = await lambdaClient.send(command);
        console.log("Classifier Lambda invoked", response);
      } catch (error) {
        console.error("Error invoking classifier lambda", error);
      }

      try {
        const invokeParams: InvokeCommandInput = {
          FunctionName: `classifier-${env.LAMBDA_ENV}`,
          InvocationType: "Event",
          Payload: JSON.stringify({
            documentId: event.documentId,
            orgId: event.orgId,
            secretId: event.secretId,
            type: "document",
          } as ClassifierLambdaEvent),
        };

        const command = new InvokeCommand(invokeParams);
        const response = await lambdaClient.send(command);
        console.log("Classifier Lambda invoked", response);
      } catch (error) {
        console.error("Error invoking classifier lambda", error);
      }
    }

    return {
      statusCode: 200,
      body: JSON.stringify("Success"),
    };
  } catch (err) {
    console.log(err);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error",
      }),
    };
  }
};
