from openpyxl import load_workbook
import json
import os

from datetime import datetime

class DateTimeEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, datetime):
            return o.isoformat()  # Converts to a string in ISO 8601 format
        try:
           return super().default(o)
        except:
           return str(o)

data = {"time": datetime.now()}

def main(filepath):
  # Load workbook
  wb = load_workbook(filepath)
  wb_valuedata = []
  wb_formatdata = []

  for sheetnames in wb.sheetnames:
    sheet_valuedata = []
    sheet_formatdata = []

    for row in wb[sheetnames].iter_rows():
      row_valuedata = []
      row_formatdata = []

      for cell in row:
          if not cell.value:
            continue
          cell_value = {
              "value": cell.value,
              "coordinate": cell.coordinate,
          }
          row_valuedata.append(cell_value)

          cell_format = {
              "formula": cell.formula if hasattr(cell, 'formula') else None,  # Check for formula
              "font": {
                  "name": cell.font.name,
                  "size": cell.font.size,
                  "bold": cell.font.bold,
                  "italic": cell.font.italic,
                  "color": cell.font.color.rgb if cell.font.color else None,
              },
              "fill": {
                  "bg_color": cell.fill.start_color.rgb if cell.fill.start_color else None,
                  "pattern_type": cell.fill.patternType,
              },
              "alignment": {
                  "horizontal": cell.alignment.horizontal,
                  "vertical": cell.alignment.vertical,
              },
              "coordinate": cell.coordinate,
          }
          row_formatdata.append(cell_format)
      if row_valuedata:
        sheet_valuedata.append(row_valuedata)
      if row_formatdata:
        sheet_formatdata.append(row_formatdata)
    wb_valuedata.append({"sheet_name": sheetnames , "rows": sheet_valuedata})
    wb_formatdata.append({"sheet_name": sheetnames, "rows": sheet_formatdata, "merged_cells": list(wb[sheetnames].merged_cells) })

  # return json.dumps(wb_valuedata, cls=DateTimeEncoder, indent=indent), json.dumps(wb_formatdata, cls=DateTimeEncoder, indent=indent)
  return wb_valuedata, wb_formatdata

if __name__ == "__main__":

  # @TODO get this directly from dvc.yaml
  file_to_parse = "data/raw/Financial/XLSX/[DDQ] TEST - Imprint Private Equity Data Request_Template_v01.2023_Vistria_vF_April 6.xlsx"
  indent = 4
  output_path = 'data/parsed-xlsx'
  # / get this directly from dvc.yaml

  wb_valuedata, wb_formatdata = main(file_to_parse)
  os.makedirs(output_path, exist_ok=True)
  with open(os.path.join(output_path, 'wb_valuedata.json'), 'w') as f:
      json.dump(wb_valuedata, f, cls=DateTimeEncoder, indent=indent)
  with open(os.path.join(output_path, 'wb_formatdata.json'), 'w') as f:
      json.dump(wb_formatdata, f, cls=DateTimeEncoder, indent=indent)
