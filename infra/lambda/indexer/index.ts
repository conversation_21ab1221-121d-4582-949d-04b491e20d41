import {
  InvokeCommand,
  InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import { DocumentSource } from "@prisma/client";
import { APIGatewayProxyResult, Context } from "aws-lambda";
import { env } from "~/env";
import { indexSharepoint } from "~/lib/integrations/azure/indexer";
import { indexEgnyte } from "~/lib/integrations/egnyte/indexer";
import { initWithSecrets } from "../../../src/lib/integrations/aws/utils";
import { LambdaEvent } from "../vectorizer";

export type IndexerLambdaEvent = {
  orgId: string;
  secretId?: string;
  source: DocumentSource;
};

export const handler = async (
  event: IndexerLambdaEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  try {
    console.log("EVENT", JSON.stringify(event, null, 2));
    console.log("CONTEXT", JSON.stringify(context, null, 2));

    const { db, secret } = await initWithSecrets(event.secretId as string);

    const orgAccessTokens = await (async () =>
      await db.org.findUnique({
        where: {
          id: event.orgId,
        },
        select: {
          egnyteAccessToken: true,
          azureAccessToken: true,
        },
      }))();

    const payload = JSON.stringify({
      orgId: event.orgId,
      egnyteAccessToken:
        event.source === DocumentSource.EGNYTE
          ? orgAccessTokens?.egnyteAccessToken?.accessToken
          : undefined,
      azureAccessToken:
        event.source === DocumentSource.SHAREPOINT
          ? orgAccessTokens?.azureAccessToken?.accessToken
          : undefined,
      secretId: event.secretId,
    } as LambdaEvent);

    console.log("Vectorizer Lambda Payload", payload);

    const lambdaClient = new LambdaClient();

    const documents =
      event.source === DocumentSource.SHAREPOINT
        ? await indexSharepoint(db, event.orgId)
        : event.source === DocumentSource.EGNYTE
          ? await indexEgnyte(db, event.orgId)
          : [];

    // Filter out documents that should not be indexed
    const filteredDocuments = documents.filter(
      (document) => !document.name.startsWith("~"),
    );

    for (const document of filteredDocuments) {
      const payload = JSON.stringify({
        orgId: event.orgId,
        egnyteAccessToken:
          event.source === DocumentSource.EGNYTE
            ? orgAccessTokens?.egnyteAccessToken?.accessToken
            : undefined,
        azureAccessToken:
          event.source === DocumentSource.SHAREPOINT
            ? orgAccessTokens?.azureAccessToken?.accessToken
            : undefined,
        secretId: event.secretId,
        documentId: document.id,
      } as LambdaEvent);

      const invokeParams: InvokeCommandInput = {
        FunctionName: `vectorizer-${env.LAMBDA_ENV}`,
        InvocationType: "Event",
        Payload: payload,
      };
      const command = new InvokeCommand(invokeParams);
      const lambdaResponse = await lambdaClient.send(command);
      console.log("Vectorizer Lambda invoked", lambdaResponse);
    }

    return {
      statusCode: 200,
      body: JSON.stringify("OK"),
    };
  } catch (err) {
    console.log(err);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error",
      }),
    };
  }
};
