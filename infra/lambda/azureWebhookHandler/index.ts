import {
  InvokeCommand,
  type InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import {
  DDQStatus,
  type Document,
  DocumentSource,
  DocumentType,
} from "@prisma/client";
import { type APIGatewayProxyResult, type Context } from "aws-lambda";
import { type AzureWebhookEvent } from "~/app/api/webhook/azure/route";
import { env } from "~/env";
import { crawlSharepointFolder } from "~/lib/integrations/azure/crawler";
import { type AzureWebhookResponse } from "~/lib/integrations/azure/types";
import { getSharepointDelta } from "~/lib/integrations/azure/utils";
import { generateChunksLambda } from "~/lib/vectorizer";
import { cleanupDocuments } from "~/server/api/routers/document";
import { type PrismaClientType } from "~/server/db";
import { getDocumentTypeByExtension } from "~/utils/document";
import { initWithSecrets } from "../../../src/lib/integrations/aws/utils";
import { type LambdaEvent } from "../vectorizer";

export type AzureWebhookLambdaEvent = {
  secretId?: string;
  payload: AzureWebhookResponse;
};

async function createDocuments(db: PrismaClientType, event: AzureWebhookEvent) {
  const botUser = await db.user.findFirst({
    where: {
      name: "Virgil AI",
    },
  });

  if (!botUser) {
    throw new Error("Bot user not found");
  }

  const newDocuments = await Promise.all(
    event.modifiedItems
      .filter((item) => !item.folder)
      .map(async (file) => {
        const fullPath =
          file.parentReference?.path.split(":")[1] + "/" + file.name;

        console.log("Checking if document exists", fullPath, file.id);

        const docExists = await db.document.findFirst({
          where: {
            // name: fullPath,
            azureItemId: file.id,
            orgId: event.orgId,
            source: DocumentSource.SHAREPOINT,
          },
        });

        if (!docExists) {
          return await db.document.create({
            data: {
              name: fullPath,
              ddqStatus: /DDQ/i.exec(fullPath) ? DDQStatus.PENDING : undefined,
              size: file.size,
              type: file.folder
                ? DocumentType.FOLDER
                : getDocumentTypeByExtension(file.name?.split(".")[1] ?? ""),
              source: DocumentSource.SHAREPOINT,
              url: file.webUrl,
              orgId: event.orgId,
              createdById: botUser.id,
              azureItemId: file.id,
            },
          });
        }
      }),
  );

  return newDocuments.filter((doc) => doc !== undefined);
}

async function deleteDocuments(db: PrismaClientType, event: AzureWebhookEvent) {
  const documentsToDelete = await db.document.findMany({
    where: {
      azureItemId: {
        in: event.deletedItems.map((item) => item.id),
      },
      orgId: event.orgId,
      source: DocumentSource.SHAREPOINT,
    },
  });

  console.log("Deleting documents", documentsToDelete);

  return await cleanupDocuments(db, event.orgId, {
    list: documentsToDelete.map((document) => ({
      id: document.id,
      name: document.name,
    })),
  });
}

async function renameDocuments(db: PrismaClientType, event: AzureWebhookEvent) {
  try {
    const modifiedFiles = event.modifiedItems.filter(
      (item) => item.file?.mimeType && item.parentReference?.path,
    );

    const modifiedFileNames = modifiedFiles
      .map(
        (item) =>
          item.parentReference?.path &&
          item.parentReference?.path.split(":")[1] + "/" + item.name,
      )
      .filter((name) => name !== undefined);

    console.log("Modified file names", modifiedFileNames);

    const documentsToRename = await db.document.findMany({
      where: {
        azureItemId: {
          in: modifiedFiles.map((item) => item.id),
        },
        name: {
          notIn: modifiedFileNames,
        },
        orgId: event.orgId,
        source: DocumentSource.SHAREPOINT,
      },
    });

    console.log("Renaming documents", documentsToRename);

    await Promise.all(
      documentsToRename.map(async (document) => {
        const item = modifiedFiles.find(
          (item) => item.id === document.azureItemId,
        );

        await db.document.update({
          where: { id: document.id },
          data: {
            name: item?.parentReference?.path.split(":")[1] + "/" + item?.name,
          },
        });
      }),
    );
  } catch (error) {
    console.error("Error renaming documents", error);
  }

  // If a folder is renamed, we need to rename the documents inside it
  // Sharepoint doesn't tell us what the previous folder name was, so we need to do this manually:
  // 1. Get full pathname of the folder
  // 2. Get all documents inside the new folder
  // 3. Update the documents with the new folder name

  const modifiedFolders = event.modifiedItems
    .filter((item) => item.folder && !item.root && !item.deleted)
    .map((item) => ({
      azureId: item.id,
      name: item.parentReference?.path.split(":")[1] + "/" + item.name,
    }));

  const existingFolders = await db.document.findMany({
    where: {
      name: {
        in: modifiedFolders.map((folder) => folder.name),
      },
      orgId: event.orgId,
      source: DocumentSource.SHAREPOINT,
    },
  });

  const renamedFolders = modifiedFolders.filter(
    (folder) => !existingFolders.some((f) => f.name === folder.name),
  );

  console.log("Modified folders", modifiedFolders);
  if (modifiedFolders.length > 0) {
    console.log(
      "Existing folders",
      existingFolders.map((f) => f.name),
    );
    console.log("Renamed folders", renamedFolders);

    const azureDrive = await db.azureDrive.findFirst({
      where: { orgId: event.orgId },
    });

    const renameTree = await crawlSharepointFolder(
      event.azureAccessToken,
      azureDrive?.azureId ?? "",
      renamedFolders[0]?.name ?? "",
      {
        "@odata.context": "",
        "@odata.nextLink": "",
        "@odata.deltaLink": "",
        value: event.modifiedItems.filter((item) =>
          renamedFolders.some((f) => f.azureId === item.id),
        ),
      },
      db,
    );

    const renamedSharedpointItems = renameTree.map((item) => {
      return {
        name: item.parentReference?.path.split(":")[1] + "/" + item.name,
        id: item.id,
      };
    });

    console.log("Renamed sharedpoint items", renamedSharedpointItems);

    const existingDocuments = await db.document.findMany({
      where: {
        azureItemId: {
          in: renameTree.map((item) => item.id),
        },
        orgId: event.orgId,
        source: DocumentSource.SHAREPOINT,
      },
    });

    console.log(
      "Existing documents before rename",
      existingDocuments.map((doc) => {
        return {
          name: doc.name,
          id: doc.azureItemId,
        };
      }),
    );

    await Promise.all(
      existingDocuments.map(async (document) => {
        const item = renamedSharedpointItems.find(
          (item) => item.id === document.azureItemId,
        );

        console.log(`Renaming document ${document.name} -> ${item?.name}`);

        await db.document.update({
          where: { id: document.id },
          data: { name: item?.name ?? document.name },
        });
      }),
    );
  }
}

async function executeVectorizer(
  db: PrismaClientType,
  event: AzureWebhookEvent,
  newDocuments: Document[],
  secretId: string | undefined,
) {
  const lambdaClient = new LambdaClient();

  // Get documents to revectorize:
  // Modified items that are not deleted
  // Renamed documents might have also been modified so revectorize them as well

  const documentIds = await db.document.findMany({
    where: {
      azureItemId: {
        in: event.modifiedItems.map((item) => item.id),
      },
      orgId: event.orgId,
      type: {
        notIn: [DocumentType.FOLDER],
      },
      source: DocumentSource.SHAREPOINT,
    },
  });

  // Add documents that were created in the webhook
  documentIds.push(...newDocuments);

  await Promise.all(
    documentIds.map(async (document) => {
      // Run vectorizer locally in dev environment
      if (process.env.NODE_ENV === "development") {
        console.time(`Vectorizing document ${document.name}`);
        const vectorizedDocument = await generateChunksLambda({
          db: db,
          orgId: event.orgId,
          azureAccessToken: event.azureAccessToken.accessToken,
          documentId: document.id,
        });
        console.timeEnd(`Vectorizing document ${document.name}`);

        return vectorizedDocument;
      } else {
        // Run vectorizer in Lambda on vercel
        console.log("Invoking Lambda");

        try {
          const invokeParams: InvokeCommandInput = {
            FunctionName: `vectorizer-${env.LAMBDA_ENV}`,
            InvocationType: "Event",
            Payload: JSON.stringify({
              documentId: document.id,
              orgId: event.orgId,
              azureAccessToken: event.azureAccessToken.accessToken,
              secretId: secretId,
            } as LambdaEvent),
          };

          const command = new InvokeCommand(invokeParams);
          const response = await lambdaClient.send(command);
          console.log("Lambda invoked", response);
        } catch (error) {
          console.error(error);
          throw error;
        }
      }
    }),
  );
}

export const processSharepointDelta = async (
  db: PrismaClientType,
  event: AzureWebhookLambdaEvent,
) => {
  console.log("Processing delta", JSON.stringify(event, null, 2));

  const delta = await getSharepointDelta(
    event.payload.value[0]?.subscriptionId ?? "",
    db,
  );

  if (delta) {
    console.log("Reprocessing all documents that were modified");
    try {
      await deleteDocuments(db, delta);
    } catch (error) {
      console.error("Error deleting documents", error);
    }

    try {
      await renameDocuments(db, delta);
    } catch (error) {
      console.error("Error renaming documents", error);
    }

    try {
      console.log("Creating new documents");
      const newDocuments = await createDocuments(db, delta);
      await executeVectorizer(db, delta, newDocuments, event.secretId);
    } catch (error) {
      console.error("Error creating new documents", error);
    }
  }
};

export const handler = async (
  event: AzureWebhookLambdaEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  try {
    console.log("EVENT", JSON.stringify(event, null, 2));
    console.log("CONTEXT", JSON.stringify(context, null, 2));

    const { db, secret } = await initWithSecrets(event.secretId!);

    await processSharepointDelta(db, event);

    return {
      statusCode: 200,
      body: JSON.stringify("OK"),
    };
  } catch (err) {
    console.log(err);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error",
      }),
    };
  }
};
