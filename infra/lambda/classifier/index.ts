import { type APIGatewayProxyResult, type Context } from "aws-lambda";
// import { db } from "~/server/db";
import {
  documentClassifier,
  documentFundClassifier,
  questionClassifier,
} from "../../../src/lib/classifier";
import { initWithSecrets } from "../../../src/lib/integrations/aws/utils";

export type ClassifierLambdaEvent = {
  documentId?: string;
  orgId: string;
  secretId: string;
  type: "questions" | "document";
};

export const handler = async (
  event: ClassifierLambdaEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  try {
    console.log("EVENT", JSON.stringify(event, null, 2));
    console.log("CONTEXT", JSON.stringify(context, null, 2));

    const { db, secret } = await initWithSecrets(event.secretId);

    const result =
      event.type === "questions"
        ? await questionClassifier({
            db: db,
            orgId: event.orgId,
            documentId: event.documentId,
            secret: secret,
          })
        : await Promise.all([
            documentClassifier({
              db: db,
              orgId: event.orgId,
              documentId: event.documentId,
              secret: secret,
            }),
            documentFundClassifier({
              db: db,
              orgId: event.orgId,
              documentId: event.documentId,
              secret: secret,
            }),
          ]);

    return {
      statusCode: 200,
      body: JSON.stringify(result),
    };
  } catch (err) {
    console.log(err);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error",
      }),
    };
  }
};
