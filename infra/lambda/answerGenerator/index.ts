import { type APIGatewayProxyResult, type Context } from "aws-lambda";

import {
  type QuestionInput,
  generateResponseFromDocumentQuestions,
} from "~/lib/rag-response";
import { type ModelParameters } from "~/sections/chat/ChatModelParametersSelector";
import {
  GetDocumentWithEmptyAnswersInclude,
  type GetDocumentWithEmptyAnswersType,
  overwriteExistingResponseContent,
} from "~/server/api/routers/document";
import { backoffDelay } from "~/server/utils/backoff";
import { initWithSecrets } from "../../../src/lib/integrations/aws/utils";

export type AnswerGeneratorLambdaEvent = {
  documentId: string;
  orgId: string;
  secretId: string;
  userId: string;
  batchPayload: QuestionInput[];
  tagIds: string[];
  fundIds: string[];
  modelParameters: ModelParameters;
};

export const handler = async (
  event: AnswerGeneratorLambdaEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  try {
    console.log("EVENT", JSON.stringify(event, null, 2));
    console.log("CONTEXT", JSON.stringify(context, null, 2));

    const { db, secret } = await initWithSecrets(event.secretId);

    const MAX_RETRIES = 5;
    let retries = 0;

    while (retries < MAX_RETRIES) {
      try {
        const response = await generateResponseFromDocumentQuestions(
          db,
          event.orgId,
          event.documentId,
          event.batchPayload,
          event.tagIds,
          event.fundIds,
          event.modelParameters,
          secret,
        );

        console.log(
          "*** generateResponseFromDocumentQuestions: responses",
          response?.length,
        );

        if (response === null) {
          throw new Error(
            "*** generateResponseFromDocumentQuestions: Response is null",
          );
        }
        // Validate response payload question IDsmatches input question IDs
        const validatedResponseIds = event.batchPayload.map((p) =>
          response.find((r) => r.questionId === p.questionId),
        );

        if (validatedResponseIds.length !== event.batchPayload.length) {
          throw new Error(
            "Response question IDs do not match input question IDs",
          );
        }

        const documentWithQuestionsAndEmptyAnswers: GetDocumentWithEmptyAnswersType =
          await db.document.findFirstOrThrow({
            where: {
              id: event.documentId,
              orgId: event.orgId,
            },
            include: {
              responses: {
                where: {
                  documentId: event.documentId,
                },

                include: GetDocumentWithEmptyAnswersInclude,
              },
            },
          });
        const answersAndResponses = response.map((response) => ({
          answer: response?.answer ?? "",
          reason: response?.reason ?? "",
          citations: response?.citations ?? [],
          response: documentWithQuestionsAndEmptyAnswers.responses.find((r) =>
            r.response.questions.find((q) => q.id === response?.questionId),
          ),
        }));

        console.log(
          "*** generateResponseFromDocumentQuestions: answersAndResponses",
          answersAndResponses,
        );

        console.time(
          `Overwriting existing response contents for ${documentWithQuestionsAndEmptyAnswers.name}. Total responses generated: ${response.length}`,
        );
        const updatedResponseContents = await overwriteExistingResponseContent(
          db,
          answersAndResponses,
          event.userId,
          event.orgId,
          secret,
        );

        console.timeEnd(
          `Overwriting existing response contents for ${documentWithQuestionsAndEmptyAnswers.name}. Total responses generated: ${response.length}`,
        );

        return {
          statusCode: 200,
          body: JSON.stringify(response),
        };
      } catch (err) {
        console.log(err);
        retries++;

        await backoffDelay(retries);
      }
    }

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: `Error generating responses after ${MAX_RETRIES} retries`,
      }),
    };
  } catch (err) {
    console.log(err);

    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error",
      }),
    };
  }
};
