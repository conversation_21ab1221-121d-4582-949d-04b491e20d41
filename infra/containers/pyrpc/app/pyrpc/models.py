from enum import Enum
from typing import List, Dict, Any, Literal, Optional
from pydantic import BaseModel, Field

# Enums
class ChatMessageStatus(str, Enum):
    RETRIEVING = "RETRIEVING"
    GENERATING_CITATIONS = "GENERATING_CITATIONS"
    VALIDATING_CITATIONS = "VALIDATING_CITATIONS"
    GENERATING_ANSWER = "GENERATING_ANSWER"
    RETRY = "RETRY"
    ERROR = "ERROR"
    READY = "READY"

# Base Models
class DocumentMetadata(BaseModel):
    languages: List[Literal["eng"]]
    page_number: Optional[int] = None
    page_name: Optional[str] = None
    text_as_html: Optional[str] = None
    image_base64: Optional[str] = None
    source: Optional[str] = None
    filetype: str

class DocumentChunkMetadata(BaseModel):
    id: str
    distance: Optional[int] = Field(alias="_distance", default=None)

# Request/Response Models
class RetrievalRequest(BaseModel):
    query: str = Field(description="The search query to retrieve relevant documents")
    limit: Optional[int] = Field(description="Maximum number of documents to retrieve")

class RetrievalResponse(BaseModel):
    chunks: List[Dict[str, Any]] = Field(description="The retrieved document chunks")

class LLMResponse(BaseModel):
    answer: str = Field(description="The answer to the user question.")

# Citation Models
class Citation(BaseModel):
    sourceId: Optional[int]
    quote: str
    fileName: str = Field(alias="filename")
    metadata: DocumentMetadata

class CitationFinalAnwser(BaseModel):
    quote: str = Field(description="The VERBATIM quote from the specified source that justifies the answer.")
    document_chunk_id: Optional[str] = Field(
        description="""
        The UNIQUE identifier of the specific document chunk that contains the cited information.
        
        REQUIRED FIELD - You MUST include this for every citation.
        
        This should be the exact 'id' field from the document chunk metadata that was returned 
        by the retrieval tool. This is CRITICAL for proper citation and source tracking.
        
        Example: If the chunk metadata is {{"id": "cmawf6yw0001f4nmo2o8j3vpd", ...}}, then use "cmawf6yw0001f4nmo2o8j3vpd"
        as the documentChunkId.
        """
    )

class CitationResponse(BaseModel):
    source_id: int = Field(description="The integer ID of a SPECIFIC source which justifies the answer.")
    quote: str = Field(description="The VERBATIM quote from the specified source that justifies the answer.")
    named_entity_mentioned: bool = Field(description="Whether the quote mentions the named entity from the question.")
    file_name: str = Field(description="The name of the file containing the citation.")

class DocumentChunkWithMetadata(BaseModel):
    pageContent: str
    metadata: DocumentChunkMetadata
    fileName: str = Field(alias="filename")

# Main Response Model
class RAGResponseWithCitationsFinalAnwser(BaseModel):
    answer: Optional[str] = Field(
        description="The answer to the user's question. Base entirely on retrieved sources. "
                   "Do not hallucinate. If unsure, state that directly."
    )
    citations: Optional[List[CitationFinalAnwser]] = Field(
        description="A list of supporting citations, each are only the relavent and exact sentence(s) or phrase(s) "
                   "from the retrieved document chunk that directly supports the answer. Must be verbatim from the chunk. "
                   "Must NOT be the surrounding but irrelavent, or hallucinated ones."
    )
class LLMDebugResponse(BaseModel):
    steps: int
    details: Optional[list] = None
    
class RAGResponseWithCitations(BaseModel):
    agenticResponseDebug: Optional[LLMDebugResponse] = None
    answer: Optional[str] = Field(
        description="The answer to the user's question. Base entirely on retrieved sources. "
                   "Do not hallucinate. If unsure, state that directly."
    )
    citations: Optional[List[Citation]] = Field(
        description="A list of supporting citations, each are only the relavent and exact sentence(s) or phrase(s) "
                   "from the retrieved document chunk that directly supports the answer. Must be verbatim from the chunk. "
                   "Must NOT be the surrounding but irrelavent, or hallucinated ones."
    )
    status: Optional[ChatMessageStatus]

    traceId: Optional[str] = None