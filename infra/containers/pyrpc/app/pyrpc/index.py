from typing import List
import logging
import asyncio
import json
import os
import traceback
from datetime import datetime

from ai_sdk import formatter
from ai_sdk.types import ConversationIncomintMessage, ConversationMessageUser, LanguageModelV1Source
from data_mode import DataMode
from fastapi import FastAPI, Request
from fastapi.encoders import jsonable_encoder
from fastapi.responses import StreamingResponse, JSONResponse, PlainTextResponse
from pydantic import BaseModel, ValidationError
from pyrpc.rag_response import generate_ddq_response
from pyrpc import agentic_response_langgraph
from pyrpc.context_based_doc_chunking import chunking_pipeline
from pyrpc.rag_response import step_5b_generate_answer
from pyrpc.rag_response.new_llm import ModelHyperparameters
from pyrpc.rag_response.step_1_classify_input import classify_input
from pyrpc.rag_response.step_2a_conversation import UserInput, conversation
from pyrpc.utils.aws_secret import get_secret_value
from pyrpc.rag_response.generate_ddq_response import QuestionInput
from pyrpc.utils.tracing import pipeline_tracing

logging.basicConfig(level=logging.DEBUG)

app = FastAPI()


async def stream_text(latestMessage: ConversationMessageUser):
    yield formatter.text(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}|")
    yield formatter.reasoning(f"pause|")
    await asyncio.sleep(1)
    yield formatter.text(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}|")
    yield formatter.reasoning("I just sleep for 1 second")
    yield formatter.source(LanguageModelV1Source(
        sourceType='url',
        id='src-123',
        url='https://example.com/article',
        title='Example Article',
        providerMetadata={
            "google": {
                "position": 1,
                "snippet": "Top result from Google Search"
            },
            "bing": {
                "confidence": 0.95
            }
        }
    ))
    yield formatter.text(f"Echo: {latestMessage.parts[0].text}|")
    await asyncio.sleep(1)
    yield formatter.text(f"Any question?")


@app.post('/pyrpc/squared')
async def handle_chat_data(request: ConversationIncomintMessage):
    response = StreamingResponse(stream_text(request.latestMessage))
    response.headers['x-vercel-ai-data-stream'] = 'v1'
    response.headers['x-vercel-real-path'] = '/pyrpc/squared'
    return response


@app.route('/pyrpc/time')
async def time(request: Request):
    async def g():
        yield datetime.now().strftime('%Y-%m-%d %H:%M:%S\n')
        await asyncio.sleep(1)
        yield datetime.now().strftime('%Y-%m-%d %H:%M:%S\n')
        await asyncio.sleep(1)
        yield datetime.now().strftime('%Y-%m-%d %H:%M:%S\n\n')

    return StreamingResponse(g())


@app.route('/pyrpc/time2')
async def time2(request: Request):
    from pyrpc.rag_response.time import now_string
    return PlainTextResponse(now_string())


@app.route("/{path:path}")
async def catch_all_1a(request: Request):
    return JSONResponse({"request_method": request.method, "path": request.path_params})


class Context(BaseModel):
    institutionName: str


class GenerateAnswerRequest(BaseModel):
    contextualizedQuestion: str
    citations: List[dict]
    context: Context


class ConversationRequest(BaseModel):
    input: UserInput
    context: Context


async def model_dump_json(stream):
    try:
        async for chunk in stream:
            yield chunk.model_dump_json(warnings="warn") + "\n"
    except Exception as e:
        logging.error(f"Error while dumping model to JSON: {e}")

    # TODO pydantic validation the request


@app.post('/pyrpc/generate-answer/agentic-response')
async def generate_answer_endpoint(request: Request):
    authorization = request.headers.get("authorization")
    api_secret = os.getenv("CHUNKING_API_SECRET") or get_secret_value("CHUNKING_API_SECRET")
    if authorization != f"Bearer {api_secret}":
        return JSONResponse(content={"error": "Unauthorized"}, status_code=401)

    try:
        json_request = await request.json()
        user_message = json_request["user_message"]
        context = json_request["context"]
        model_params = json_request["modelParameters"]
        tag_ids = json_request["tagIds"]
        fund_ids = json_request["fundIds"]
        conversation_id = json_request["conversationId"]
        chat_message_id = json_request["chatMessageId"]
        session_id = context.get("sessionId", None)
        trace_id = request.headers.get("x-trace-id")
        model_hyperparams = ModelHyperparameters(**model_params)
        with pipeline_tracing.continue_trace(trace_id=trace_id, session_id=session_id):
            response = agentic_response_langgraph.generate_answer(user_message, context, tag_ids=tag_ids, fund_ids=fund_ids,
                                                              model_hyperparams=model_hyperparams, chat_message_id=chat_message_id, conversation_id=conversation_id)
            return StreamingResponse(model_dump_json(response))
    except ValidationError as e:
        return JSONResponse(content=jsonable_encoder(e.errors()), status_code=422)
    except Exception as e:
        traceback_str = traceback.format_exc()
        return JSONResponse(content={"error": str(e), "traceback": traceback_str}, status_code=500)


# TODO pydantic validation the request
@app.post('/pyrpc/generate-answer/step_5b_generate_answer')
async def generate_answer_endpoint(request: Request):
    authorization = request.headers.get("authorization")
    api_secret = os.getenv("CHUNKING_API_SECRET") or get_secret_value("CHUNKING_API_SECRET")
    if authorization != f"Bearer {api_secret}":
        return JSONResponse(content={"error": "Unauthorized"}, status_code=401)

    try:
        json_request = await request.json()
        contextualized_question = json_request["contextualizedQuestion"]
        jumbo_chunks = json_request["jumbo_chunks"]
        citations_data = json_request["citations"]
        context = json_request["context"]
        institution_name = context["institutionName"]
        session_id = context.get("sessionId", None)
        trace_id = request.headers.get("x-trace-id")
        chat_message_id = json_request.get("chatMessageId", None)

        # Get the url parameter selecting which data to use
        qp = request.query_params
        data_mode = DataMode(int(qp.get("data_mode")))

        logging.debug(f'Data mode is {data_mode}')

        if data_mode is None:
            logging.error('Must send data selector')
            raise Exception("Must send data selector")

        if data_mode == DataMode.CITATIONS:
            _data = citations_data
        elif data_mode == DataMode.JUMBO_CHUNKS:
            _data = jumbo_chunks
        else:
            logging.error(f'Unknown data mode {data_mode}')
            raise Exception(f'Unknown data mode {data_mode}')

        logging.debug(f'\nUsing {DataMode(data_mode)} data:\n{_data}')

        # Append citations with the file name (which may contains "context" information, e.g. fund name)
        _data = json.loads(_data)
        if data_mode == DataMode.CITATIONS:
            for citation in _data:
                citation["quote"] = citation['file_name'] + \
                    ": " + citation["quote"]

        logging.debug(f'\nData:\n{_data}')

        model_params = json_request["modelParameters"]
        model_hyperparams = ModelHyperparameters(**model_params)
        with pipeline_tracing.continue_trace(trace_id=trace_id, session_id=session_id):
            response = step_5b_generate_answer.generate_answer(contextualized_question=contextualized_question,
                                                               data_mode=data_mode,
                                                               data=_data,
                                                               institution_name=institution_name,
                                                               model_hyperparams=model_hyperparams,
                                                               chat_message_id=chat_message_id)
            return StreamingResponse(response)
    except ValidationError as e:
        return JSONResponse(content=jsonable_encoder(e.errors()), status_code=422)


@app.post('/pyrpc/generate-answer/generate_ddq_responses')
async def generate_ddq_responses_endpoint(request: Request):
    authorization = request.headers.get("authorization")
    api_secret = os.getenv("CHUNKING_API_SECRET") or get_secret_value("CHUNKING_API_SECRET")
    if authorization != f"Bearer {api_secret}":
        return JSONResponse(content={"error": "Unauthorized"}, status_code=401)

    try:
        json_request = await request.json()

        logging.debug(f'\nRequest:\n{json_request}')

        question_array = json.loads(json_request['question_array'])
        context = json_request["context"]
        institution_name = context["institutionName"]
        session_id = context.get("sessionId", None)
        trace_id = request.headers.get("x-trace-id")

        # Get the url parameter selecting which data to use
        qp = request.query_params
        data_mode = DataMode(int(qp.get("data_mode")))

        logging.debug(f'Data mode is {data_mode}')

        if data_mode is None:
            logging.error('Must send data selector')
            raise Exception("Must send data selector")

        logging.debug(f'\nUsing {DataMode(data_mode)} data:\n{data_mode}')

        # Append citations with the file name (which may contains "context" information, e.g. fund name)
        for question in question_array:
            logging.debug(f'\nQuestion:\n{question}')

        _data = json.loads(json_request["jumbo_chunks"])
        if data_mode == DataMode.CITATIONS:
            for citation in _data:
                citation["quote"] = citation['file_name'] + \
                    ": " + citation["quote"]

            # print(f'\nData:\n{_data}')

        question_payload = [
            QuestionInput(
                question_id=question["question_id"],
                question_type=question["question_type"],
                answer_template=question["answer_template"],
                contextualized_question=question["contextualized_question"],
                # data = json.dumps(_data)
            )
            for question in question_array]

        model_params = json_request["modelParameters"]
        model_hyperparams = ModelHyperparameters(**model_params)

        logging.debug(f"\nQuestion payload:\n{question_payload}")

        with pipeline_tracing.continue_trace(trace_id=trace_id, session_id=session_id):
            response = generate_ddq_response.generate_ddq_responses(
                question_inputs=question_payload,
                data_mode=data_mode,
                data=json.dumps(_data),
                model_hyperparams=model_hyperparams,
                institution_name=institution_name)

            print(f"\nResponse:\n{response}")

            # Convert the response to a JSON string before returning
            return JSONResponse(content=[r.model_dump() for r in response])
    except ValidationError as e:
        return JSONResponse(content=jsonable_encoder(e.errors()), status_code=422)


@app.post("/pyrpc/vectorize/generate-chunks")
async def generate_doc_chunks(request: Request):

    authorization = request.headers.get("authorization")
    api_secret = os.getenv("CHUNKING_API_SECRET") or get_secret_value("CHUNKING_API_SECRET")
    if authorization != f"Bearer {api_secret}":
        return JSONResponse(content={"error": "Unauthorized"}, status_code=401)

    data = await request.json()
    message = data["input"]

    logging.info(f'Sending async chunk generation request for {message}')
    # await chunking_pipeline.process(doc_id=message)
    chunking_pipeline.worker(doc_id=message)
    logging.info(f'Done sending chunk generation request for {message}')

    return JSONResponse(content={"chunking": "processing"}, status_code=200)


# TODO pydantic validation the request
@app.post("/pyrpc/generate-answer/step_1_classify_input")
async def classify_input_endpoint(request: Request):
    authorization = request.headers.get("authorization")
    api_secret = os.getenv("CHUNKING_API_SECRET") or get_secret_value("CHUNKING_API_SECRET")
    if authorization != f"Bearer {api_secret}":
        return JSONResponse(content={"error": "Unauthorized"}, status_code=401)

    try:
        data = await request.json()
        message = data["input"]
        context = {"institutionName": data["institution_name"]}
        session_id = data.get("sessionId", None)
        trace_id = request.headers.get("x-trace-id")
        chat_message_id = data.get("chatMessageId", None)

        model_params = data["modelParameters"]
        model_hyperparams = ModelHyperparameters(**model_params)
        with pipeline_tracing.continue_trace(trace_id=trace_id, session_id=session_id):
            classification = await classify_input(message, context, model_hyperparams=model_hyperparams, chat_message_id=chat_message_id)
            return classification
    except ValidationError as e:
        return JSONResponse(content=jsonable_encoder(e.errors()), status_code=422)


@app.post('/pyrpc/generate-answer/step_2a_conversation')
async def conversation_endpoint(request: Request):
    authorization = request.headers.get("authorization")
    api_secret = os.getenv("CHUNKING_API_SECRET") or get_secret_value("CHUNKING_API_SECRET")
    if authorization != f"Bearer {api_secret}":
        return JSONResponse(content={"error": "Unauthorized"}, status_code=401)

    try:
        json_request = await request.json()

        logging.debug(json_request)

        input_data = json_request["input"]
        context = json_request["context"]
        institution_name = context["institutionName"]
        session_id = context.get("sessionId", None)
        trace_id = request.headers.get("x-trace-id")
        chat_message_id = json_request.get("chatMessageId", None)


        model_params = json_request["modelParameters"]
        model_hyperparams = ModelHyperparameters(**model_params)

        with pipeline_tracing.continue_trace(trace_id=trace_id, session_id=session_id):
            response = conversation(input=UserInput(**input_data),
                                    institution_name=institution_name,
                                    model_hyperparams=model_hyperparams,
                                    chat_message_id=chat_message_id)

            return PlainTextResponse(response, media_type='text/plain')
    except ValidationError as e:
        return JSONResponse(content=jsonable_encoder(e.errors()), status_code=422)
