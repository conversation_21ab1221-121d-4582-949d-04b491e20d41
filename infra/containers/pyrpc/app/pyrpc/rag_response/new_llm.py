from enum import Enum
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain_google_genai import ChatGoogleGenerativeAI

import os

from pydantic import BaseModel, Field

from pyrpc.utils.aws_secret import get_secret_value


class ChatModel(str, Enum):
    GPT_41_NANO = "gpt-4.1-nano"
    GPT_4O = "gpt-4o"
    GPT_4O_MINI = "gpt-4o-mini"
    GPT_O3_MINI = "o3-mini"
    CLAUDE_37_LATEST = "claude-3-7-sonnet-latest"
    GEMINI_25_FLASH = "gemini-2.5-flash-preview-05-20"
    GEMINI_25_PRO = "gemini-2.5-pro-preview-05-06"

class ResponseStyle():
    NORMAL = "NORMAL"
    CONCISE = "CONCISE"
    DETAILED = "DETAILED"
    OFFICIAL = "OFFICIAL"

class ModelHyperparameters(BaseModel):
    model: str = Field(default=ChatModel.GPT_O3_MINI)
    responseStyle: str = Field(default=ResponseStyle.NORMAL)
    temperature: float = Field(default=0.0)
    top_p: float = Field(default=1e-9, ge=1e-9, le=1.0)  # Range: 1e-9 to 1
    thinking_mode: bool = Field(default=False)
    agentic_mode_langgraph: bool = Field(default=False)
    citation_verification_mode: bool = Field(default=False)
    answer_reflexion_mode: bool = Field(default=False)

def new_llm(model_hyperparams: ModelHyperparameters = ModelHyperparameters(), streaming: bool=False,):
    if model_hyperparams.model == ChatModel.GPT_4O_MINI or model_hyperparams.model == ChatModel.GPT_4O or model_hyperparams.model == ChatModel.GPT_41_NANO:
        return new_openai_llm(
            model=model_hyperparams.model,
            streaming=streaming,
            temperature=model_hyperparams.temperature,
            top_p=model_hyperparams.top_p
        )

    if model_hyperparams.model == ChatModel.GPT_O3_MINI:
        return new_openai_llm(
            model=model_hyperparams.model,
            streaming=streaming,
            top_p=model_hyperparams.top_p
        )
    if model_hyperparams.model == ChatModel.CLAUDE_37_LATEST:
        return new_anthropic_llm(
            model=model_hyperparams.model,
            streaming=streaming,
            temperature=model_hyperparams.temperature,
            top_p=model_hyperparams.top_p
        )
    if model_hyperparams.model == ChatModel.GEMINI_25_FLASH:
        return new_gemini_llm(
            model=model_hyperparams.model,
            streaming=streaming,
            temperature=model_hyperparams.temperature,
            top_p=model_hyperparams.top_p
        )
        
    if model_hyperparams.model == ChatModel.GEMINI_25_PRO:
        return new_gemini_llm(
            model=model_hyperparams.model,
            streaming=streaming,
            temperature=model_hyperparams.temperature,
            top_p=model_hyperparams.top_p
        )
    raise ValueError(f"Unsupported model: {model_hyperparams.model}")

def new_openai_llm(model="gpt-4.1-nano", streaming: bool=False, temperature: float=0.7, top_p: float=1e-9):
    return ChatOpenAI(
        model=model,
        api_key=os.getenv("OPENAI_API_KEY") or get_secret_value("OPENAI_API_KEY"),
        # temperature=temperature,
        # top_p=top_p,
        streaming=streaming,
    )

def new_anthropic_llm(model, streaming: bool, temperature: float=0.7, top_p: float=1e-9):
    return ChatAnthropic(
        model=model,
        api_key=os.getenv("ANTHROPIC_API_KEY") or get_secret_value("ANTHROPIC_API_KEY"),
        temperature=temperature,
        top_p=top_p,
        streaming=streaming,
        max_tokens_to_sample=64000
    )

def new_gemini_llm(model, streaming: bool, temperature: float=0.7, top_p: float=1e-9):
    return ChatGoogleGenerativeAI(
        model=model,
        api_key=os.getenv("GEMINI_API_KEY") or get_secret_value("GEMINI_API_KEY")   ,
        temperature=temperature,
        top_p=top_p,
        streaming=streaming,

    )
