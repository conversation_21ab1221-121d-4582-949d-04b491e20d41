import logging
import json
import time
from typing import <PERSON><PERSON><PERSON><PERSON><PERSON>, Generator

from pydantic import BaseModel, Field
from langchain.prompts import Chat<PERSON>romptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate, \
    MessagesPlaceholder
from langchain_core.messages import BaseMessage
from langchain_openai import ChatOpenAI

from pyrpc.rag_response.persona_prompt_string import persona_prompt_string
from pyrpc.rag_response.new_llm import new_llm, ModelHyperparameters
from pyrpc.utils.tracing import pipeline_tracing

logging.basicConfig(level=logging.INFO)


class UserInput(BaseModel):
    message: str
    messageHistory: list


class ConversationResponse(BaseModel):
    response: str


conversation_prompt = f"""
{persona_prompt_string}
Engage naturally with the user's statements while maintaining context from previous messages.
Be concise and conversational. Don't reference any external documents or sources.
If the user's statement implies a question or requires information lookup, 
encourage them to ask it as a direct question.
"""


@pipeline_tracing.observe_step(name="step_2a_conversation")
def conversation(
        input: UserInput,
        institution_name: str,
        model_hyperparams: ModelHyperparameters = None,
        chat_message_id: str = None,
) -> Generator[BaseMessage, None, None]:
    logging.info('Conversation ...')

    try:
        llm = new_llm(streaming=False, model_hyperparams=model_hyperparams)

        prompt = ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(conversation_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            HumanMessagePromptTemplate.from_template("{input}"),
        ])

        callback = pipeline_tracing.get_handler()
        response = prompt.pipe(llm).invoke({
            "input": input.message,
            "institution_name": institution_name,
            "chat_history": input.messageHistory
        }, config={
            "callbacks": [callback] if callback else []
        })

        logging.info('Conversation done.')
        return response.content
    except Exception as e:
        logging.error(f"Error in conversation: {str(e)}")
        raise
