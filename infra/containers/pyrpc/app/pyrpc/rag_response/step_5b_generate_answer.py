import logging
import json
from typing import Generator

from data_mode import DataMode
from langchain.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain_core.messages import BaseMessage
from pyrpc.contradiction_detection.contradiction_classifier import ContradictionClassifier
from pyrpc.models import LLMResponse
from pyrpc.rag_response.new_llm import new_llm, ModelHyperparameters, ResponseStyle
from pyrpc.rag_response.persona_prompt_string import persona_prompt_string
from pyrpc.utils.tracing import pipeline_tracing

logging.basicConfig(level=logging.INFO)

# Map of response style to prompt
response_style_prompt_map = {
    ResponseStyle.NORMAL: "",
    ResponseStyle.CONCISE: "Answer in a concise manner, without any additional commentary. Limit your response to a single paragraph.",
    ResponseStyle.DETAILED: "Answer in a detailed manner, with all the information from the excerpts.",
    ResponseStyle.OFFICIAL: "Answer in a formal manner, with all the information from the excerpts.",
}

tool_prompt = f""" 
{persona_prompt_string} 
You are given a user question and a list of document excerpts.
Your task is to answer the user question based on the provided document excerpts. 
You must answer your query **only using the retrieved content**, and format your answer for use in investor reports.

Use **Markdown** formatting wherever possible, including tables.  
Ensure that Markdown tables include a **newline between the header and each table row**.

If the content includes **names or personnel**, be **verbose** and include **full details**.

Avoid using phrases such as:
- "Based on the information provided"
- "Based on the document excerpts"

---

When your query involves **time series or itemized lists**, follow these rules:

---

### 1. ✅ Known Exhaustive (List or Time Series)

If the source **explicitly defines a total count or time range** (e.g., "Arclight Capital manages 4 funds" or "Quarterly revenue from Q1 2022 to Q4 2023"), you must:

- Display **all expected entries**.
- Use **"Unnamed item"** or **"No data available"** where information is missing.
- **Do not fabricate names or attributes** not present in the source.

#### 📄 List Example:

> "Arclight Capital manages 4 funds."  
> Only 2 are named: ArcGrowth and ArcIncome. AUM is only provided for ArcGrowth.

| Fund Name     | AUM               |
|---------------|-------------------|
| ArcGrowth     | $100M             |
| ArcIncome     | No data available |
| Unnamed fund  | No data available |
| Unnamed fund  | No data available |

_Note: Arclight Capital manages 4 funds. Only 2 are named in the retrieved content._

#### 🕒 Time Series Example:

> "Quarterly revenue from Q1 2022 to Q4 2023:"

| Quarter   | Revenue        |
|-----------|----------------|
| Q1 2022   | $12M           |
| Q2 2022   | No data available |
| Q3 2022   | $14M           |
| Q4 2022   | $13.5M         |
| Q1 2023   | $16M           |
| Q2 2023   | No data available |
| Q3 2023   | $17.2M         |
| Q4 2023   | $18M           |

---

### 2. ❌ Unknown Exhaustiveness

If the source **mentions individual items** (e.g., funds or time points) but does **not say how many exist in total**, treat the list as **ambiguously incomplete**.

- Show only retrieved items.
- Do not infer or fabricate missing names or totals.
- Use **"No data available"** only for attributes that were expected but missing.
- Clarify that coverage is **unknown**.

#### 📄 List Example:

> "Calder Equity is managed by Calder Investments. Calder Growth is managed by Calder Investments."

| Fund Name       | AUM               |
|------------------|-------------------|
| Calder Equity    | No data available |
| Calder Growth    | No data available |

_Note: It is unclear whether Calder Investments manages additional funds or what their AUMs are._

#### 🕒 Time Series Example:

> "Revenue was reported for Q1 and Q3 2023."

| Quarter   | Revenue        |
|-----------|----------------|
| Q1 2023   | $11M           |
| Q3 2023   | $13M           |

_Note: Coverage is unclear — other quarters may exist but are not shown._

---

### 3. ⚠️ Explicitly Non-Exhaustive

If the source language **implies the list is illustrative and not complete**, treat it as **intentionally partial**.

You may determine non-exhaustiveness if the source uses any of the following:
- "such as"
- "including"
- "among others"
- "for example"
- "e.g."
- "select funds"
- "sample list"
- "not limited to"

You may also infer partiality based on context — even if such phrases are not present — when it is clear that the source is **providing examples, not a complete list**.

- Show only what is named.
- Clearly state that the list is **non-exhaustive**.

#### 📄 List Example:

> "Northwave Capital manages funds such as Northwave Balanced and Northwave ESG."

| Fund Name         | AUM     |
|--------------------|---------|
| Northwave Balanced | $95M    |
| Northwave ESG      | $88M    |

_Note: This is a non-exhaustive list — other funds may exist._

#### 🕒 Time Series Example:

> "Performance highlights include Q2 and Q4 2023."

| Quarter   | Revenue        |
|-----------|----------------|
| Q2 2023   | $14.1M         |
| Q4 2023   | $15.5M         |

_Note: Only select quarters are shown. Full range not provided._


Use Markdown whenever possible, including tables.

{{response_style}}

The document excerpts are provided as follow:
{{excerpts}}
"""

DELIM = "__@__"


def is_contradiction(text, answer) -> bool:
    cc = ContradictionClassifier()
    return cc.pairwise_contradiction_classifier(text, answer)


@pipeline_tracing.observe_step(name="step_5b_generate_answer")
def generate_answer(
        contextualized_question: str,
        data_mode: DataMode,
        data: str,
        institution_name: str,
        max_retries: int = 3,
        retries: int = None,
        model_hyperparams: ModelHyperparameters = ModelHyperparameters(),
        chat_message_id: str = None,
) -> Generator[BaseMessage, None, None]:
    logging.info('Generate answer ...')

    try:
        if retries is None:
            retries = max_retries

        if retries <= 0:
            yield LLMResponse(
                answer=f"There were errors trying to answer this query after {retries} auto retries.",
                excerpts_with_source_ids_and_quotes=[],
            )
            return

        llm = new_llm(streaming=True, model_hyperparams=model_hyperparams)

        prompt = ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(tool_prompt),
            HumanMessagePromptTemplate.from_template("{input}"),
        ])

        if data_mode == DataMode.JUMBO_CHUNKS:
            # Need to remove the doc context.
            _content_records = [json.loads(d['pageContent'])[
                'content'] for d in data]
            _content_records = [': '.join(c.split(DELIM))
                                for c in _content_records]
            _content_records = [c.replace('\n', ' ') for c in _content_records]
            data = '\n\n'.join(_content_records)

        logging.debug(f"Generating answer from:\n {data} ")

        callback = pipeline_tracing.get_handler()
        response = prompt.pipe(llm).stream({
            "input": contextualized_question,
            "institution_name": institution_name,
            "response_style": response_style_prompt_map[model_hyperparams.responseStyle],
            "excerpts": data,
        }, config={
            "callbacks": [callback] if callback else []
        })

        logging.info(f"Answer response generated.")

        streamed_content = ""
        for chunk in response:
            streamed_content += chunk.content
            yield chunk.content

        logging.info(f"Answer response returned.")
        return streamed_content
    except Exception as e:
        logging.error(f"Error in generate_answer: {str(e)}")
        raise
