from pydantic import BaseModel, Field
from langchain.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain_openai import ChatOpenAI
from typing import Dict

from pyrpc.rag_response.persona_prompt_string import persona_prompt_string
from pyrpc.rag_response.new_llm import new_llm, ModelHyperparameters
from pyrpc.utils.tracing import pipeline_tracing
import logging

logging.basicConfig(level=logging.INFO)

class ClassifiedInput(BaseModel):
    type: str = Field(description="The type of user input")
    explanation: str = Field(description="Brief explanation of why this classification was chosen")

classify_input_prompt = ChatPromptTemplate.from_messages([
    SystemMessagePromptTemplate.from_template(f"""
${persona_prompt_string}
Your task is to classify whether a user input is one of the following:

- "work" — for Work-related Inquiry: Messages that seek factual information, clarification, or assistance, typically related to tasks, tools, processes, or professional topics. Includes explicit (e.g., "How do I…?") and implicit inquiries (e.g., "I'd like to know more about…" or "Describe…").
- "casual" — for Casual Statement: Conversational messages that do not seek factual answers. Includes greetings, small talk, humor, personal updates, or expressions of opinion (e.g., "What's up?", "I'm doing fine", "That's funny").
- "unclear" — for Ambiguous/Unclear: Messages that are vague, incomplete, or not clearly classifiable as either work-related or casual (e.g., "Interesting." or "I'm not sure").
"""),
    HumanMessagePromptTemplate.from_template("{input}"),
])

@pipeline_tracing.observe_step(name="step_1_classify_input")
async def classify_input(message: str,
                           context: Dict[str, str],
                           model_hyperparams: ModelHyperparameters = ModelHyperparameters(),
                           chat_message_id: str = None) -> ClassifiedInput:
    logging.info('Classify input ...')
    
    try:
        llm = new_llm(streaming=False, model_hyperparams=model_hyperparams)

        classify_input_chain = classify_input_prompt.pipe(
            llm.with_structured_output(ClassifiedInput)
        )

        callback = pipeline_tracing.get_handler()
        classification = classify_input_chain.invoke({
            "input": message,
            "institution_name": context["institutionName"],
        }, config={
            "callbacks": [callback] if callback else []
        })

        logging.info('Classify input done.')
        return classification
    except Exception as e:
        logging.error(f"Error in classify_input: {str(e)}")
        raise
