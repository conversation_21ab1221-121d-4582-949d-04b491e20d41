from asyncio import sleep
from datetime import datetime
from enum import Enum
import json
import logging
import time
from typing import AsyncGenerator, Generator, List, Dict, Any, Literal, Optional
from langchain.callbacks import StdOutCallbackHandler

import os
import requests

from pydantic import BaseModel, Field
from langchain.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain_core.messages import BaseMessage
from langchain_core.tools import Tool
from langchain.tools import StructuredTool

from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, ToolMessage
from langchain_core.utils.json import parse_partial_json

from langchain_core.messages.ai import add_ai_message_chunks, AIMessageChunk

from pyrpc.rag_response.new_llm import ModelHyperparameters, new_llm, new_anthropic_llm, new_gemini_llm
from pyrpc.utils.base_url import get_base_url

class CitationResponse(BaseModel):
    source_id: int = Field(description="The integer ID of a SPECIFIC source which justifies the answer.")
    quote: str = Field(description="The VERBATIM quote from the specified source that justifies the answer.")
    named_entity_mentioned: bool = Field(description="Whether the quote mentions the named entity from the question.")
    file_name: str = Field(description="The name of the file containing the citation.")


class LLMResponse(BaseModel):
    answer: str = Field(description="The answer to the user question.")


class RetrievalRequest(BaseModel):
    query: str = Field(description="The search query to retrieve relevant documents")
    limit: Optional[int] = Field(description="Maximum number of documents to retrieve")


class RetrievalResponse(BaseModel):
    chunks: List[Dict[str, Any]] = Field(description="The retrieved document chunks")


tool_prompt = f""" 
You are a resourceful financial analyst, and you work for {{institution_name}}.

You are given a user question and a list of document citations.
Your task is to answer the user question based on the provided document citations. 

Your answers will be used to generate a report that will be read by investors, so format them as such. Use Markdown whenever possible, including tables.
Pay close attention to the markdown table structure. Ensure there is a new line between the header and each table row.
If the source material or answer contain names or personnel, be verbose and include all personnel information.

Do not use phrases such as "Based on the information provided" or "Based on the document citations".

"""

from typing import List, Optional
from pydantic import BaseModel
from pyrpc.utils.tracing import Tracing

tracing = Tracing()
class DocumentMetadata(BaseModel):
    languages: List[Literal["eng"]]  # You can use Literal["eng"] if you want to restrict
    page_number: Optional[int] = None
    page_name: Optional[str] = None
    text_as_html: Optional[str] = None
    image_base64: Optional[str] = None
    source: Optional[str] = None
    filetype: str


class Citation(BaseModel):
    sourceId: int
    quote: str
    fileName: str = Field(alias="filename")
    metadata: DocumentMetadata


class DocumentChunkMetadata(BaseModel):
    id: str


class DocumentChunkWithMetadata(BaseModel):
    pageContent: str
    metadata: DocumentChunkMetadata
    fileName: str = Field(alias="filename")


class ChatMessageStatus(str, Enum):
    RETRIEVING = "RETRIEVING"
    GENERATING_CITATIONS = "GENERATING_CITATIONS"
    VALIDATING_CITATIONS = "VALIDATING_CITATIONS"
    GENERATING_ANSWER = "GENERATING_ANSWER"
    RETRY = "RETRY"
    ERROR = "ERROR"
    READY = "READY"


class RAGResponseWithCitations(BaseModel):
    citations: Optional[List["Citation"]] = Field(
        description="A list of supporting citations, each are only the relavent and exact sentence(s) or phrase(s) from the retrieved document chunk that directly supports the answer. Must be verbatim from the chunk. Must NOT be the surrounding but irrelavent, or hallucinated ones."
    )
    status: Optional[ChatMessageStatus]
    answer: Optional[str] = Field(
        description="The answer to the user's question. Base entirely on retrieved sources. Do not hallucinate. If unsure, state that directly."
    )


async def astream_with_final_none(stream):
    async for chunk in stream:
        yield chunk
    yield None


async def generate_answer(
        user_message: str,
        org_info: dict,
        max_retries: int = 3,
        retries: int = None,
        chat_message_id: str = None,
        model_hyperparams: ModelHyperparameters = ModelHyperparameters(),
) -> AsyncGenerator[BaseMessage, None]:
    with tracing.get_client().start_as_current_span("agentic_response") as span:
        def retrieve_documents(query: str, limit: int = 5) -> List[Dict[str, Any]]:
            """
            Placeholder function for document retrieval.
            """
            url = f"{get_base_url()}/api/retrieve-by-similarities"

            params = {
                "query": query,
                "options": {
                    "orgId": org_info["orgId"],
                    # "openDocumentId": openDocumentId,
                    # "worksheet": worksheet,
                    "tagIds": [],
                    "fundIds": [],
                }
            }
            logging.debug(f"Calling: {url} with {json.dumps(params)}")
            response = requests.post(url, json=params)
            response.raise_for_status()
            response_text = response.text
            return response_text

        if retries is None:
            retries = max_retries

        if retries <= 0:
            yield RAGResponseWithCitations(
                answer=f"There were errors trying to answer this query after {retries} auto retries.",
                citations=[],
                status="ERROR"
            )
            return

        # Define the retrieval tool
        retrieval_tool = StructuredTool.from_function(
            func=retrieve_documents,
            name="retrieve_documents",
            description="Retrieve relevant document chunks based on a search query",
            args_schema=RetrievalRequest,
            return_direct=True
        )

        final_answer_tool = StructuredTool.from_function(
            func=lambda x: x,
            name="final_answer",
            description="Invoke this tool to give the final answer",
            args_schema=RAGResponseWithCitations,
            return_direct=True
        )

        # Get the LLM with tools enabled
        llm_without_tool = new_llm(streaming=True, model_hyperparams=model_hyperparams)
        tool_list = [retrieval_tool]
        tool_dict = {tool.name: tool for tool in tool_list}
        llm_with_tool = llm_without_tool.bind_tools(tool_list + [final_answer_tool])

        while user_message.startswith("test_streaming"):
            yield RAGResponseWithCitations(answer="current timestamp is " + datetime.now().timestamp().__str__() + "\n",
                                        citations=[], status=ChatMessageStatus.GENERATING_ANSWER)
            await sleep(10 ** -float(user_message.split(" ")[-1]))

        s = SystemMessagePromptTemplate.from_template(tool_prompt).format_messages(
            institution_name=org_info["institutionName"])[0]
        messages = [
            s,
            HumanMessage(user_message),
        ]
        while not isinstance(messages[-1], AIMessage):
            gathered = None

            logging.debug("Streaming response")
            tool_results = []
            async for chunk in astream_with_final_none(llm_with_tool.astream(messages)):
                # chunk.
                gathered = chunk if not gathered else add_ai_message_chunks(gathered, chunk) if chunk else gathered
                is_done = chunk is None
                status = ("READY" if is_done else ChatMessageStatus.GENERATING_ANSWER)
                if gathered.tool_call_chunks:
                    if gathered.tool_call_chunks[-1]["name"] == "final_answer":
                        args = gathered.tool_call_chunks[-1]["args"]
                        if args:
                            # Always stream partials
                            try:
                                if is_done:
                                    yield RAGResponseWithCitations(
                                        **parse_partial_json(gathered.tool_call_chunks[-1]["args"]))
                                    return
                                else:
                                    yield RAGResponseWithCitations(
                                        answer=parse_partial_json(gathered.tool_call_chunks[-1]["args"]).get("answer", ""),
                                        citations=[], status=status)
                            except Exception as e:
                                logging.critical(f"Error in final answer parsing: {e} | {args}")
                                yield RAGResponseWithCitations(
                                    answer=f"There were errors trying to answer this query after {retries} auto retries.",
                                    citations=[],
                                    status="ERROR"
                                )
                                return
                            continue

                    while (len(gathered.tool_call_chunks) > 1) or (is_done and len(gathered.tool_call_chunks) > 0):
                        tool_call = gathered.tool_call_chunks.pop()
                        tool_call_id = tool_call["id"]
                        tool_call_name = tool_call["name"]
                        tool_call_args = json.loads(tool_call["args"])

                        if tool_call_name == "retrieve_documents":
                            tool_call_result = retrieve_documents(tool_call_args["query"], tool_call_args["limit"])
                            yield RAGResponseWithCitations(
                                answer=f'Thinking... looking for {json.dumps(tool_call_args["query"])}',
                                citations=[],
                                status="RETRIEVING"
                            )
                        else:
                            tool_call_result = tool_dict[tool_call_name].invoke(tool_call_args, callbacks=[tracing_handler])
                        tool_results.append(ToolMessage(content=tool_call_result, tool_call_id=tool_call_id))

                # Final chunk: content='' additional_kwargs={} response_metadata={'finish_reason': 'tool_calls', 'model_name': 'gpt-4o-mini-2024-07-18', 'system_fingerprint': 'fp_b376dfbbd5'} id='run-8f08a3cc-2007-484a-8eb4-fb95ca2598e0'
                elif gathered.content:
                    yield RAGResponseWithCitations(answer=gathered.content, citations=[], status=status)
            messages.append(gathered)
            if tool_results:
                messages.append(*tool_results)
        return

