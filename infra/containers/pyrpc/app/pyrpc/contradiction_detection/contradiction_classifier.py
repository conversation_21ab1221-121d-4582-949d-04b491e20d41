from concurrent.futures import ThreadPoolExecutor
from enum import Enum
from typing import List

import pandas as pd
from pydantic import BaseModel
from sklearn.metrics.pairwise import cosine_similarity

from pyrpc.context_based_doc_chunking.services.llm.openai_adapter import OpenAIAdapter
from pyrpc.context_based_doc_chunking.services.llm.voyageai_embedding_adapter import \
    VoyageAIAdapter


class ContradictionsModel(BaseModel):
    contradiction: str
    justification: str


class Topic(BaseModel):
    title: str
    summary: str


class TopicSplitModel(BaseModel):
    topics: List[Topic]


class ContradictionClassifier:
    class ContradictionClassification(Enum):
        NON_CONTRADICTORY = 0
        CONTRADICTORY = 1
        VERY_DIFFERENT = 2

    split_into_topics_prompt = """
        Given the following text, identify and list the main topics discussed within it. For each topic, provide a concise summary in one to two sentences.
        
        Text:
        {T}
        
        Respond in the following JSON format:
        {{
          "topics": [
            {{"title": "Topic Title 1", "summary": "Brief summary of topic 1."}},
            {{"title": "Topic Title 2", "summary": "Brief summary of topic 2."}},
            {{"title": "Topic Title 3", "summary": "Brief summary of topic 3."}}
          ]
        }}
        
        Ensure topics accurately reflect the primary points or subjects covered in the provided text.
    """

    check_contradictions_prompt = """
    You are given two texts:
        Text 1:
            {T1}
        
        Text 2:
            {T2}
        
        Your task is to determine whether these two texts contain contradictory information.
        Provide your answer clearly by selecting one of the following options:
        "1": If the texts contain statements or claims that logically cannot both be true simultaneously.
        "0": If the texts do not contain conflicting information or can logically coexist.
        Additionally, provide a brief justification explaining your reasoning.
        
        Your response should be formatted in JSON as follows:
        {{
            "contradiction": "1" or "0",
            "justification": "Your brief explanation here"
        }}
    """

    def __init__(self):
        self.embeder = VoyageAIAdapter()
        self.llm = OpenAIAdapter()

    def pairwise_contradiction_classifier(self, T1: str, T2: str):
        # TODO DORON 130525 Algo
        #                   1. Split both texts into main topics and short summaries  via llm
        #                   2. For each pair of topics ask LLM if they are contradictory or not
        #                   3. For each pair of topics compute embedding distance
        #                   4. Count the number of contradiction normalized by TBD N
        #                   5. Create an overall distance score across all embedded pairs
        #                   6. Weight the result in (4) against the result in (5) and score the "amount of contradiction"
        #                   7. Threshold the results into binary classification, return both score and class
        fulltext_contradictions = pd.DataFrame(index=[T1], columns=[T2])
        is_contradictory_ft = self.llm.generate(prompt=self.check_contradictions_prompt.format(T1=T1, T2=T2),
                                                response_model=ContradictionsModel)
        fulltext_contradictions.loc[T1, T2] = (int(is_contradictory_ft['contradiction']),
                                               is_contradictory_ft['justification'])

        fulltext_distance = 1.0 - cosine_similarity(self.embeder.embed(T1).reshape(1, -1),
                                                    self.embeder.embed(T2).reshape(1, -1)).flatten()[0]

        topics_T1 = {}
        topics_T2 = {}

        def process_topic_split(text, llm, prompt):
            return llm.generate(prompt=prompt.format(T=text), response_model=TopicSplitModel)

        def process_topic_pair(t1, t2, embeder, llm, prompt_template):
            t1_emb = embeder.embed(t1['title'])
            t2_emb = embeder.embed(t2['title'])
            topical_distance = (1.0 - cosine_similarity(t1_emb.reshape(1, -1), t2_emb.reshape(1, -1)).flatten()[0])

            is_contradictory_tp = llm.generate(prompt=prompt_template.format(T1=t1['title'], T2=t2['title']),
                                               response_model=ContradictionsModel)

            topical_contradiction = (int(is_contradictory_tp['contradiction']),
                                     is_contradictory_tp['justification'])

            return t1['title'], t2['title'], topical_contradiction, topical_distance

        with ThreadPoolExecutor(max_workers=2) as executor:
            futures = [executor.submit(process_topic_split, T1, self.llm, self.split_into_topics_prompt),
                       executor.submit(process_topic_split, T2, self.llm, self.split_into_topics_prompt)]
            _topics_T = [f.result() for f in futures]
            topics_T1 = _topics_T[0]
            topics_T2 = _topics_T[1]

        topical_contradictions = pd.DataFrame(index=[t['title'] for t in topics_T1['topics']],
                                              columns=[t['title'] for t in topics_T2['topics']])

        topical_distances = pd.DataFrame(index=[t['title'] for t in topics_T1['topics']],
                                         columns=[t['title'] for t in topics_T2['topics']])

        with ThreadPoolExecutor(max_workers=len(topics_T1['topics']) * len(topics_T2['topics'])) as executor:
            futures = []
            for t1 in topics_T1['topics']:
                for t2 in topics_T2['topics']:
                    futures.append(executor.submit(process_topic_pair,
                                                   t1,
                                                   t2,
                                                   self.embeder,
                                                   self.llm,
                                                   self.check_contradictions_prompt))

            _topic_pairs = [f.result() for f in futures]

            for _tp in _topic_pairs:
                t1_title, t2_title, topical_contradiction, topical_distance = _tp
                topical_contradictions.loc[t1_title, t2_title] = topical_contradiction
                topical_distances.loc[t1_title, t2_title] = topical_distance

        _classification = ContradictionClassifier.ContradictionClassification.NON_CONTRADICTORY
        if fulltext_contradictions.loc[T1, T2][0] == 1:
            _classification = ContradictionClassifier.ContradictionClassification.CONTRADICTORY
        elif topical_contradictions.map(lambda x: x[0]).mean().iloc[0] > 0.5:
            _classification = ContradictionClassifier.ContradictionClassification.CONTRADICTORY
        elif fulltext_distance > 0.9 and topical_distances.mean().iloc[0] > 0.9:
            _classification = ContradictionClassifier.ContradictionClassification.VERY_DIFFERENT

        return (_classification,
                topical_contradictions,
                topical_distances,
                fulltext_contradictions,
                fulltext_distance)


if __name__ == '__main__':
    c = ContradictionClassifier()
    (classification,
     topical_contradictions,
     topical_distances,
     fulltext_contradictions,
     fulltext_distance) = c.pairwise_contradiction_classifier("The Galatine EDITDA is 0.2x",
                                                              "The Galatine EDITDA is 0.8x")
