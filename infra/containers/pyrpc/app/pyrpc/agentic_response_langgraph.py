import json
import logging
from asyncio import sleep
from typing import Annotated, Async<PERSON>enerator, List
import requests
from asteval import <PERSON>preter
from langchain.prompts import SystemMessagePromptTemplate
from langchain_core.messages import AIMessage, BaseMessageChunk, HumanMessageChunk, SystemMessage, ToolMessage, ToolMessageChunk
from langchain_core.messages import BaseMessage
from langchain_core.messages import HumanMessage
from langchain_core.messages.ai import AIMessageChunk
from langchain_core.tools import tool
from langgraph.constants import END, START
from langgraph.graph import MessagesState, StateGraph
from langgraph.prebuilt import InjectedState
from langgraph.prebuilt import ToolNode

from pyrpc.models import ChatMessageStatus, LLMDebugResponse, RAGResponseWithCitations, Citation, DocumentMetadata, RAGResponseWithCitationsFinalAnwser
from pyrpc.rag_response.new_llm import ModelHyperparameters, new_llm
from pyrpc.utils.base_url import get_base_url
from pyrpc.utils.tracing import Tracing

tracing = Tracing()

tool_prompt = f""" 
You are a resourceful financial analyst, and you work for {{institution_name}}.

You are an autonomous Agentic AI system.  
You can issue your own search queries and retrieve relevant document chunks using **cosine similarity**.  
You must answer your query **only using the retrieved content**, and format your answer for use in investor reports.

Use **Markdown** formatting wherever possible, including tables.  
Ensure that Markdown tables include a **newline between the header and each table row**.

If the content includes **names or personnel**, be **verbose** and include **full details**.

Avoid using phrases such as:
- “Based on the information provided”
- “Based on the document citations”

Always use `final_answer_tool` to give the final answer.

---

When your query involves **time series or itemized lists**, follow these rules:

---

### 1. ✅ Known Exhaustive (List or Time Series)

If the source **explicitly defines a total count or time range** (e.g., “Arclight Capital manages 4 funds” or “Quarterly revenue from Q1 2022 to Q4 2023”), you must:

- Display **all expected entries**.
- Use **“Unnamed item”** or **“No data available”** where information is missing.
- **Do not fabricate names or attributes** not present in the source.

#### 📄 List Example:

> “Arclight Capital manages 4 funds.”  
> Only 2 are named: ArcGrowth and ArcIncome. AUM is only provided for ArcGrowth.

| Fund Name     | AUM               |
|---------------|-------------------|
| ArcGrowth     | $100M             |
| ArcIncome     | No data available |
| Unnamed fund  | No data available |
| Unnamed fund  | No data available |

_Note: Arclight Capital manages 4 funds. Only 2 are named in the retrieved content._

#### 🕒 Time Series Example:

> “Quarterly revenue from Q1 2022 to Q4 2023:”

| Quarter   | Revenue        |
|-----------|----------------|
| Q1 2022   | $12M           |
| Q2 2022   | No data available |
| Q3 2022   | $14M           |
| Q4 2022   | $13.5M         |
| Q1 2023   | $16M           |
| Q2 2023   | No data available |
| Q3 2023   | $17.2M         |
| Q4 2023   | $18M           |

---

### 2. ❌ Unknown Exhaustiveness

If the source **mentions individual items** (e.g., funds or time points) but does **not say how many exist in total**, treat the list as **ambiguously incomplete**.

- Show only retrieved items.
- Do not infer or fabricate missing names or totals.
- Use **“No data available”** only for attributes that were expected but missing.
- Clarify that coverage is **unknown**.

#### 📄 List Example:

> “Calder Equity is managed by Calder Investments. Calder Growth is managed by Calder Investments.”

| Fund Name       | AUM               |
|------------------|-------------------|
| Calder Equity    | No data available |
| Calder Growth    | No data available |

_Note: It is unclear whether Calder Investments manages additional funds or what their AUMs are._

#### 🕒 Time Series Example:

> “Revenue was reported for Q1 and Q3 2023.”

| Quarter   | Revenue        |
|-----------|----------------|
| Q1 2023   | $11M           |
| Q3 2023   | $13M           |

_Note: Coverage is unclear — other quarters may exist but are not shown._

---

### 3. ⚠️ Explicitly Non-Exhaustive

If the source language **implies the list is illustrative and not complete**, treat it as **intentionally partial**.

You may determine non-exhaustiveness if the source uses any of the following:
- “such as”
- “including”
- “among others”
- “for example”
- “e.g.”
- “select funds”
- “sample list”
- “not limited to”

You may also infer partiality based on context — even if such phrases are not present — when it is clear that the source is **providing examples, not a complete list**.

- Show only what is named.
- Clearly state that the list is **non-exhaustive**.

#### 📄 List Example:

> “Northwave Capital manages funds such as Northwave Balanced and Northwave ESG.”

| Fund Name         | AUM     |
|--------------------|---------|
| Northwave Balanced | $95M    |
| Northwave ESG      | $88M    |

_Note: This is a non-exhaustive list — other funds may exist._

#### 🕒 Time Series Example:

> “Performance highlights include Q2 and Q4 2023.”

| Quarter   | Revenue        |
|-----------|----------------|
| Q2 2023   | $14.1M         |
| Q4 2023   | $15.5M         |

_Note: Only select quarters are shown. Full range not provided._

"""


@tool
def final_answer_tool(x: RAGResponseWithCitationsFinalAnwser) -> str:
    """
    Invoke this tool to give the final answer.
    """
    return x.model_dump_json()


@tool
def compute(expression: str, variables: dict) -> dict:
    """
    Evaluate a mathematical or logical expression using the provided variables.

    This tool is used when the agent has retrieved numerical values or lists from documents 
    and needs to calculate or compare them to derive the final answer.

    The expression should be a string containing a valid Python-like expression, such as:
    - Simple arithmetic:         "a + b", "revenue - cost", "total / count"
    - Aggregation on lists:      "sum(revenue)", "max(costs)", "len(invoices)"
    - Logical comparison:        "revenue > cost", "len(employees) >= 10"
    - Combined logic/math:       "(revenue - cost) > 1_000_000"

    The `variables` argument is a dictionary mapping variable names to either:
    - A number:        {"a": 5, "b": 3}
    - A list of values:{"revenue": [5000000, 10000000], "cost": [2000000, 3000000]}

    If the expression evaluates correctly, the result will be returned as:
        {"result": <value>}

    If an error occurs, a structured message will be returned as:
        {"error": "description of what went wrong"}

    ---
    ✅ Examples:

    1. Compute the total revenue:
        compute("sum(revenue)", {"revenue": [5000000, 10000000]})
        → {"result": 15000000}

    2. Calculate profit:
        compute("revenue - cost", {"revenue": 12000000, "cost": 8000000})
        → {"result": 4000000}

    3. Check if profit is over 1 million:
        compute("(revenue - cost) > 1000000", {"revenue": 12000000, "cost": 8000000})
        → {"result": True}

    4. Find if number of customers meets threshold:
        compute("len(customers) >= 100", {"customers": [1,2,3,...]})
        → {"result": True}

    5. Handle unknown variable:
        compute("a + b", {"a": 5})
        → {"error": "name 'b' is not defined"}

    Use this tool only when numbers are already identified and need further computation.
    Do not use this tool to extract facts from documents — only to operate on known data.
    """
    # Safe interpreter
    aeval = Interpreter(usersyms=variables, use_numpy=True)

    try:
        result = aeval(expression)
        return {"result": result}
    except Exception as e:
        logging.error(e)
        return {"error": str(e)}


async def generate_answer(
    user_message: str,
    org_info: dict,
    max_retries: int = 3,
    retries: int = None,
    chat_message_id: str = None,
    tag_ids: List[str] = None,
    fund_ids: List[str] = None,
    conversation_id: str = None,
    model_hyperparams: ModelHyperparameters = ModelHyperparameters(),
) -> AsyncGenerator[BaseMessage, None]:
    trace_id = None
    with tracing.start_as_current_span(name="agentic_response_langgraph") as root_span:
        if root_span and conversation_id:
            root_span.update_trace(session_id=conversation_id)
            trace_id = root_span.trace_id
        
        document_chunk_ids = []
        document_chunks: dict = {}

        @tool
        def retrieval_tool(query: str, state: Annotated[dict, InjectedState], limit: int = 50, ) -> str:
            """
            Retrieve relevant document chunks based on a search query.
            Subsequent retrieve tool call will exclude results from previous calls implementaiton, exclude chunks ID from previous call
            """
            client = tracing.get_client()
            messages = state["messages"]
    
            last_message = messages[-1]
            if isinstance(last_message, AIMessage) and last_message.response_metadata != None and last_message.response_metadata['model_name'].startswith("claude"):
                limit = 5

            url = f"{get_base_url()}/api/retrieve-by-similarities"
            params = {
                "query": query,
                "options": {
                    "orgId": org_info["orgId"],
                    # "openDocumentId": openDocumentId,
                    # "worksheet": worksheet,
                    "lastChunkIds": document_chunk_ids,
                    "tagIds": tag_ids,
                    "fundIds": fund_ids,
                    "limit": limit
                }
            }

            if client:
                client.update_current_span(input={
                    "query": query,
                    "fundIds": fund_ids,
                    "tagIds": tag_ids,
                    "limit": limit,
                })

            try:
                response = requests.post(url, json=params)
                response.raise_for_status()
                response_text = response.text

                try:
                    response_obj = json.loads(response.text)
                    for chunk in response_obj:
                        doc_id = chunk.get("metadata", {}).get("id", None)
                        if doc_id:
                            document_chunks[chunk.get(
                                "metadata", {}).get("id", None)] = chunk
                            document_chunk_ids.append(doc_id)
                except Exception as e:
                    logging.error(e)

                return response_text

            except Exception as e:
                logging.error(e)
                return ""

        if retries is None:
            retries = max_retries

        if user_message.startswith("test_streaming"):
            answer = "START\n\n"
            for i in range(25):
                answer += ("|" + (chr(ord('a') + i) * 100) + "|\n") * 10
                yield RAGResponseWithCitations(answer=answer, citations=[], status=ChatMessageStatus.GENERATING_ANSWER, traceId=trace_id)
                await sleep(float(user_message.split(" ")[-1]))
            yield RAGResponseWithCitations(answer=answer, citations=[], status=ChatMessageStatus.READY, traceId=trace_id)    
            return

        if retries <= 0:
            yield RAGResponseWithCitations(
                answer=f"There were errors trying to answer this query after {retries} auto retries.",
                citations=[],
                status="ERROR",
                traceId=trace_id
            )
            return

        # Get the LLM with tools enabled
        llm_without_tool = new_llm(model_hyperparams, streaming=True)
        tool_list = [retrieval_tool, compute]
        llm_with_tool = llm_without_tool.bind_tools(
            tool_list + [final_answer_tool])

        s = SystemMessagePromptTemplate.from_template(tool_prompt).format_messages(
            institution_name=org_info["institutionName"])[0]
        messages = [
            s,
            HumanMessage(user_message),
        ]

        def should_continue(state: MessagesState):
            messages = state["messages"]
            last_message = messages[-1]
            if hasattr(last_message, "tool_calls") and last_message.tool_calls and last_message.tool_calls[-1]["name"] != "final_answer_tool":
                return "tools"
            return END

        async def call_model(state: MessagesState):
            messages = state["messages"]

            try:
                response = await llm_with_tool.ainvoke(messages)
                return {"messages": [response]}
            except Exception as e:
                return {"messages": [AIMessage(
                    additional_kwargs={
                        "message": str(e),
                        "type": "error",
                    },
                    content="Unable to generate answer"
                )]}

        llm_messages = {}
        steps: int = 0

        def handle_debug_chunk(chunk: tuple):
            chunk_message = chunk[0]
            chunk_meta = chunk[1]

            if isinstance(chunk_message, AIMessageChunk):
                nonlocal steps
                if llm_messages.get(chunk_message.id) is None:
                    steps += 1
                llm_messages[chunk_message.id] = {
                    "id": chunk_message.id,
                    "tool_calls": str(getattr(chunk_message, "tool_calls", [])),
                    "usage_metadata": str(getattr(chunk_message, "usage_metadata", {})),
                    "additional_kwargs": str(getattr(chunk_message, "additional_kwargs", {})),
                    "response_metadata": str(getattr(chunk_message, "response_metadata", {}))
                }

                return LLMDebugResponse(
                    steps=steps,
                    details=llm_messages.values()
                )

            return None

        def handle_debug(chunks: list):
            last_message = chunks[-1]
            if (isinstance(last_message, AIMessage)):
                nonlocal steps
                if not llm_messages.get(last_message.id):
                    steps += 1
                llm_messages[last_message.id] = {
                    "id": last_message.id,
                    "tool_calls": str(getattr(last_message, "tool_calls", [])),
                    "usage_metadata": str(getattr(last_message, "usage_metadata", {})),
                    "additional_kwargs": str(getattr(last_message, "additional_kwargs", {})),
                    "response_metadata": str(getattr(last_message, "response_metadata", {}))
                }
                return LLMDebugResponse(
                    steps=steps,
                    details=llm_messages.values()
                )

            return None

        workflow = StateGraph(MessagesState)

        tools = tool_list
        tool_node = ToolNode(tools)

        # Define the two nodes we will cycle between
        workflow.add_node("agent", call_model)
        workflow.add_node("tools", tool_node)

        workflow.add_edge(START, "agent")
        workflow.add_conditional_edges(
            "agent", should_continue, ["tools", END])
        workflow.add_edge("tools", "agent")
        callback = tracing.get_handler()
        app = workflow.compile().with_config(config={
            "callbacks": [callback] if callback else []
        })
        gathered = None

        async for values_or_messages, chunk in app.astream(
            {"messages": messages},
            stream_mode=["values", "messages"],
            config={"recursion_limit": 100}
        ):
            if values_or_messages == "values":
                gathered = None
                last_message = chunk["messages"][-1]
                debug = handle_debug(chunk["messages"])
                if not isinstance(last_message, AIMessage):
                    continue
                elif last_message.tool_calls:
                    tool_call = last_message.tool_calls[-1]
                    if tool_call["name"] == "final_answer_tool":
                        a = tool_call["args"].get("x", {})
                        if isinstance(a, str):
                            answer = a
                        else:
                            answer = a.get("answer", None)

                        if answer:
                            if isinstance(a, str):
                                citations = []
                                status = ChatMessageStatus.GENERATING_ANSWER
                            else:
                                citations = a.get("citations", [])
                                status = a.get(
                                    "status", ChatMessageStatus.GENERATING_ANSWER)

                            try:
                                # Enrich citations with metadata from citation_state
                                enriched_citations = []
                                if citations and document_chunks:
                                    for idx, citation in enumerate(citations):
                                        if isinstance(citation, str):
                                            try:
                                                citation = json.loads(citation)
                                            except json.JSONDecodeError:
                                                print(
                                                    f"Warning: Could not parse citation as JSON: {citation}")
                                                continue

                                        chunk_id = citation.get(
                                            'document_chunk_id')
                                        if chunk_id:
                                            # Find the matching chunk by ID
                                            matching_chunks = document_chunks.get(
                                                chunk_id)
                                            full_citation = matching_chunks if matching_chunks else None
                                            if full_citation:
                                                # Get metadata from the full citation
                                                metadata = full_citation.get(
                                                    'metadata', {})
                                                # Create enriched citation with proper binding
                                                enriched_citation = Citation(
                                                    sourceId=citation.get(
                                                        'sourceId', idx),
                                                    quote=citation.get(
                                                        'quote', ""),
                                                    filename=citation.get(
                                                        'filename') or metadata.get('fileName', ''),
                                                    metadata=DocumentMetadata(
                                                        text_as_html=citation.get('metadata', {}).get(
                                                            'text_as_html') or metadata.get('text_as_html', None),
                                                        languages=citation.get('metadata', {}).get(
                                                            'languages') or metadata.get('languages', ['eng']),
                                                        title=citation.get('metadata', {}).get(
                                                            'title') or metadata.get('title', 'Untitled'),
                                                        page_number=citation.get(
                                                            'metadata', {}).get('page_number', None),
                                                        page_name=citation.get(
                                                            'metadata', {}).get('page_name', None),
                                                        source=citation.get(
                                                            'metadata', {}).get('source', None),
                                                        filetype=citation.get('metadata', {}).get(
                                                            'filetype', None) or metadata.get('filetype', 'Unknown')
                                                    )
                                                )
                                                enriched_citations.append(
                                                    enriched_citation)
                                                continue

                                        # If no enrichment possible, keep original citation
                                        enriched_citations.append(citation)

                                yield RAGResponseWithCitations(
                                    agenticResponseDebug=debug,
                                    answer=answer,
                                    status=status,
                                    citations=enriched_citations or citations,  # Fallback to original if no enrichment
                                    traceId=trace_id
                                )
                            except Exception as e:
                                print(
                                    "Failed to parse RAGResponseWithCitations: ", e)
                                yield RAGResponseWithCitations(
                                    agenticResponseDebug=debug,
                                    answer=answer,
                                    status=status,
                                    citations=[],
                                    traceId=trace_id
                                )
                            return

                        content = last_message.content
                        if isinstance(content, list):
                            answer = "".join(item.get("text", "")
                                             for item in content if isinstance(item, dict))
                        else:
                            answer = content

                        status = a.get("status", ChatMessageStatus.READY)
                        yield RAGResponseWithCitations(
                            agenticResponseDebug=debug,
                            answer=answer,
                            status=status,
                            citations=[],
                            traceId=trace_id
                        )
                        return
                    elif tool_call["name"] == "retrieval_tool":
                            yield RAGResponseWithCitations(
                                debug=debug,
                                answer=f'Thinking... looking for {json.dumps(tool_call["args"]["query"])}',
                                status=ChatMessageStatus.RETRIEVING,
                                citations=[],
                                traceId=trace_id
                            )
                    elif tool_call["name"] == "compute":
                            yield RAGResponseWithCitations(
                                debug=debug,
                                answer=f'Computing... {json.dumps(tool_call["args"])}',
                                status=ChatMessageStatus.GENERATING_ANSWER,
                                citations=[],
                                traceId=trace_id
                            )
                elif last_message.content:
                    content = last_message.content
                    if isinstance(content, list):
                        answer = "".join(item.get("text", "")
                                         for item in content if isinstance(item, dict))
                    else:
                        answer = content

                    yield RAGResponseWithCitations(
                        agenticResponseDebug=debug,
                        answer=answer,
                        citations=[],
                        status=ChatMessageStatus.READY,
                        traceId=trace_id
                    )
                    return
            elif isinstance(chunk[0], AIMessageChunk):
                gathered = gathered + chunk[0] if gathered else chunk[0]
                debug = handle_debug_chunk(chunk)
                if gathered.tool_calls and gathered.tool_calls[-1].get("name", None) == "final_answer_tool":
                    parital_response_object = gathered.tool_calls[-1]["args"].get("x", {
                    })
                    # Claude response str with partial obj
                    if isinstance(parital_response_object, str):
                        partial_answer = parital_response_object
                    elif isinstance(parital_response_object, dict):
                        partial_answer = parital_response_object.get(
                            "answer", "")

                    if partial_answer:
                        yield RAGResponseWithCitations(
                            agenticResponseDebug=debug,
                            answer=partial_answer,
                            citations=[],
                            status=ChatMessageStatus.GENERATING_ANSWER,
                            traceId=trace_id
                        )
                    # This is Claude handling on empty answer
                    elif gathered.content and isinstance(gathered.content, list):
                        try:
                            content = " ".join(
                                [a["text"] for a in gathered.content if "text" in a])
                        except Exception as e:
                            content = ""
                        if content:
                            yield RAGResponseWithCitations(
                                agenticResponseDebug=debug,
                                answer=content,
                                citations=[],
                                status=ChatMessageStatus.GENERATING_ANSWER,
                                traceId=trace_id
                            )
                    else:
                        citations = parital_response_object.get(
                            "citations", []) or []
                        yield RAGResponseWithCitations(
                            agenticResponseDebug=debug,
                            answer=f"Noting down {len(citations)} relevant citations...",
                            citations=[],
                            status=ChatMessageStatus.GENERATING_CITATIONS,
                            traceId=trace_id
                        )
                elif gathered.content:
                    if isinstance(gathered.content, list):  # this is Claude
                        try:
                            content = " ".join(
                                [a["text"] for a in gathered.content if "text" in a])
                        except Exception as e:
                            content = ""
                    else:
                        content = gathered.content
                    if content:
                        yield RAGResponseWithCitations(
                            agenticResponseDebug=debug,
                            answer=content,
                            citations=[],
                            status=ChatMessageStatus.GENERATING_ANSWER,
                            traceId=trace_id
                        )
        return
