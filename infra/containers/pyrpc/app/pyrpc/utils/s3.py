import uuid
import boto3
import json
import os

import logging
from pyrpc.utils.aws_secret import get_secret_value

logging.basicConfig(level=logging.INFO)

def write_chunks_to_s3(chunks: list[str], bucket: str, document_id: str):
    s3 = boto3.client(
        "s3",
        aws_access_key_id=os.environ.get("AWS_S3_ACCESS_KEY") or get_secret_value("AWS_S3_ACCESS_KEY"),
        aws_secret_access_key=os.environ.get("AWS_S3_SECRET_ACCESS_KEY") or get_secret_value("AWS_S3_SECRET_ACCESS_KEY"),
        region_name=os.environ.get("AWS_S3_REGION", "us-east-1") or get_secret_value("AWS_S3_REGION")
    )
    random_id = str(uuid.uuid4())

    logging.info(f"Writing {len(chunks)} chunks to S3: {bucket}/{random_id}/{document_id}/chunks.json")
    
    # Write the object with ACL permissions for the target account
    # Permission adjustments are required in order to read and then delete the chunks
    # on the Vercel side
    s3.put_object(
        Bucket=bucket,
        Key=f"{random_id}/{document_id}/chunks.json",
        Body=json.dumps(chunks),
    )
    
    return f"{random_id}/{document_id}/chunks.json"