import asyncio
import functools
import logging
import os
from concurrent.futures import <PERSON><PERSON><PERSON><PERSON>xecutor
from typing import Any, Callable

process_pool = ProcessPoolExecutor(max_workers=os.cpu_count())
background_tasks: set[asyncio.Future] = set()

def is_pool_running(pool: ProcessPoolExecutor) -> bool:
    # Has shutdown() been called?
    if getattr(pool, "_shutdown", False):
        return False

    # Any live worker processes?
    procs = getattr(pool, "_processes", {})
    return any(p.is_alive() for p in procs.values())


async def schedule_worker(worker: Callable[..., Any],
                          *args: Any,
                          **kwargs: Any, ) -> None:
    """
    Submit a job to the process pool and keep a reference to its Future
    so it isn't GC-ed prematurely (same idea as your original set).
    """
    global process_pool, background_tasks

    logging.info(f"Scheduling work for doc ID {kwargs.get("doc_id", None)}")

    # Make sure to re-set the pool if it dies for some reason
    if not is_pool_running(process_pool):
        logging.info(f"Refreshing pool for doc ID {kwargs.get("doc_id", None)}")
        process_pool = ProcessPoolExecutor(max_workers=os.cpu_count())
        background_tasks = set()

    loop = asyncio.get_running_loop()

    bound_call = functools.partial(worker, *args, **kwargs)

    # run_in_executor hands execution to the ProcessPoolExecutor
    task = loop.run_in_executor(process_pool, bound_call)

    # Register a discard callback
    background_tasks.add(task)

    def _on_done(future: asyncio.Future) -> None:
        logging.info(f"Finished work _on_done called {future.result()}")
        background_tasks.discard(future)
        if not background_tasks:
            process_pool.shutdown(wait=False)

    task.add_done_callback(_on_done)

    logging.info(f"Done scheduling work for doc ID {kwargs.get("doc_id", None)}")
