import os

from pyrpc.utils.aws_secret import get_secret_value


def get_base_url(override_url=None):
    if override_url is not None:
        return override_url

    # Equivalent logic from TypeScript version
    if os.getenv("VERCEL_PROJECT_PRODUCTION_URL") or get_secret_value("VERCEL_PROJECT_PRODUCTION_URL"):
        return f"https://{os.getenv('VERCEL_PROJECT_PRODUCTION_URL') or get_secret_value('VERCEL_PROJECT_PRODUCTION_URL')}"
    if os.getenv("VERCEL_URL"):
        return f"https://{os.getenv('VERCEL_URL')}"
    if os.getenv("DEV_BASE_URL"):
        return os.getenv("DEV_BASE_URL")
    return f"http://host.docker.internal:{os.getenv('PORT', 3000)}"