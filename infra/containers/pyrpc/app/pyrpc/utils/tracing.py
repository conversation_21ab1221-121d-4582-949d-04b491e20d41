from contextlib import nullcontext, contextmanager
import os
from typing import List, Dict, Any, Literal, Optional, Callable, TypeVar, cast
from functools import wraps
from langfuse.langchain import CallbackHandler
from langfuse import Langfuse
from pyrpc.utils.aws_secret import get_secret_value
from langfuse.model import MapValue
import logging
import contextvars
import uuid
import inspect
import types
from pydantic import BaseModel
import json

# Context variable to hold the trace ID for the current request
_trace_id_var = contextvars.ContextVar('trace_id', default=None)

# Type variable for generic function type
F = TypeVar('F', bound=Callable[..., Any])

class PipelineTracing:
    """
    Manages a single trace for an entire request pipeline, ensuring thread safety
    with contextvars.
    
    - A single trace is created at the start of the request.
    - Each processing step is a span within that trace.
    """
    def __init__(self):
        self.langfuse_public_key = (os.getenv("LANGFUSE_PUBLIC_KEY") or
                                    get_secret_value("LANGFUSE_PUBLIC_KEY"))
        self.langfuse_secret_key = (os.getenv("LANGFUSE_SECRET_KEY") or
                                    get_secret_value("LANGFUSE_SECRET_KEY"))
        self.langfuse_host = (os.getenv("LANGFUSE_HOST") or
                              get_secret_value("LANGFUSE_HOST"))

        if not all([self.langfuse_public_key, self.langfuse_secret_key, self.langfuse_host]):
            self.client = None
            logging.warning("Langfuse keys not found. Tracing is disabled.")
        else:
            self.client = Langfuse(
                public_key=self.langfuse_public_key,
                secret_key=self.langfuse_secret_key,
                host=self.langfuse_host
            )

    @contextmanager
    def start_trace(self, name: str, session_id: Optional[str] = None, user_id: Optional[str] = None):
        """
        A context manager to start a trace in Python.
        It creates a new root span and sets its trace_id in the context for subsequent steps.
        """
        if not self.client:
            yield
            return

        with self.client.start_as_current_span(name=name) as span:
            span.update_trace(user_id=user_id, session_id=session_id)
            trace_id = span.get_trace_id()
            token = _trace_id_var.set(trace_id)
            logging.info(f"Trace started: {trace_id} for session: {session_id}")
            try:
                yield
            finally:
                _trace_id_var.reset(token)
                logging.info(f"Trace finished: {trace_id}")

    @contextmanager
    def continue_trace(self, trace_id: Optional[str], session_id: Optional[str] = None):
        """
        A context manager to continue a trace from an external source (e.g., NodeJS).
        It only sets the trace_id in the context for subsequent steps.
        """
        if not self.client or not trace_id:
            yield
            return

        token = _trace_id_var.set(trace_id)
        logging.info(f"Continuing trace: {trace_id} for session: {session_id}")
        try:
            yield
        finally:
            _trace_id_var.reset(token)
            logging.info(f"Finished step on trace: {trace_id}")

    def _prepare_input_dict(self, func: F, *args, **kwargs) -> dict:
        """Helper to capture and serialize function inputs."""
        try:
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            input_dict = {}
            for k, v in bound_args.arguments.items():
                if k in ['self', 'cls']:
                    continue
                if isinstance(v, BaseModel):
                    input_dict[k] = v.model_dump()
                else:
                    # Attempt to handle other types, fallback for unserializable ones
                    try:
                        json.dumps(v)
                        input_dict[k] = v
                    except (TypeError, OverflowError):
                        input_dict[k] = f"Unserializable value of type {type(v).__name__}"

            return input_dict
        except Exception as e:
            logging.warning(f"Failed to capture function inputs for {func.__name__}: {e}")
            return {"info": "Could not capture inputs"}

    def observe_step(self, name: str):
        """
        A decorator to mark a function as a span within the current trace.
        It reads the trace_id from the context, creates a new span,
        and captures inputs, outputs, and errors.
        """
        def decorator(func: F) -> F:
            if inspect.iscoroutinefunction(func):
                @wraps(func)
                async def async_wrapper(*args, **kwargs):
                    if not self.client: return await func(*args, **kwargs)
                    
                    input_dict = self._prepare_input_dict(func, *args, **kwargs)
                    trace_id = _trace_id_var.get()
                    
                    span_args = {"name": name, "input": input_dict}
                    if trace_id: span_args["trace_context"] = {"trace_id": trace_id}
                    else: logging.warning(f"No active trace found for '{name}'. Starting a new trace.")
                    
                    async with self.client.start_as_current_span(**span_args) as span:
                        try:
                            result = await func(*args, **kwargs)
                            if not isinstance(result, (types.GeneratorType, types.AsyncGeneratorType)):
                                output = result.model_dump() if isinstance(result, BaseModel) else result
                                span.update(output=output)
                            return result
                        except Exception as e:
                            span.update(level="ERROR", status_message=str(e))
                            raise
                return cast(F, async_wrapper)
            else:
                @wraps(func)
                def sync_wrapper(*args, **kwargs):
                    if not self.client: return func(*args, **kwargs)

                    input_dict = self._prepare_input_dict(func, *args, **kwargs)
                    trace_id = _trace_id_var.get()

                    span_args = {"name": name, "input": input_dict}
                    if trace_id: span_args["trace_context"] = {"trace_id": trace_id}
                    else: logging.warning(f"No active trace found for '{name}'. Starting a new trace.")

                    with self.client.start_as_current_span(**span_args) as span:
                        try:
                            result = func(*args, **kwargs)
                            if not isinstance(result, (types.GeneratorType, types.AsyncGeneratorType)):
                                output = result.model_dump() if isinstance(result, BaseModel) else result
                                span.update(output=output)
                            return result
                        except Exception as e:
                            span.update(level="ERROR", status_message=str(e))
                            raise
                return cast(F, sync_wrapper)

        return decorator

    def get_handler(self) -> Optional[CallbackHandler]:
        """
        Gets a Langchain CallbackHandler that is automatically linked to the current trace/span.
        """
        if not self.client:
            return None
        
        # CallbackHandler with no arguments should pick up the active span from the OTEL context.
        return CallbackHandler()


# Global instance to be used across the application
pipeline_tracing = PipelineTracing()
class Tracing:
    client: Langfuse = None
    def __init__(self):
        langfuse_public_key = (os.getenv("LANGFUSE_PUBLIC_KEY") or
                          get_secret_value("LANGFUSE_PUBLIC_KEY"))

        langfuse_secret_key = (os.getenv("LANGFUSE_SECRET_KEY") or
                          get_secret_value("LANGFUSE_SECRET_KEY"))

        langfuse_host = (os.getenv("LANGFUSE_HOST") or
                          get_secret_value("LANGFUSE_HOST"))

        # Manually force-set the env var for AWS environments
        if langfuse_public_key:
            os.environ["LANGFUSE_PUBLIC_KEY"] = langfuse_public_key
        if langfuse_secret_key:
            os.environ["LANGFUSE_SECRET_KEY"] = langfuse_secret_key
        if langfuse_host:
            os.environ["LANGFUSE_HOST"] = langfuse_host

        if not langfuse_public_key or not langfuse_secret_key or not langfuse_host:
            return
        
        self.client = Langfuse(
            public_key=langfuse_public_key,
            host=langfuse_host
        )

    def get_client(self):
        return self.client

    def start_as_current_span(
        self,
        *,
        name: str,
        ) -> "LangfuseSpan":
        if not self.client:
            return nullcontext()
        try:
            return self.client.start_as_current_span(name=name)
        except Exception as e:
            return nullcontext()
        
    def start_as_current_generation(
        self,
        *,
        name: str,
        model: str,
        model_parameters: Optional[Dict[str, MapValue]] = None,
        input: Optional[Any] = None,
    ) -> "LangfuseSpan":
        if not self.client:
            return nullcontext()
        try:
            return self.client.start_as_current_generation(name=name, model=model, model_parameters=model_parameters, input=input)
        except Exception as e:
            return nullcontext()
    
    def get_handler(self):
        if not self.client:
            return None
        return CallbackHandler()
