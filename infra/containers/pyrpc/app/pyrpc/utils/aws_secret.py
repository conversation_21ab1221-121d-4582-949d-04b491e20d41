import json
import os
import boto3
from botocore.exceptions import ClientError

def get_secret_value(secret_value):
    secret_name = os.getenv("SECRET_NAME")
    region_name = "us-east-1"

    if not secret_name:
        return None

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=region_name
    )

    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name
        )
    except ClientError as e:
        # For a list of exceptions thrown, see
        # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
        raise e

    parsed_secret = json.loads(get_secret_value_response['SecretString'])
    secret = parsed_secret[secret_value]
    return secret