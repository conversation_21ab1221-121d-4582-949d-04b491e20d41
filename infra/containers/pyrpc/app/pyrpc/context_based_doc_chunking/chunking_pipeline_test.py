import sys
import os
from pathlib import Path
import json
import mimetypes
from pyrpc.context_based_doc_chunking.chunking_pipeline import chunker
from pyrpc.context_based_doc_chunking.step_2_build_graph import build_graph, cluster
import pandas as pd
import logging

def _convert_format(c, doc_context):
    c['pageContent'] = f"{doc_context}__@__{c['text']}"
    c['id'] = f"{c['element_id']}"
    # NOTE! store chunks expects an object in metadata
    c['metadata'] = {}
    del c['text']
    del c['element_id']
    return c
    
def main():
    if len(sys.argv) < 3:
        print("Usage: python test_chunking_pipeline.py <documents.json> <output_json>")
        sys.exit(1)
    documents_json = sys.argv[1]
    output_json = sys.argv[2]

    # Read documents.json, which should be a list of dicts with doc_id and path
    with open(documents_json, "r") as f:
        documents = json.load(f)
    print(Path(__file__).resolve())
    project_root = Path(__file__).resolve().parents[6]

    results = []
    for entry in documents:
        doc_id = entry.get("doc").get("id")
        file_path = entry.get("path")
        file_path = project_root / entry.get("path")
        file_path = str(file_path)

        if not doc_id or not file_path or not os.path.isfile(file_path):
            print(f"Skipping entry: invalid doc_id or file path: {entry}")
            continue
        file_type, _ = mimetypes.guess_type(file_path)
        if file_type is None:
            file_type = "application/octet-stream"
        with open(file_path, "rb") as f:
            blob = f.read()
            
        print(f"Processing {file_path} as doc_id={doc_id}... {len(blob)}")
        
        chunks, doc_context = chunker(doc_id, blob, file_type)
        _chunking_mode = os.getenv('CHUNKING_MODE', None)
        if not chunks:
            print(f"No chunks for {file_path}, skipping.")
            continue

        if chunks is None or len(chunks) == 0:
            print(f'Error chunking {doc_id}')
            continue

        if _chunking_mode == 'NORMAL_ONLY':
            logging.debug(f'\nStoring NORMAL chunks for {doc_id} and skipping JUMBO generation (ctx={doc_context}).')
            results.append({
                "doc_id": doc_id,
                "doc_context": doc_context,
                "contextual_chunks": [_convert_format(c, doc_context) for c in chunks],
                "chunks": chunks
            })
            continue

        # TODO: Need to sync with chunking pipeline process
        # Build chunks' context graph
        G = build_graph(chunks)

        print(f'\nGraph nodes {len(G.nodes)}')

         # Cluster them into text chunks for later processing
        contextual_chunks = cluster(G)

        cdf = pd.DataFrame(contextual_chunks)
        contextual_chunks = list(cdf.reset_index().T.to_dict().values())

        results.append({
            "doc_id": doc_id,
            "doc_context": doc_context,
            "contextual_chunks": [_convert_format(c, doc_context) for c in contextual_chunks],
            "chunks": chunks
        })

    with open(output_json, "w") as f:
        json.dump(results, f, indent=2)
    print(f"Results written to {output_json}")

if __name__ == "__main__":
    main()
