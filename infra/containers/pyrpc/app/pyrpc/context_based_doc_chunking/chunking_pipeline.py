import asyncio
import json
import logging

import numpy as np
import pandas as pd

from pyrpc.context_based_doc_chunking.step_1_pre_chunk import chunker
from pyrpc.context_based_doc_chunking.step_2_build_graph import build_graph, cluster
from pyrpc.utils.aws_secret import get_secret_value
from pyrpc.utils.base_url import get_base_url
from pyrpc.utils.concurrent_workers import schedule_worker
from pyrpc.utils.s3 import write_chunks_to_s3

logging.basicConfig(level=logging.INFO)


class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        return super().default(obj)


import os
import requests

DEBUG_URL_OVERRIDE = os.getenv('DEBUG_URL_OVERRIDE', None)


def store_chunks(document_id: str, doc_context: str, chunks: list) -> dict:
    """
    Send document chunks off to your NodeJS service for storage & embedding.
    """
    logging.info(f'\nStoring chunks for {document_id}')

    # TODO DORON 220425 this is ugly, we need to make sure we use the same types
    def _convert_format_normal(doc_context, c, id):
        _c = {}
        _c['pageContent'] = f"{doc_context}__@__{c}"
        _c['id'] = f"{id}"
        # NOTE! store chunks expects an object in metadata
        _c['metadata'] = {}
        return _c

    def _convert_format_jumbo(doc_context, c):
        c['pageContent'] = f"{doc_context}__@__{c['text']}"
        c['id'] = f"{c['element_id']}"
        # NOTE! store chunks expects an object in metadata
        c['metadata'] = {}
        del c['text']
        del c['element_id']
        return c

    _chunking_mode = os.getenv('CHUNKING_MODE', 'JUMBO_AND_NORMAL')
    if _chunking_mode == 'JUMBO_ONLY' or _chunking_mode == 'JUMBO_AND_NORMAL':
        chunks = [_convert_format_jumbo(doc_context, c) for c in chunks]
    elif _chunking_mode == 'NORMAL_ONLY':
        chunks = [_convert_format_normal(doc_context, c, id) for c, id in zip(chunks, range(len(chunks)))]

    api_secret = os.getenv("CHUNKING_API_SECRET") or get_secret_value("CHUNKING_API_SECRET")
    if not api_secret:
        raise RuntimeError("Missing CHUNKING_API_SECRET environment variable")

    url = f"{get_base_url(DEBUG_URL_OVERRIDE)}/api/store-chunks"
    headers = {
        "Authorization": f"Bearer {api_secret}",
        "Content-Type": "application/json"
    }
    payload = {
        "documentId": document_id,
        "chunks": chunks
    }

    if os.getenv("VERCEL_PROJECT_PRODUCTION_URL") or get_secret_value("VERCEL_PROJECT_PRODUCTION_URL"):
        chunk_path = write_chunks_to_s3(chunks,
                                        os.getenv("CHUNKING_S3_BUCKET") or get_secret_value("CHUNKING_S3_BUCKET"),
                                        document_id)
        payload = {
            "documentId": document_id,
            "chunkPath": chunk_path,
        }

    resp = requests.post(url, headers=headers, json=payload)
    resp.raise_for_status()

    logging.info(f'\nPosted stored chunks for {document_id}')

    return resp.json()


def worker(*args, **kwargs):
    # Set default if needed
    _chunking_mode = os.getenv('CHUNKING_MODE', 'JUMBO_AND_NORMAL')

    doc_id = kwargs.get("doc_id", None)
    blob = kwargs.get("blob", None)
    file_type = kwargs.get("file_type", None)
    test_mode = kwargs.get("test_mode", False)

    logging.info(f"Worker started processing doc ID {doc_id}")

    # Pre-chunk the doc
    chunks, doc_context = chunker(doc_id=doc_id)

    _chunking_mode = os.getenv('CHUNKING_MODE', None)
    if _chunking_mode == 'NORMAL_ONLY':
        logging.debug(f'\nStoring NORMAL chunks for {doc_id} and skipping JUMBO generation (ctx={doc_context}).')
        store_chunks(doc_id, doc_context, chunks)
        return

    if chunks is None or len(chunks) == 0:
        logging.error(f'Error chunking {doc_id}')
        # TODO DORON 290425 what to return on the return path?
        return

    logging.debug(f'\nBuilding graph for chunks: {len(chunks)}')

    # Build chunks' context graph
    G = build_graph(chunks)

    logging.debug(f'\nClustering graph nodes {len(G.nodes)}')

    # Cluster them into text chunks for later processing
    contextual_chunks = cluster(G)

    cdf = pd.DataFrame(contextual_chunks)
    contextual_chunks = list(cdf.reset_index().T.to_dict().values())

    if not test_mode:
        logging.debug(f'\nStoring chunks for {doc_id}')
        store_chunks(doc_id, doc_context, contextual_chunks)

    logging.info(f"Worker finished processing doc ID {doc_id}")

    return doc_id, doc_context, contextual_chunks, chunks


async def process(_doc_id: str):
    await schedule_worker(worker, doc_id=_doc_id)


if __name__ == '__main__':
    asyncio.run(process('cmaz6zl2i0015m1r9e48l0p24'))
