import logging
import os

import networkx as nx
import numpy as np
import pandas as pd
from networkit import nxadapter, community
from networkx.drawing.nx_pydot import to_pydot
from nltk import word_tokenize
from scipy.spatial import distance_matrix

from pyrpc.context_based_doc_chunking.services.llm.voyageai_embedding_adapter import VoyageAIAdapter

logging.basicConfig(level=logging.INFO)

# TODO DEBUG DORON 240425
DEBUG_URL_OVERRIDE = os.getenv('DEBUG_URL_OVERRIDE', None)

embeder = VoyageAIAdapter()
embedding_dimension = embeder.embedding_dimension()

def build_graph(chunks, dotfile=None, print_graph=False):
    global embeder, embedding_dimension

    logging.info(f'\nBuilding graph for {len(chunks)} chunks')

    # Create embedded nodes
    nodes = {}
    for grp in chunks:
        elements = chunks[grp]
        for el in elements:
            _emb_val = embeder.embed(el)
            if _emb_val is None or np.isnan(_emb_val).any() or _emb_val.shape[0] != embedding_dimension:
                # We have a bad embedding, we'll drop the element by marking its text with NaN embedding
                nodes[el] = {'text': el, 'embedding': None}
                logging.warning(f'Element {el} in chunk {grp} failed to embed. Dropping it.')
            else:
                nodes[el] = {'text': el, 'embedding': _emb_val}

    # Create similarity matrix
    df = pd.DataFrame.from_records(nodes).T.reset_index(drop=True)

    # Drop all texts that have bad embeddings
    df.dropna(inplace=True, subset=['embedding'])

    similarity_matrix = 1.0 - distance_matrix(df['embedding'].tolist(), df['embedding'].tolist())

    from sklearn.preprocessing import MinMaxScaler

    scaler = MinMaxScaler(feature_range=(0, 1))
    similarity_matrix = scaler.fit_transform(similarity_matrix)

    # Create an empty undirected graph
    G = nx.Graph()

    # Add nodes to the graph along with their attributes from the DataFrame
    for idx, row in df.iterrows():
        G.add_node(idx, **{**row.to_dict(), **{'id': idx, 'metadata': []}}, label=row['text'])

    # Add edges to the graph using similarity values as weights
    num_nodes = similarity_matrix.shape[0]
    for i in range(num_nodes):
        for j in range(i + 1, num_nodes):  # upper triangle for undirected graph
            sim_value = similarity_matrix[i, j]
            # Optionally, add a threshold to filter edges (e.g., only add edges with similarity > 0.3)
            if sim_value <= 1.0e-6:
                continue
            G.add_edge(i, j, weight=sim_value)

    if print_graph:
        # At this point, G contains all nodes from the DataFrame and edges with the similarity values as weights.
        logging.debug("Graph edges with weights:")
        for u, v, d in G.edges(data=True):
            logging.debug(f"{u} - {v}: {d}")

    if dotfile is not None:
        pydot_graph = to_pydot(G)

        # Print DOT format string to console
        dot_string = pydot_graph.to_string()
        logging.debug(dot_string)

        # Optionally, write the DOT format to a file
        with open(dotfile, "w") as f:
            f.write(dot_string)

    logging.info(f'\nBuilt graph for {len(chunks)} chunks')
    return G


def community_size_words(G, community):
    sum = 0
    for node_id in community:
        node = G.nodes.get(node_id)
        logging.debug(f'"Comm. node: {node}')
        sum += len(word_tokenize(node['text']))

    logging.debug(f'Community word sum: {sum}')
    return sum


def limited_greedy_modularity_communities(G, max_size, weight='weight', resolution=1.5):
    """
    Run greedy_modularity_communities on G, then for any resulting community
    larger than max_size, recurse on its induced subgraph to split it further.
    Returns a list of sets, each of size <= max_size.
    """
    if len(G.nodes) == 0 or len(G.edges) == 0:
        logging.debug(f'No communities found. [N={len(G.nodes)}, E={len(G.edges)}]')
        return []

    nx_nodes = list(G.nodes())
    label2id = {lbl: i for i, lbl in enumerate(nx_nodes)}
    id2label = nx_nodes

    # Convert to NetworKit representation
    nkG = nxadapter.nx2nk(G, weightAttr='weight', data=True)

    plm = community.PLM(nkG, refine=True, gamma=resolution)  # Parallel Louvain
    plm.run()
    partition = plm.getPartition()
    comm_ids = partition.getSubsetIds()

    # Get communities in original node labels (from G networkx graph)
    communities = [[id2label[i] for i in partition.getMembers(cid)] for cid in comm_ids]

    # TODO DORON 210525, trying to replace w/faster equiv. alg:  communities = list(greedy_modularity_communities(G, weight=weight, resolution=resolution))

    output = []
    for comm in communities:
        if community_size_words(G, comm) <= max_size:
            output.append(comm)
        else:
            subG = G.subgraph(comm).copy()
            # if subG has single or no edges left, break into singletons to avoid infinite recursion
            if len(subG.edges) <= 1:
                output.extend({n} for n in comm)
            else:
                # recurse to split this big community
                output.extend(limited_greedy_modularity_communities(subG,
                                                                    max_size,
                                                                    weight=weight,
                                                                    resolution=resolution))

    return output


def cluster(G):
    # Use the greedy modularity communities algorithm to detect clusters
    communities = list(limited_greedy_modularity_communities(G, max_size=64, weight='weight', resolution=1.5))

    clusters = {}

    if communities is None or len(communities) == 0:
        # We need to set each node as its own community
        # Map each node to its community id
        for comm_id, comm_nodes in enumerate(zip(range(len(G.nodes)), list(G.nodes()))):
            for node_id in comm_nodes:
                node = G.nodes.get(node_id)

                if comm_id not in clusters:
                    clusters[comm_id] = []

                clusters[comm_id].append(node)
    else:
        # Map each node to its community id
        for comm_id, comm_nodes in enumerate(communities):
            for node_id in comm_nodes:
                node = G.nodes.get(node_id)

                if comm_id not in clusters:
                    clusters[comm_id] = []

                clusters[comm_id].append(node)

    merged_clusters = []
    for _cluster_id in clusters:
        _cluster = clusters[_cluster_id]
        _cluster_text = ''
        for _element in _cluster:
            text = _element['text']
            _cluster_text += text + '.\n'
        merged_clusters.append({'text': _cluster_text.strip(), 'element_id': _cluster_id, 'metadata': []})

    logging.info(f'\nClustered graph nodes {len(G.nodes)}')

    return merged_clusters
