import os
import sys
import tempfile
from typing import List, Optional

import bs4
import requests
from bs4 import BeautifulSoup
from langchain_community.document_loaders import (
    PyPDFLoader,
    Docx2txtLoader,
    UnstructuredFileIOLoader,
)
from nltk.tokenize import blankline_tokenize
from unstructured.documents.elements import TableChunk, CompositeElement
import unstructured_client
from unstructured_client.models import operations, shared


from pyrpc.utils.aws_secret import get_secret_value

import logging

from pyrpc.utils.html2text import html_to_text_advanced, longest_repeated_substrings, count_substring, \
    replace_after_first

logging.basicConfig(level=logging.INFO)

class UnstructuredAdapter:
    def __init__(self):
        pass

    def split_text_into_paragraphs(self, text):
        """
        Splits the input text into paragraphs using NLTK's blankline_tokenize.
        A paragraph is defined as a block of text separated by one or more blank lines.
        """
        # blankline_tokenize splits the text on blank lines.
        paragraphs = blankline_tokenize(text)
        # Remove any leading/trailing whitespace from each paragraph.
        return [para.strip() for para in paragraphs if para.strip()]

    def intelligent_pre_chunking(self, texts):
        chunks = []
        for text in texts:
            if isinstance(text, TableChunk):
                soup = BeautifulSoup(text.metadata.text_as_html, 'html.parser')
                paragraphs = [p.get_text(strip=True) for p in soup.find_all('td')]
                chunks += paragraphs
            elif isinstance(text, CompositeElement):
                # Split text to paragraphs
                paragraphs = self.split_text_into_paragraphs(text)
                chunks += paragraphs
            else:
                # TODO for now we bucket all other cases, but we need to use special Unstr. types to
                #      better pre-split into "paragraphs"
                # Split text to paragraphs
                paragraphs = self.split_text_into_paragraphs(text)
                chunks += paragraphs

        # Remove too short chunks
        filtered_chunks = [c.strip() for c in chunks if len(c.strip()) >= 2]

        # Remove duplicated whole chunks
        trimmed_chunks = []
        for c in filtered_chunks:
            repeated_substrs = set(longest_repeated_substrings(c))
            for rsub in repeated_substrs:
                _count = count_substring(c, rsub)
                if (_count * len(rsub)) / len(c) > 0.5:
                    # If the repeated substrings appear more than 50% of the text, we remove them,
                    # keeping just the first one
                    c = replace_after_first(c, rsub)

            trimmed_chunks.append(c)

        trimmed_chunks = list(set(trimmed_chunks))

        # Remove duplicated lines/sentences within chunks
        filtered_chunks_dedup = []
        for c in trimmed_chunks:
            filtered_chunks_dedup.append('\n'. join(list(set(c.split('\n')))))

        # Remove HTML tags
        html_cleaned_chunks = []
        for c in filtered_chunks:
            html_cleaned_chunks.append(html_to_text_advanced(c))

        return html_cleaned_chunks

    def _get_langchain_loader(self, file_name: str, content_type: str):
        """Get the appropriate LangChain document loader based on file type."""
        if content_type == "application/pdf":
            return PyPDFLoader
        elif content_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            return Docx2txtLoader
        elif content_type in ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                            "application/vnd.ms-excel",
                            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                            "application/vnd.ms-powerpoint"]:
            return UnstructuredFileIOLoader
        return None

    def _load_with_langchain(self, file_name: str, content_type: str, file_bytes) -> Optional[List[dict]]:
        """Load document using LangChain as a fallback."""
        try:
            # Get the appropriate loader
            loader_class = self._get_langchain_loader(file_name, content_type)
            if not loader_class:
                logging.warning(f"No LangChain loader available for content type: {content_type}")
                return None

            # Load the document using the blob
            if loader_class == UnstructuredFileIOLoader:
                loader = loader_class(file_bytes, mode="elements")
            elif loader_class == PyPDFLoader:
                # Create a temporary file for PDF loading
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                    temp_file.write(file_bytes.read())
                    temp_file.flush()
                    loader = loader_class(temp_file.name)
                try:
                    docs = loader.load()
                finally:
                    # Clean up the temporary file
                    os.unlink(temp_file.name)
            else:
                loader = loader_class(file_bytes)
                docs = loader.load()

            # Convert to the same format as Unstructured API
            elements = []
            for i, doc in enumerate(docs):
                element = {
                    "id": f"langchain_{i}",
                    "text": doc.page_content,
                    "type": "text",
                    "metadata": {
                        "source": file_name,
                        "pageNumber": doc.metadata.get("page", 1),
                        "filetype": content_type,
                    }
                }
                elements.append(element)

            return elements
        except Exception as e:
            logging.error(f"Error in LangChain fallback: {str(e)}")
            return None

    def partition(self, file_name, content_type, file_bytes):              
        try:
            # Load environment variables
            UNSTRUCTURED_API_KEY = os.getenv("UNSTRUCTURED_API_KEY") or get_secret_value("UNSTRUCTURED_API_KEY")
            UNSTRUCTURED_API_URL = os.getenv("UNSTRUCTURED_API_URL") or get_secret_value("UNSTRUCTURED_API_URL")

            client = unstructured_client.UnstructuredClient(
                        api_key_auth=UNSTRUCTURED_API_KEY,
                        server_url="https://api.unstructuredapp.io"
                    )

            blob = bytearray(file_bytes.read())           
            
            req = operations.PartitionRequest(
                partition_parameters=shared.PartitionParameters(
                    files=shared.Files(
                        content=blob,
                        file_name=file_name,
                    ),
                    strategy=shared.Strategy.HI_RES,
                    chunking_strategy="by_title",
                    overlap="500",
                    max_characters="5000",
                    overlap_all="true",
                    skip_infer_table_types=[],
                    extract_image_block_types=["Image", "Table"],
                    include_orig_elements=False,
                    split_pdf_concurrency_level=15,
                    split_pdf_allow_failed=True,
                    split_pdf_page=True,
                ),
            )
          
            logging.info(f'Calling Unstructured API')

            res = client.general.partition(
                request=req
            )

            element_dicts = [element for element in res.elements]            
            logging.debug(f'\n\nUnstructured API response: {element_dicts}')

           
            if element_dicts and len(element_dicts) > 0:
                return element_dicts
            else:
                return None
        except Exception as e:
            logging.error(f'Problem running unstructured: {str(e)}')    

        return None
