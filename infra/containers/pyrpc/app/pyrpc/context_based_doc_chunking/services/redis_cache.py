import hashlib
import os

import redis
import logging

logging.basicConfig(level=logging.INFO)


class RedisCache:
    def __init__(self):
        # Connect to Redis
        self.redis_client = redis.Redis(host=os.getenv('REDIS_CACHE_HOST'),
                                        port=os.getenv('REDIS_CACHE_PORT'),
                                        db=os.getenv('REDIS_CACHE_DB'))
        self.CACHE_TTL = os.getenv('REDIS_CACHE_TTL')

    def hash_file(self, filepath):
        """Return the SHA256 hash of the file."""
        sha = hashlib.sha256()
        with open(filepath, 'rb') as f:
            while chunk := f.read(8192):
                sha.update(chunk)
        return sha.hexdigest()

    def cache_file(self, key, metadata):
        """Store file in Redis if changed or new."""
        if not os.path.isfile(key):
            logging.debug(f"File not found: {key}")
            return False

        file_hash = self.hash_file(key)
        cached_hash_key = f"{file_hash}:hash"
        cached_file_key = f"{file_hash}:data"
        cached_metadata_key = f"{file_hash}:metadata"

        prev_hash = self.redis_client.get(cached_hash_key)

        if prev_hash and prev_hash.decode() == file_hash:
            logging.debug(f"No changes in {key}. Skipping cache update.")
            return False

        with open(key, 'rb') as f:
            file_data = f.read()
            self.redis_client.set(cached_hash_key, file_hash, ex=self.CACHE_TTL)
            self.redis_client.set(cached_file_key, file_data, ex=self.CACHE_TTL)
            self.redis_client.set(cached_metadata_key, metadata, ex=self.CACHE_TTL)
            logging.debug(f"Cached {key} (updated).")
            return True

    def fetch_file(self, filepath, output_path=None):
        if not os.path.isfile(filepath):
            logging.debug(f"File not found: {filepath}")
            return False

        file_hash = self.hash_file(filepath)

        """Retrieve file from Redis and write to disk."""
        cached_file_data_key = f"{file_hash}:data"
        file_data = self.redis_client.get(cached_file_data_key)

        if file_data is None:
            logging.debug(f"No cached file for {filepath}.")
            return None

        logging.debug(f"Fetched {filepath} from cache to {output_path}.")
        return file_data

    def fetch_file_metadata(self, filepath):
        if not os.path.isfile(filepath):
            logging.debug(f"File not found: {filepath}")
            return False

        file_hash = self.hash_file(filepath)

        """Retrieve file from Redis and write to disk."""
        cached_metadata_key = f"{file_hash}:metadata"
        metadata = self.redis_client.get(cached_metadata_key)

        if metadata is None:
            logging.debug(f"No cached file metadata for {filepath}.")
            return None

        return metadata
