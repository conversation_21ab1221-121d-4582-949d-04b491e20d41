import os

import google.generativeai as genai

genai.configure(api_key=os.environ.get("GEMINI_API_KEY", None))

# Set the model to "gemini-2.0-flash"
model = genai.GenerativeModel('gemini-2.0-flash-exp',
                              system_instruction="Please provide the JSON output as plain text without any wrappers, format should obey python's json.loads() function format.")

prompt = f"""
    You are provided by a list of text chunks seperated by newlines.
    Group the chunks based on contextual similarity.
    Pay special attention to person names when grouping lines into contexts. 
    Keep the full text of each line in the output JSON.

    The chunks are:
    -\t{'\n\n-\t'.join(chunks)}
"""

# Generate content using the Gemini 2.0 Flash model.
# Here we use the alias 'gemini-2.0-flash-exp' which refers to the experimental flash model.
response = model.generate_content(prompt)

text = response.text
if response.text.startswith("```json"):
    text = response.text[len("```json"):].strip()
if response.text.endswith("```"):
    text = text[:-3].strip()


# def get_embedding(text, model="models/gemini-embedding-exp-03-07"):
def get_embedding(text, model="models/text-embedding-004"):
    embedding_response = genai.embed_content(
        model=model,
        content=text
    )
    return embedding_response['embedding']
