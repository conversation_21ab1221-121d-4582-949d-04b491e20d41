import os

import numpy as np
import voyageai
from dotenv import load_dotenv

from pyrpc.utils.aws_secret import get_secret_value

load_dotenv(".env")

import logging

logging.basicConfig(level=logging.INFO)


class VoyageAIAdapter:
    def __init__(self):
        # Set your API key
        self.client = voyageai.Client(api_key=os.getenv('VOYAGEAI_API_KEY') or get_secret_value("VOYAGEAI_API_KEY"))

    def embedding_dimension(self):
        _e = self.client.embed('LENGTH TEST', model=os.getenv('VOYAGEAI_MODEL') or get_secret_value("VOYAGEAI_MODEL"))
        return len(_e.embeddings[0])

    def embed(self, text, num_retries=5):
        while num_retries > 0:
            try:
                # Request an embedding for the input text using the text-embedding-ada-002 model
                response = self.client.embed(text, model=os.getenv('VOYAGEAI_MODEL') or get_secret_value("VOYAGEAI_MODEL"))

                # Extract the embedding vector from the response
                embedding_vector = np.array(response.embeddings[0])

                return embedding_vector
            except Exception as e:
                num_retries -= 1
                logging.error(f"An error occurred: {e}, retrying (attempt {num_retries})")
                return None

        logging.error(f'Could not embed text: {text}, max reties reached')
        return None

