import logging
import os
import random
import time

import instructor
import numpy as np
import openai
from openai import OpenAI
from pydantic import BaseModel

from pyrpc.utils.aws_secret import get_secret_value

logging.basicConfig(level=logging.INFO)

fallback_models = [
    'o3-mini',
    'o4-mini',
    'gpt-4.1-nano',
    'gpt-4.1-mini'
]

context_limit = {
    'gpt-4.1-nano': 1047576,
    'o3-mini': 200000,
    'o4-mini': 200000,
    'gpt-4.1-mini': 1047576,
}

model = fallback_models[0]


def retry_with_exponential_backoff(
        func=None,
        *,
        initial_delay=1,
        exponential_base=2,
        jitter=False,
        max_retries=3,
        errors=Exception
):
    def decorator(f):
        def wrapper(*args, **kwargs):
            global model

            while True:
                delay = initial_delay
                for attempt in range(max_retries):
                    try:
                        return f(*args, **kwargs)
                    except errors as e:
                        sleep_time = delay * (exponential_base ** attempt)
                        if jitter:
                            sleep_time *= random.uniform(0.5, 1.5)
                        logging.warning(f"Retrying after error: {e}. Sleeping for {sleep_time:.2f}s")
                        time.sleep(sleep_time)

                model_index = fallback_models.index(model)
                if model_index < len(fallback_models) - 1:
                    model_index += 1
                    model = fallback_models[model_index]
                    logging.warning(f"Retrying with model: {model}")
                else:
                    # Revert to the original top model, and bail, we failed
                    model = fallback_models[0]
                    break

            return None

        return wrapper

    return decorator(func) if func else decorator


class OpenAIAdapter:

    def __init__(self):
        global model

        openai_api_key = (os.getenv("OPENAI_API_KEY_JUMBO_CHUNK") or
                          get_secret_value("OPENAI_API_KEY_JUMBO_CHUNK") or
                          os.getenv("OPENAI_API_KEY") or get_secret_value("OPENAI_API_KEY"))

        # Manually force-set the env var for AWS environments
        os.environ["OPENAI_API_KEY"] = openai_api_key

        openai.api_key = openai_api_key

        self.client = OpenAI(
            # This is the default and can be omitted
            api_key=openai_api_key,
        )

        self.instructor_client = instructor.from_provider(f'openai/{model}')

    def get_context_limit(self):
        global context_limit
        return context_limit[model]

    def embed(self, text, model="text-embedding-ada-002"):
        try:
            # Request an embedding for the input text using the text-embedding-ada-002 model
            response = self.client.embeddings.create(
                input=text,
                model=model
            )

            # Extract the embedding vector from the response
            embedding_vector = np.array(response.data[0].embedding)

            return embedding_vector
        except Exception as e:
            logging.error("An error occurred:", e)
            return None

    def call_openai_api(self, prompt, response_model, max_output_tokens):
        global model

        return self.instructor_client.chat.completions.create(
            messages=[{'role': 'user', 'content': prompt}],
            model=model,
            response_model=response_model,
            max_completion_tokens=max_output_tokens)

    @retry_with_exponential_backoff
    def generate(self, prompt, response_model: BaseModel, max_output_tokens=32 * 1024):
        response = self.call_openai_api(prompt, response_model, max_output_tokens)
        if response is None or response.is_empty():
            raise Exception('Invalid response: No text chunks were found')

        return response