import json
import logging
import os
import pickle
import sys
from io import BytesIO
from typing import Dict, List

import requests

from pyrpc.context_based_doc_chunking.services.llm.openai_adapter import OpenAIAdapter
from pyrpc.context_based_doc_chunking.services.redis_cache import RedisCache
from pyrpc.context_based_doc_chunking.services.unstructured_adapter import UnstructuredAdapter
from pyrpc.utils.aws_secret import get_secret_value
from pyrpc.utils.base_url import get_base_url
from pyrpc.utils.html2text import pack_strings
from pyrpc.utils.s3 import write_chunks_to_s3

logging.basicConfig(level=logging.INFO)

redis_cache = RedisCache()

unstrc = UnstructuredAdapter()

llm = OpenAIAdapter()
llm_context_limit = llm.get_context_limit()

DEBUG_URL_OVERRIDE = os.getenv('DEBUG_URL_OVERRIDE', None)


def process_raw_file(**kwargs) -> dict:
    """
    Send unstructred file elements off to NodeJS service for processing
    """

    document_id = kwargs.get('doc_id', None)
    elements = kwargs.get('partition_result', None)

    logging.info(f'\nProcessing raw file for {document_id}')

    # TODO DORON 220425 this is ugly, we need to make sure we use the same types
    def _convert_format(c):
        c['pageContent'] = c['text']
        c['id'] = f"{c['element_id']}"
        # NOTE! process raw files expects a string in metadata
        c['metadata'] = f"{c['metadata']}"
        del c['text']
        del c['element_id']
        return c

    # Convert the elements to a list of dictionaries
    processed_elements = [_convert_format(c) for c in elements]

    api_secret = os.getenv("CHUNKING_API_SECRET") or get_secret_value("CHUNKING_API_SECRET")
    if not api_secret:
        raise RuntimeError("Missing CHUNKING_API_SECRET environment variable")

    url = f"{get_base_url(DEBUG_URL_OVERRIDE)}/api/process-raw-file"

    headers = {
        "Authorization": f"Bearer {api_secret}",
        "Content-Type": "application/json"
    }

    elements_path = write_chunks_to_s3(processed_elements,
                                       os.getenv("AWS_S3_BUCKET_NAME") or get_secret_value("AWS_S3_BUCKET_NAME"),
                                       document_id)
    payload = {
        "documentId": document_id,
        "elementsPath": elements_path,
    }

    resp = requests.post(url, headers=headers, json=payload)
    if resp.status_code != 200:
        logging.error(f'process_raw_file failed for {document_id} with {resp.status_code}: {resp.text}')
    else:
        logging.info(f'\nProcessed raw file for {document_id} and posted results')

    return resp.json()


class LenientJSONDecoder(json.JSONDecoder):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("strict", False)
        super().__init__(*args, **kwargs)


from pydantic import BaseModel


class GroupedChunks(BaseModel):
    groups: Dict[str, List[Dict[str, str]]]

    def is_empty(self):
        return len(self.groups) == 0


class DocContext(BaseModel):
    text: str

    def is_empty(self):
        return len(self.text) == 0


def generate_doc_context(prompt: str, response_model):
    response = llm.generate(prompt, response_model=response_model)
    if response is not None and response.text is not None and len(response.text.strip()) > 0:
        return response.text

    return ''


def process_chunks(current_context_grp,
                   iteration,
                   jchunks):
    global llm

    try:
        _pc = '\n-'.join(set(current_context_grp))
        prompt = f"""
                        You are provided by a list of texts.
                        Please group the following list of texts based on mutual information. 
                        Every text from the list must be included in one of the groups. 
                        Keep the full text of each line in the output JSON.
                        
                        The output JSON must follow the following structure:
                        {{
                            "groups": [
                                "group-1": ["text1", "text2"],
                                "group-2": ["text3", "text4"]
                                "group-3": ["text5", "text6"]
                            ]
                        }}
    
                        The chunks are:
                        -{_pc}
                    """

        response = llm.generate(prompt,
                                response_model=GroupedChunks,
                                max_output_tokens=max(int((len(prompt) / 4) * 2), 32 * 1024))

        groups = response.groups

        if groups is None:
            # If we failed grouping we'll take the original chunks as groups
            _d = {f'group-{k}_{iteration}': v for k, v in zip(range(len(current_context_grp)), current_context_grp)}
        else:
            _d = {f'{k}_{iteration}': list(set([vi['text'] for vi in v])) for k, v in groups.items()}

        jchunks = {**jchunks, **_d}

        return jchunks
    except Exception as e:
        logging.error(f"Unexpected error in _process_chunks: {str(e)}")
        logging.error(f"Full error details: {sys.exc_info()}")
        return jchunks


def to_blob(data: dict[str, int] | bytes) -> BytesIO:
    if isinstance(data, dict):
        data: bytes = bytes(data[str(i)] for i in range(len(data)))
        return BytesIO(data)
    elif isinstance(data, bytes):
        return BytesIO(data)

    raise TypeError(f'Expected data to be either dict[str, int] or bytes, however got {type(data)}')


def get_file_contents(document_id: str) -> tuple[dict, bytes]:
    """
    Calls GET /api/get-file-contents?documentId=<document_id>
    using Bearer auth from CHUNKING_API_SECRET env var,
    and returns (metadata, file_bytes).
    """
    logging.info(f'\nGetting file contents for {document_id}')

    api_secret = os.getenv("CHUNKING_API_SECRET") or get_secret_value("CHUNKING_API_SECRET")
    if not api_secret:
        raise RuntimeError("Missing CHUNKING_API_SECRET environment variable")

    url = f"{get_base_url(DEBUG_URL_OVERRIDE)}/api/get-file-contents"

    headers = {
        "Authorization": f"Bearer {api_secret}"
    }
    resp = requests.get(url, params={"documentId": document_id}, headers=headers)
    resp.raise_for_status()

    payload = resp.json()
    metadata = payload.get("contentType", {})
    file_bytes = to_blob(payload.get("body"))

    logging.info(f'\nGot file contents for {document_id}')

    return metadata, file_bytes


def precompute_context_groups(chunks, context_limit):
    context_tokens_cnt = 0
    current_context_grp = []
    iteration = 0
    current_chunk = 0

    # Get the current chunk
    chunk = chunks[current_chunk]
    context_tokens_cnt += int(len(chunk) / 4 + 0.5)

    context_groups = []

    while True:
        if context_tokens_cnt > int(0.75 * context_limit):
            context_groups.append(current_context_grp)

            # Advance to next iteration
            iteration += 1

            # Reset next group
            context_tokens_cnt = 0
            current_context_grp = []
        else:
            current_context_grp.append(chunk)
            current_chunk += 1

        if current_chunk >= len(chunks):
            break

        # Get the current chunk
        chunk = chunks[current_chunk]
        context_tokens_cnt += int(len(chunk) / 4 + 0.5)

    if len(current_context_grp) == len(chunks):
        context_groups.append(current_context_grp)

    return context_groups


def concurrently_run(func, args_list, max_workers=1):
    results = []
    from concurrent.futures import ThreadPoolExecutor, as_completed
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # submit all tasks
        futures = [executor.submit(func, *args) for args in args_list]
        # collect results as they complete
        for future in as_completed(futures):
            try:
                results.append(future.result())
            except Exception as exc:
                results.append(f"Generated an exception: {exc}")

    return results


def raw_chunk(doc_id):
    logging.info(f'\nRaw chunking document: {doc_id}')

    metadata, file_bytes = get_file_contents(doc_id)
    partition_result = unstrc.partition(doc_id, metadata, file_bytes)
    elements = [e['text'] for e in partition_result]

    return elements, partition_result


def intelligent_pre_chunk_raw(elements):
    # Create pre-chunks intelligently
    return unstrc.intelligent_pre_chunking(elements)

def gen_global_context(chunks):
    # Create a global context to be added to each chunk using LLM
    doc_context_prompt = """
        You are a talented financial analyst. 
        Summarize the following text into a short and descriptive title.
        Output the result as plain text
        
        Result:
            {}
    """

    packed_chunks = pack_strings(chunks, max_len=llm_context_limit)

    tasks = []

    for pchk in packed_chunks:
        tasks.append((doc_context_prompt.format(pchk), DocContext))

    doc_contexts = concurrently_run(generate_doc_context, tasks, max_workers=os.cpu_count())

    if len(doc_contexts) > 1:
        _joined_doc_contexts = '.\n'.join(doc_contexts)
        response = llm.generate(doc_context_prompt.format(_joined_doc_contexts), response_model=DocContext)
        doc_context = response.text
    elif len(doc_contexts) == 1:
        doc_context = doc_contexts[0]
    else:
        doc_context = ''

    logging.debug(f'Doc ctx: \n\t"{doc_context}"')

    return doc_context


def chunker(doc_id: str, context_limit=32 * 1024):
    """
    Chunk a document by doc_id. If blob and file_type are provided, use them directly and do not call get_file_contents.
    If either blob or file_type is missing, fallback to get_file_contents(doc_id).
    """
    global unstrc, llm, llm_context_limit

    # Use unstr to do a first-pass chunking
    elements, partition_result = raw_chunk(doc_id)

    # Send the raw chunks to the NodeJS
    process_raw_file(doc_id=doc_id, partition_result=partition_result)

    if elements is not None:
        redis_cache.cache_file(doc_id, pickle.dumps(elements))
    else:
        return None, None, None

    # Read chunking mode from env, and default to JUMBO_AND_NORMAL if undefined
    _chunking_mode = os.getenv('CHUNKING_MODE', None)
    if _chunking_mode is None:
        _chunking_mode = 'JUMBO_AND_NORMAL'

    if _chunking_mode == 'NORMAL_ONLY':
        # Skip all jumbo chunks creation, return NORMAL only
        if partition_result is None:
            raise Exception('No partition result')

        return [{'index': i, 'text': e, 'element_id': i, 'metadata': []} for e, i in
                zip(elements, range(len(elements)))], '-'

    # Create pre-chunks intelligently
    chunks = intelligent_pre_chunk_raw(elements)

    # Compute the overall doc context (i.e. "semantic title")
    doc_context = gen_global_context(chunks)

    context_groups = precompute_context_groups(chunks, context_limit)

    tasks = []
    for i, cgrp in enumerate(context_groups):
        tasks.append((
            cgrp,
            i,
            {}
        ))

    multiple_jchunks = concurrently_run(process_chunks, tasks, max_workers=os.cpu_count())

    jchunks = {k: v for d in multiple_jchunks for k, v in d.items()}

    logging.info(f'\nPre-chunking document: {doc_id} done')

    return jchunks, doc_context
