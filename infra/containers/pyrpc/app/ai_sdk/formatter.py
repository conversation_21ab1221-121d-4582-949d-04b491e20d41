"""
Adapted from https://sdk.vercel.ai/docs/ai-sdk-ui/stream-protocol#data-stream-protocol
TODO: it's currently just simple object, we should have pydantic models.
"""

import json

from ai_sdk.types import LanguageModelV1Source

def text(content: str) -> str:
    """
    Generates a text part for the data stream protocol.
    
    Args:
        content (str): The text content to be included in the part.
        
    Returns:
        str: A formatted text part string.
    """
    return f'0:{json.dumps(content)}\n'

def reasoning(reasoning: str) -> str:
    """
    Generates a reasoning part for the data stream protocol.
    
    Args:
        reasoning (str): The reasoning content to be included in the part.
        
    Returns:
        str: A formatted reasoning part string.
    """
    return f'g:{json.dumps(reasoning)}\n'

def redacted_reasoning(data: str) -> str:
    """
    Generates a redacted reasoning part for the data stream protocol.
    
    Args:
        data (str): The redacted reasoning data to be included in the part.
        
    Returns:
        str: A formatted redacted reasoning part string.
    """
    return f'i:{json.dumps({"data": data})}\n'

def reasoning_signature(signature: str) -> str:
    """
    Generates a reasoning signature part for the data stream protocol.
    
    Args:
        signature (str): The reasoning signature to be included in the part.
        
    Returns:
        str: A formatted reasoning signature part string.
    """
    return f'j:{json.dumps({"signature": signature})}\n'

def source(source: LanguageModelV1Source) -> str:
    """
    Generates a source part for the data stream protocol.
    
    Args:
        source (LanguageModelV1Source): A LanguageModelV1Source object containing source information.
        
    Returns:
        str: A formatted source part string.
    """
    return f'h:{source.json()}\n'

def data(data: list) -> str:
    """
    Generates a data part for the data stream protocol.
    
    Args:
        data (list): A list of JSON-serializable objects to be included in the part.
        
    Returns:
        str: A formatted data part string.
    """
    return f'2:{json.dumps(data)}\n'

def message_annotation(annotations: list) -> str:
    """
    Generates a message annotation part for the data stream protocol.
    
    Args:
        annotations (list): A list of JSON-serializable annotation objects.
        
    Returns:
        str: A formatted message annotation part string.
    """
    return f'8:{json.dumps(annotations)}\n'
