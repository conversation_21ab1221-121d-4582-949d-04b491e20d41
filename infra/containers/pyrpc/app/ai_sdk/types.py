from pydantic import BaseModel, Field, HttpUrl
from pydantic.json import pydantic_encoder

from typing import Annotated, Any, Dict, List, Literal, Optional, Union

class ProviderMetadataDetail(BaseModel):
    position: Optional[int] = None
    snippet: Optional[str] = None
    confidence: Optional[float] = None

class LanguageModelV1Source(BaseModel):
    sourceType: Literal['url']
    id: str
    url: HttpUrl
    title: Optional[str] = None
    providerMetadata: Optional[ProviderMetadataDetail] = None

class ConversationTextPart(BaseModel):
    type: Literal["text"]
    text: str


class ConversationReasoningDetail(BaseModel):
    type: Literal["text"]
    text: str

class ConversationReasoningPart(BaseModel):
    type: Literal["reasoning"]
    reasoning: str
    details: List[ConversationReasoningDetail]

class ConversationSourcePart(BaseModel):
    type: Literal["source"]
    source: LanguageModelV1Source


ConversationMessagePartAI = Union[
    ConversationTextPart,
    ConversationReasoningPart,
    ConversationSourcePart,
]

class ConversationMessageUser(BaseModel):
    role: Literal["user"]
    content: str
    parts: List[ConversationTextPart]

class ConversationMessageAI(BaseModel):
    role: Literal["assistant"]
    content: str
    parts: List[ConversationMessagePartAI]

ConversationMessage = Annotated[
    Union[ConversationMessageUser, ConversationMessageAI],
    Field(discriminator='role')
]


class Conversation(BaseModel):
    id: str
    messages: List[ConversationMessage]

class ConversationIncomintMessage(BaseModel):
    id: str
    latestMessage: ConversationMessageUser
