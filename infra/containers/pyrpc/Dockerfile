FROM python:3

# Change the working directory to the `app` directory
WORKDIR /code
COPY ./requirements.txt /code/requirements.txt
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --upgrade -r /code/requirements.txt

COPY ./app /code/app

# Run with uvicorn
WORKDIR /code/app
RUN python -m nltk.downloader punkt_tab
CMD ["python", "-m", "gunicorn", "-k", "uvicorn.workers.UvicornWorker", "pyrpc.index:app", "--bind", "0.0.0.0:80", "--timeout", "1200", "-w", "20"]
