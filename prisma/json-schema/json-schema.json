{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"Session": {"type": "object", "properties": {"id": {"type": "string"}, "sessionToken": {"type": "string"}, "expires": {"type": "string", "format": "date-time"}, "user": {"$ref": "#/definitions/User"}}}, "Address": {"type": "object", "properties": {"id": {"type": "string"}, "street": {"type": ["string", "null"]}, "city": {"type": ["string", "null"]}, "state": {"type": ["string", "null"]}, "zip": {"type": ["string", "null"]}, "orgs": {"type": "array", "items": {"$ref": "#/definitions/Org"}}}}, "Integration": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "type": {"type": "string", "enum": ["Plaid", "Quickbooks", "Xero", "Stripe", "Square", "Shopify", "Amazon", "Meta", "Google"]}, "status": {"type": "string", "default": "Disconnected", "enum": ["Pending", "Connected", "Disconnected"]}, "createdBy": {"$ref": "#/definitions/User"}, "org": {"$ref": "#/definitions/Org"}}}, "Org": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdById": {"type": "string"}, "incorporatedAt": {"type": ["string", "null"], "format": "date-time"}, "type": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Other"]}, "corpType": {"type": "string", "enum": ["LLC", "CCorp", "SCorp", "NonProfit", "Partnership", "SoleProprietorship"]}, "ein": {"type": ["string", "null"]}, "bio": {"type": ["string", "null"]}, "clerkId": {"type": ["string", "null"]}, "webhookSecret": {"type": ["string", "null"]}, "lenders": {"type": "array", "items": {"$ref": "#/definitions/Org"}}, "borrowers": {"type": "array", "items": {"$ref": "#/definitions/Org"}}, "address": {"anyOf": [{"$ref": "#/definitions/Address"}, {"type": "null"}]}, "users": {"type": "array", "items": {"$ref": "#/definitions/UserOrg"}}, "Integration": {"type": "array", "items": {"$ref": "#/definitions/Integration"}}, "Document": {"type": "array", "items": {"$ref": "#/definitions/Document"}}, "Conversation": {"type": "array", "items": {"$ref": "#/definitions/Conversation"}}, "ChatMessage": {"type": "array", "items": {"$ref": "#/definitions/ChatMessage"}}, "DocumentChunk": {"type": "array", "items": {"$ref": "#/definitions/DocumentChunk"}}, "DocumentEdit": {"type": "array", "items": {"$ref": "#/definitions/DocumentEdit"}}, "Response": {"type": "array", "items": {"$ref": "#/definitions/Response"}}, "ResponseContent": {"type": "array", "items": {"$ref": "#/definitions/ResponseContent"}}, "Question": {"type": "array", "items": {"$ref": "#/definitions/Question"}}, "QuestionContent": {"type": "array", "items": {"$ref": "#/definitions/QuestionContent"}}, "DDQSummary": {"type": "array", "items": {"$ref": "#/definitions/DDQSummary"}}, "DDQMetadata": {"type": "array", "items": {"$ref": "#/definitions/DDQMetadata"}}, "PluginResponse": {"type": "array", "items": {"$ref": "#/definitions/PluginResponse"}}, "AzureDrive": {"type": "array", "items": {"$ref": "#/definitions/AzureDrive"}}, "egnyteAccessToken": {"anyOf": [{"$ref": "#/definitions/EgnyteAccessToken"}, {"type": "null"}]}, "azureAccessToken": {"anyOf": [{"$ref": "#/definitions/AzureAccessToken"}, {"type": "null"}]}, "AzureSubscription": {"type": "array", "items": {"$ref": "#/definitions/AzureSubscription"}}, "Tag": {"type": "array", "items": {"$ref": "#/definitions/Tag"}}, "TagEntityConnection": {"type": "array", "items": {"$ref": "#/definitions/TagEntityConnection"}}, "ChatMessageFeedback": {"type": "array", "items": {"$ref": "#/definitions/ChatMessageFeedback"}}}}, "EgnyteAccessToken": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "accessToken": {"type": "string"}, "expiresAt": {"type": "string", "format": "date-time"}, "org": {"anyOf": [{"$ref": "#/definitions/Org"}, {"type": "null"}]}}}, "AzureAccessToken": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "accessToken": {"type": "string"}, "refreshToken": {"type": "string"}, "expiresAt": {"type": "string", "format": "date-time"}, "org": {"anyOf": [{"$ref": "#/definitions/Org"}, {"type": "null"}]}}}, "AzureDrive": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": ["string", "null"]}, "azureId": {"type": "string"}, "deltaLink": {"type": ["string", "null"]}, "webUrl": {"type": ["string", "null"]}, "driveType": {"type": ["string", "null"]}, "org": {"$ref": "#/definitions/Org"}, "subscriptions": {"type": "array", "items": {"$ref": "#/definitions/AzureSubscription"}}}}, "AzureSubscription": {"type": "object", "properties": {"id": {"type": "string"}, "org": {"$ref": "#/definitions/Org"}, "AzureDrive": {"anyOf": [{"$ref": "#/definitions/AzureDrive"}, {"type": "null"}]}, "subscriptionId": {"type": "string"}, "changeType": {"type": "string"}, "clientState": {"type": ["string", "null"]}, "notificationUrl": {"type": "string"}, "lifecycleNotificationUrl": {"type": "string"}, "expirationDateTime": {"type": "string", "format": "date-time"}, "creatorId": {"type": ["string", "null"]}, "latestSupportedTlsVersion": {"type": ["string", "null"]}, "encryptionCertificate": {"type": ["string", "null"]}, "encryptionCertificateId": {"type": ["string", "null"]}, "includeResourceData": {"type": ["boolean", "null"]}, "notificationContentType": {"type": ["string", "null"]}}}, "Social": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["X", "LinkedIn", "Facebook", "Instagram", "TikTok", "YouTube", "Website"]}, "url": {"type": "string"}, "user": {"$ref": "#/definitions/User"}}}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "clerkId": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "email": {"type": "string"}, "emailVerified": {"type": ["string", "null"], "format": "date-time"}, "image": {"type": ["string", "null"]}, "role": {"type": "string", "default": "Member", "enum": ["Member", "Admin", "SuperAdmin"]}, "title": {"type": "string", "enum": ["CEO", "CFO", "COO", "CTO", "CMO", "CSO", "CIO", "Founder", "BoardMember", "Other"]}, "bio": {"type": ["string", "null"]}, "socials": {"type": "array", "items": {"$ref": "#/definitions/Social"}}, "sessions": {"type": "array", "items": {"$ref": "#/definitions/Session"}}, "orgs": {"type": "array", "items": {"$ref": "#/definitions/UserOrg"}}, "Integration": {"type": "array", "items": {"$ref": "#/definitions/Integration"}}, "Document": {"type": "array", "items": {"$ref": "#/definitions/Document"}}, "Conversation": {"type": "array", "items": {"$ref": "#/definitions/Conversation"}}, "ChatMessage": {"type": "array", "items": {"$ref": "#/definitions/ChatMessage"}}, "ChatAttachment": {"type": "array", "items": {"$ref": "#/definitions/ChatAttachment"}}, "ChatParticipant": {"type": "array", "items": {"$ref": "#/definitions/ChatParticipant"}}, "DocumentEdit": {"type": "array", "items": {"$ref": "#/definitions/DocumentEdit"}}, "CreatedResponses": {"type": "array", "items": {"$ref": "#/definitions/Response"}}, "AssignedResponses": {"type": "array", "items": {"$ref": "#/definitions/Response"}}, "ResponseContent": {"type": "array", "items": {"$ref": "#/definitions/ResponseContent"}}, "Question": {"type": "array", "items": {"$ref": "#/definitions/Question"}}, "QuestionContent": {"type": "array", "items": {"$ref": "#/definitions/QuestionContent"}}, "DDQSummary": {"type": "array", "items": {"$ref": "#/definitions/DDQSummary"}}, "DDQMetadata": {"type": "array", "items": {"$ref": "#/definitions/DDQMetadata"}}, "PluginResponse": {"type": "array", "items": {"$ref": "#/definitions/PluginResponse"}}, "ChatMessageFeedback": {"type": "array", "items": {"$ref": "#/definitions/ChatMessageFeedback"}}}}, "UserOrg": {"type": "object", "properties": {"user": {"$ref": "#/definitions/User"}, "org": {"$ref": "#/definitions/Org"}}}, "Document": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "description": "@zod.min(1, { message: \"Name is required\" }).max(255, { message: \"Name must be less than 255 characters\" }  )"}, "title": {"type": ["string", "null"]}, "url": {"type": "string", "description": "@zod.min(1, { message: \"URL is required\" })"}, "type": {"type": "string", "enum": ["FOLDER", "PDF", "DOC", "DOCX", "XLS", "XLSX", "CSV", "TXT", "IMG", "PPTX", "OTHER"]}, "size": {"type": "integer"}, "plainTextContents": {"type": ["string", "null"]}, "status": {"type": "string", "default": "PENDING", "enum": ["PENDING", "PROCESSING", "READY", "ERROR", "EXTERNAL"]}, "dueDate": {"type": ["string", "null"], "format": "date-time"}, "ddqStatus": {"type": ["string", "null"], "enum": ["PENDING", "REVIEW", "APPROVED"]}, "source": {"type": "string", "default": "LOCAL", "enum": ["LOCAL", "EGNYTE", "INTRALINKS", "SHAREPOINT"]}, "org": {"$ref": "#/definitions/Org"}, "chunks": {"type": "array", "items": {"$ref": "#/definitions/DocumentChunk"}}, "edits": {"type": "array", "items": {"$ref": "#/definitions/DocumentEdit"}}, "DDQSummary": {"anyOf": [{"$ref": "#/definitions/DDQSummary"}, {"type": "null"}]}, "DDQMetadata": {"anyOf": [{"$ref": "#/definitions/DDQMetadata"}, {"type": "null"}]}, "htmlContents": {"type": ["string", "null"]}, "jsonContents": {"type": ["number", "string", "boolean", "object", "array", "null"]}, "summary": {"type": ["string", "null"]}, "azureItemId": {"type": ["string", "null"]}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "responses": {"type": "array", "items": {"$ref": "#/definitions/DocumentResponses"}}, "PluginResponse": {"type": "array", "items": {"$ref": "#/definitions/PluginResponse"}}, "tags": {"type": "array", "items": {"$ref": "#/definitions/Tag"}}, "TagEntityConnection": {"type": "array", "items": {"$ref": "#/definitions/TagEntityConnection"}}}}, "DocumentEdit": {"type": "object", "properties": {"id": {"type": "string"}, "org": {"$ref": "#/definitions/Org"}, "document": {"$ref": "#/definitions/Document"}, "createdBy": {"$ref": "#/definitions/User"}, "content": {"type": ["number", "string", "boolean", "object", "array", "null"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "DocumentChunk": {"type": "object", "properties": {"id": {"type": "string"}, "org": {"$ref": "#/definitions/Org"}, "document": {"$ref": "#/definitions/Document"}, "content": {"type": "string"}, "metadata": {"type": ["number", "string", "boolean", "object", "array", "null"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Tag": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "summary": {"type": "string"}, "color": {"type": "string"}, "org": {"$ref": "#/definitions/Org"}, "parent": {"anyOf": [{"$ref": "#/definitions/Tag"}, {"type": "null"}]}, "children": {"type": "array", "items": {"$ref": "#/definitions/Tag"}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "documents": {"type": "array", "items": {"$ref": "#/definitions/Document"}}, "conversations": {"type": "array", "items": {"$ref": "#/definitions/Conversation"}}, "questions": {"type": "array", "items": {"$ref": "#/definitions/Question"}}, "responses": {"type": "array", "items": {"$ref": "#/definitions/Response"}}, "TagEntityConnection": {"type": "array", "items": {"$ref": "#/definitions/TagEntityConnection"}}}}, "TagEntityConnection": {"type": "object", "properties": {"id": {"type": "string"}, "connectionReason": {"type": "string"}, "org": {"$ref": "#/definitions/Org"}, "tag": {"$ref": "#/definitions/Tag"}, "document": {"anyOf": [{"$ref": "#/definitions/Document"}, {"type": "null"}]}, "question": {"anyOf": [{"$ref": "#/definitions/Question"}, {"type": "null"}]}, "response": {"anyOf": [{"$ref": "#/definitions/Response"}, {"type": "null"}]}, "conversation": {"anyOf": [{"$ref": "#/definitions/Conversation"}, {"type": "null"}]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "ChatAttachment": {"type": "object", "properties": {"id": {"type": "string"}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "size": {"type": "integer"}, "type": {"type": ["string", "null"]}, "url": {"type": "string"}, "preview": {"type": ["string", "null"]}, "chatMessage": {"anyOf": [{"$ref": "#/definitions/ChatMessage"}, {"type": "null"}]}}}, "ChatMessageFeedback": {"type": "object", "properties": {"id": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "org": {"$ref": "#/definitions/Org"}, "chatMessage": {"anyOf": [{"$ref": "#/definitions/ChatMessage"}, {"type": "null"}]}, "createdBy": {"$ref": "#/definitions/User"}, "type": {"type": "string", "enum": ["GOOD", "BAD"]}, "feedback": {"type": ["string", "null"]}}}, "ChatMessage": {"type": "object", "properties": {"id": {"type": "string"}, "org": {"$ref": "#/definitions/Org"}, "seq": {"type": "integer"}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "body": {"type": "string"}, "contentType": {"type": ["string", "null"]}, "status": {"type": "string", "default": "READY", "enum": ["RETRIEVING", "GENERATING_CITATIONS", "VALIDATING_CITATIONS", "GENERATING_ANSWER", "RETRY", "ERROR", "READY"]}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/ChatAttachment"}}, "conversation": {"anyOf": [{"$ref": "#/definitions/Conversation"}, {"type": "null"}]}, "feedback": {"anyOf": [{"$ref": "#/definitions/ChatMessageFeedback"}, {"type": "null"}]}}}, "ChatParticipant": {"type": "object", "properties": {"id": {"type": "string"}, "user": {"$ref": "#/definitions/User"}, "conversation": {"anyOf": [{"$ref": "#/definitions/Conversation"}, {"type": "null"}]}, "status": {"type": "string", "default": "ONLINE", "enum": ["ONLINE", "OFFLINE", "BUSY", "AWAY"]}}}, "Conversation": {"type": "object", "properties": {"id": {"type": "string"}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "org": {"$ref": "#/definitions/Org"}, "messages": {"type": "array", "items": {"$ref": "#/definitions/ChatMessage"}}, "participants": {"type": "array", "items": {"$ref": "#/definitions/ChatParticipant"}}, "tags": {"type": "array", "items": {"$ref": "#/definitions/Tag"}}, "TagEntityConnection": {"type": "array", "items": {"$ref": "#/definitions/TagEntityConnection"}}}}, "ResponseContent": {"type": "object", "properties": {"id": {"type": "string"}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "org": {"$ref": "#/definitions/Org"}, "response": {"anyOf": [{"$ref": "#/definitions/Response"}, {"type": "null"}]}, "content": {"type": ["number", "string", "boolean", "object", "array", "null"]}}}, "Response": {"type": "object", "properties": {"id": {"type": "string"}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "documents": {"type": "array", "items": {"$ref": "#/definitions/DocumentResponses"}}, "questions": {"type": "array", "items": {"$ref": "#/definitions/Question"}}, "org": {"$ref": "#/definitions/Org"}, "responseContents": {"type": "array", "items": {"$ref": "#/definitions/ResponseContent"}}, "status": {"type": "string", "default": "DRAFT", "enum": ["DRAFT", "PENDING_APPROVAL", "APPROVED", "REJECTED"]}, "assignedTo": {"anyOf": [{"$ref": "#/definitions/User"}, {"type": "null"}]}, "category": {"type": "string", "default": "OTHER", "enum": ["DATA_ASSURANCE", "STANDARDS_AND_FRAMEWORKS", "INVESTMENT_PROCESS", "OTHER"]}, "tags": {"type": "array", "items": {"$ref": "#/definitions/Tag"}}, "PluginResponse": {"type": "array", "items": {"$ref": "#/definitions/PluginResponse"}}, "TagEntityConnection": {"type": "array", "items": {"$ref": "#/definitions/TagEntityConnection"}}}}, "DocumentResponses": {"type": "object", "properties": {"document": {"$ref": "#/definitions/Document"}, "response": {"$ref": "#/definitions/Response"}}}, "QuestionContent": {"type": "object", "properties": {"id": {"type": "string"}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "org": {"$ref": "#/definitions/Org"}, "question": {"anyOf": [{"$ref": "#/definitions/Question"}, {"type": "null"}]}, "content": {"type": ["number", "string", "boolean", "object", "array", "null"]}}}, "Question": {"type": "object", "properties": {"id": {"type": "string"}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "org": {"$ref": "#/definitions/Org"}, "questionContents": {"type": "array", "items": {"$ref": "#/definitions/QuestionContent"}}, "category": {"type": "string", "default": "OTHER", "enum": ["DATA_ASSURANCE", "STANDARDS_AND_FRAMEWORKS", "INVESTMENT_PROCESS", "OTHER"]}, "response": {"anyOf": [{"$ref": "#/definitions/Response"}, {"type": "null"}]}, "tags": {"type": "array", "items": {"$ref": "#/definitions/Tag"}}, "TagEntityConnection": {"type": "array", "items": {"$ref": "#/definitions/TagEntityConnection"}}}}, "PluginResponse": {"type": "object", "properties": {"id": {"type": "string"}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "org": {"$ref": "#/definitions/Org"}, "questionSource": {"type": "string", "default": "Web", "enum": ["Web", "MSExcelPlugin", "MSWordPlugin", "MSPowerpointPlugin"]}, "response": {"$ref": "#/definitions/Response"}, "originLocation": {"type": ["number", "string", "boolean", "object", "array", "null"]}, "document": {"anyOf": [{"$ref": "#/definitions/Document"}, {"type": "null"}]}}}, "DDQSummary": {"type": "object", "properties": {"id": {"type": "string"}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "org": {"$ref": "#/definitions/Org"}, "document": {"$ref": "#/definitions/Document"}, "summary": {"type": ["number", "string", "boolean", "object", "array", "null"]}}}, "DDQMetadata": {"type": "object", "properties": {"id": {"type": "string"}, "createdBy": {"$ref": "#/definitions/User"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "org": {"$ref": "#/definitions/Org"}, "document": {"$ref": "#/definitions/Document"}, "metadata": {"type": ["number", "string", "boolean", "object", "array", "null"]}}}, "RAGResponseWithCitations": {"type": "object", "properties": {"id": {"type": "string"}, "answer": {"type": "string"}, "citations": {"type": "array", "items": {"$ref": "#/definitions/Citation"}}, "sources": {"type": "array", "items": {"$ref": "#/definitions/Source"}}}}, "Citation": {"type": "object", "properties": {"id": {"type": "string"}, "sourceId": {"type": "integer"}, "quote": {"type": "string"}, "namedEntityMentioned": {"type": "boolean"}, "metadata": {"$ref": "#/definitions/CitationMetadata"}, "RAGResponseWithCitations": {"anyOf": [{"$ref": "#/definitions/RAGResponseWithCitations"}, {"type": "null"}]}}}, "CitationMetadata": {"type": "object", "properties": {"id": {"type": "string"}, "filename": {"type": "string"}, "filetype": {"type": "string"}, "languages": {"type": "array", "items": {"type": "string"}}, "page_name": {"type": "string"}, "page_number": {"type": "integer"}, "text_as_html": {"type": "string"}, "documentId": {"type": "string"}, "fileName": {"type": "string"}, "distance": {"type": "number"}, "is_continuation": {"type": "boolean", "default": false}, "citation": {"anyOf": [{"$ref": "#/definitions/Citation"}, {"type": "null"}]}}}, "Source": {"type": "object", "properties": {"id": {"type": "string"}, "pageContent": {"type": "string"}, "fileName": {"type": ["string", "null"]}, "metadata": {"$ref": "#/definitions/SourceMetadata"}, "RAGResponseWithCitations": {"anyOf": [{"$ref": "#/definitions/RAGResponseWithCitations"}, {"type": "null"}]}}}, "SourceMetadata": {"type": "object", "properties": {"id": {"type": "string"}, "filename": {"type": "string"}, "filetype": {"type": "string"}, "languages": {"type": "array", "items": {"type": "string"}}, "page_name": {"type": "string"}, "page_number": {"type": "integer"}, "text_as_html": {"type": "string"}, "documentId": {"type": ["string", "null"]}, "fileName": {"type": ["string", "null"]}, "distance": {"type": ["number", "null"]}, "source": {"anyOf": [{"$ref": "#/definitions/Source"}, {"type": "null"}]}}}}, "type": "object", "properties": {"session": {"$ref": "#/definitions/Session"}, "address": {"$ref": "#/definitions/Address"}, "integration": {"$ref": "#/definitions/Integration"}, "org": {"$ref": "#/definitions/Org"}, "egnyteAccessToken": {"$ref": "#/definitions/EgnyteAccessToken"}, "azureAccessToken": {"$ref": "#/definitions/AzureAccessToken"}, "azureDrive": {"$ref": "#/definitions/AzureDrive"}, "azureSubscription": {"$ref": "#/definitions/AzureSubscription"}, "social": {"$ref": "#/definitions/Social"}, "user": {"$ref": "#/definitions/User"}, "userOrg": {"$ref": "#/definitions/UserOrg"}, "document": {"$ref": "#/definitions/Document"}, "documentEdit": {"$ref": "#/definitions/DocumentEdit"}, "documentChunk": {"$ref": "#/definitions/DocumentChunk"}, "tag": {"$ref": "#/definitions/Tag"}, "tagEntityConnection": {"$ref": "#/definitions/TagEntityConnection"}, "chatAttachment": {"$ref": "#/definitions/ChatAttachment"}, "chatMessageFeedback": {"$ref": "#/definitions/ChatMessageFeedback"}, "chatMessage": {"$ref": "#/definitions/ChatMessage"}, "chatParticipant": {"$ref": "#/definitions/ChatParticipant"}, "conversation": {"$ref": "#/definitions/Conversation"}, "responseContent": {"$ref": "#/definitions/ResponseContent"}, "response": {"$ref": "#/definitions/Response"}, "documentResponses": {"$ref": "#/definitions/DocumentResponses"}, "questionContent": {"$ref": "#/definitions/QuestionContent"}, "question": {"$ref": "#/definitions/Question"}, "pluginResponse": {"$ref": "#/definitions/PluginResponse"}, "dDQSummary": {"$ref": "#/definitions/DDQSummary"}, "dDQMetadata": {"$ref": "#/definitions/DDQMetadata"}, "rAGResponseWithCitations": {"$ref": "#/definitions/RAGResponseWithCitations"}, "citation": {"$ref": "#/definitions/Citation"}, "citationMetadata": {"$ref": "#/definitions/CitationMetadata"}, "source": {"$ref": "#/definitions/Source"}, "sourceMetadata": {"$ref": "#/definitions/SourceMetadata"}}}