/*
  Warnings:

  - A unique constraint covering the columns `[egnyteAccessTokenId]` on the table `Org` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Org" ADD COLUMN     "egnyteAccessTokenId" TEXT;

-- CreateTable
CREATE TABLE "EgnyteAccessToken" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "accessToken" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EgnyteAccessToken_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Org_egnyteAccessTokenId_key" ON "Org"("egnyteAccessTokenId");

-- AddForeignKey
ALTER TABLE "Org" ADD CONSTRAINT "Org_egnyteAccessTokenId_fkey" FOREIGN KEY ("egnyteAccessTokenId") REFERENCES "EgnyteAccessToken"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AlterTable
ALTER TABLE "Org" ADD COLUMN     "webhookSecret" TEXT;

-- CreateEnum
CREATE TYPE "DocumentSource" AS ENUM ('LOCAL', 'EGNYTE', 'INTRALINKS', 'SHAREPOINT');

-- AlterTable
ALTER TABLE "Document" ADD COLUMN     "source" "DocumentSource" NOT NULL DEFAULT 'LOCAL';
