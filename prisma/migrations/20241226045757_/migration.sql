/*
  Warnings:

  - You are about to drop the column `question` on the `PluginResponse` table. All the data in the column will be lost.
  - You are about to drop the column `response` on the `PluginResponse` table. All the data in the column will be lost.
  - Added the required column `originLocation` to the `PluginResponse` table without a default value. This is not possible if the table is not empty.
  - Added the required column `responseId` to the `PluginResponse` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "QuestionSource" AS ENUM ('Web', 'MSExcelPlugin', 'MSWordPlugin', 'MSPowerpointPlugin');

-- AlterTable
ALTER TABLE "PluginResponse" DROP COLUMN "question",
DROP COLUMN "response",
ADD COLUMN     "originLocation" JSONB NOT NULL,
ADD COLUMN     "questionSource" "QuestionSource" NOT NULL DEFAULT 'Web',
ADD COLUMN     "responseId" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "PluginResponse" ADD CONSTRAINT "PluginResponse_responseId_fkey" FOREIGN KEY ("responseId") REFERENCES "Response"("id") ON DELETE CASCADE ON UPDATE CASCADE;
