/*
  Warnings:

  - You are about to drop the `PlaidAccount` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `PlaidItem` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `PlaidTransaction` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `VerificationToken` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[url]` on the table `Document` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "PlaidAccount" DROP CONSTRAINT "PlaidAccount_orgId_fkey";

-- DropForeignKey
ALTER TABLE "PlaidAccount" DROP CONSTRAINT "PlaidAccount_plaidItemId_fkey";

-- DropForeignKey
ALTER TABLE "PlaidItem" DROP CONSTRAINT "PlaidItem_orgId_fkey";

-- DropForeignKey
ALTER TABLE "PlaidTransaction" DROP CONSTRAINT "PlaidTransaction_accountId_fkey";

-- DropForeignKey
ALTER TABLE "PlaidTransaction" DROP CONSTRAINT "PlaidTransaction_orgId_fkey";

-- DropForeignKey
ALTER TABLE "PlaidTransaction" DROP CONSTRAINT "PlaidTransaction_plaidItemId_fkey";

-- DropTable
DROP TABLE "PlaidAccount" CASCADE;

-- DropTable
DROP TABLE "PlaidItem" CASCADE;

-- DropTable
DROP TABLE "PlaidTransaction" CASCADE;

-- DropTable
DROP TABLE "VerificationToken" CASCADE;

-- CreateTable
CREATE TABLE "DocumentEdit" (
    "id" TEXT NOT NULL,
    "orgId" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "content" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DocumentEdit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Document_url_key" ON "Document"("url");

-- AddForeignKey
ALTER TABLE "DocumentEdit" ADD CONSTRAINT "DocumentEdit_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DocumentEdit" ADD CONSTRAINT "DocumentEdit_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DocumentEdit" ADD CONSTRAINT "DocumentEdit_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AlterEnum
ALTER TYPE "DocumentStatus" ADD VALUE 'EXTERNAL';
