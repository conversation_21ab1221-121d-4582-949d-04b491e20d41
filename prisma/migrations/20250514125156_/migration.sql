-- CreateEnum
CREATE TYPE "QuestionType" AS ENUM ('YES_NO', 'YES_NO_EXPANDED', 'MULTIPLE_CHOICE', 'FREE_TEXT');

-- AlterTable
ALTER TABLE "Question" ADD COLUMN     "type" "QuestionType" NOT NULL DEFAULT 'FREE_TEXT';

-- CreateEnum
CREATE TYPE "AnswerGenerationType" AS ENUM ('EXTRACTED', 'GENERATED');

-- AlterTable
ALTER TABLE "ResponseContent" ADD COLUMN     "answerGenerationType" "AnswerGenerationType" NOT NULL DEFAULT 'EXTRACTED';
