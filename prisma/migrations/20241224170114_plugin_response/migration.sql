-- CreateTable
CREATE TABLE "PluginResponse" (
    "id" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orgId" TEXT NOT NULL,
    "question" TEXT NOT NULL,
    "response" JSONB NOT NULL,

    CONSTRAINT "PluginResponse_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "PluginResponse" ADD CONSTRAINT "PluginResponse_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PluginResponse" ADD CONSTRAINT "PluginResponse_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE CASCADE ON UPDATE CASCADE;
