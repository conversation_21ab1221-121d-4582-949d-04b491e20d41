-- Create<PERSON><PERSON>
CREATE TYPE "ResponseCategory" AS ENUM ('DATA_ASSURANCE', 'STANDARDS_AND_FRAMEWORKS', 'INVESTMENT_PROCESS', 'OTHER');

-- AlterTable
ALTER TABLE "Response" ADD COLUMN     "category" "ResponseCategory" NOT NULL DEFAULT 'OTHER';

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "QuestionCategory" AS ENUM ('DATA_ASSURANCE', 'STANDARDS_AND_FRAMEWORKS', 'INVESTMENT_PROCESS', 'OTHER');

-- CreateTable
CREATE TABLE "QuestionContent" (
    "id" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orgId" TEXT NOT NULL,
    "questionId" TEXT NOT NULL,
    "content" JSONB,

    CONSTRAINT "QuestionContent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Question" (
    "id" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orgId" TEXT NOT NULL,
    "category" "QuestionCategory" NOT NULL DEFAULT 'OTHER',
    "responseId" TEXT,

    CONSTRAINT "Question_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "QuestionContent" ADD CONSTRAINT "QuestionContent_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QuestionContent" ADD CONSTRAINT "QuestionContent_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QuestionContent" ADD CONSTRAINT "QuestionContent_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "Question"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Question" ADD CONSTRAINT "Question_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Question" ADD CONSTRAINT "Question_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Question" ADD CONSTRAINT "Question_responseId_fkey" FOREIGN KEY ("responseId") REFERENCES "Response"("id") ON DELETE SET NULL ON UPDATE CASCADE;
