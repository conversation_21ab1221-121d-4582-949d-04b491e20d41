-- CreateEnum
CREATE TYPE "ResponseFeedbackType" AS ENUM ('GOOD', 'BAD');

-- CreateTable
CREATE TABLE "ResponseFeedback" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orgId" TEXT NOT NULL,
    "responseId" TEXT,
    "createdById" TEXT NOT NULL,
    "type" "ResponseFeedbackType" NOT NULL,
    "feedback" TEXT,
    "questionId" TEXT,
    "documentId" TEXT,

    CONSTRAINT "ResponseFeedback_pkey" PRIMARY KEY ("id")
);

-- AddFore<PERSON>Key
ALTER TABLE "ResponseFeedback" ADD CONSTRAINT "ResponseFeedback_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResponseFeedback" ADD CONSTRAINT "ResponseFeedback_responseId_fkey" FOREIGN KEY ("responseId") REFERENCES "Response"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResponseFeedback" ADD CONSTRAINT "ResponseFeedback_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResponseFeedback" ADD CONSTRAINT "ResponseFeedback_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "Question"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResponseFeedback" ADD CONSTRAINT "ResponseFeedback_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE SET NULL ON UPDATE CASCADE;
