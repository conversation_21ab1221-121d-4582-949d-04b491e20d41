/*
  Warnings:

  - You are about to drop the `_DocumentToResponse` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "_DocumentToResponse" DROP CONSTRAINT "_DocumentToResponse_A_fkey";

-- DropForeignKey
ALTER TABLE "_DocumentToResponse" DROP CONSTRAINT "_DocumentToResponse_B_fkey";

-- DropTable
DROP TABLE "_DocumentToResponse";

-- CreateTable
CREATE TABLE "DocumentResponses" (
    "documentId" TEXT NOT NULL,
    "responseId" TEXT NOT NULL,

    CONSTRAINT "DocumentResponses_pkey" PRIMARY KEY ("documentId","responseId")
);

-- AddForeignKey
ALTER TABLE "DocumentResponses" ADD CONSTRAINT "DocumentResponses_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DocumentResponses" ADD CONSTRAINT "DocumentResponses_responseId_fkey" FOREIGN KEY ("responseId") REFERENCES "Response"("id") ON DELETE CASCADE ON UPDATE CASCADE;
