-- DropFore<PERSON><PERSON><PERSON>
ALTER TABLE "ChatAttachment" DROP CONSTRAINT "ChatAttachment_chatMessageId_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "ChatMessage" DROP CONSTRAINT "ChatMessage_conversationId_fkey";

-- DropForeignKey
ALTER TABLE "ChatParticipant" DROP CONSTRAINT "ChatParticipant_conversationId_fkey";

-- DropForeignKey
ALTER TABLE "Conversation" DROP CONSTRAINT "Conversation_orgId_fkey";

-- DropForeignKey
ALTER TABLE "DDQMetadata" DROP CONSTRAINT "DDQMetadata_documentId_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "DDQMetadata" DROP CONSTRAINT "DDQMetadata_orgId_fkey";

-- DropForeign<PERSON>ey
ALTER TABLE "DDQSummary" DROP CONSTRAINT "DDQSummary_documentId_fkey";

-- DropFore<PERSON><PERSON><PERSON>
ALTER TABLE "DDQSummary" DROP CONSTRAINT "DDQSummary_orgId_fkey";

-- DropForeign<PERSON>ey
ALTER TABLE "DocumentChunk" DROP CONSTRAINT "DocumentChunk_documentId_fkey";

-- DropForeignKey
ALTER TABLE "DocumentEdit" DROP CONSTRAINT "DocumentEdit_documentId_fkey";

-- DropForeignKey
ALTER TABLE "Question" DROP CONSTRAINT "Question_orgId_fkey";

-- DropForeignKey
ALTER TABLE "Question" DROP CONSTRAINT "Question_responseId_fkey";

-- DropForeignKey
ALTER TABLE "QuestionContent" DROP CONSTRAINT "QuestionContent_questionId_fkey";

-- DropForeignKey
ALTER TABLE "ResponseContent" DROP CONSTRAINT "ResponseContent_orgId_fkey";

-- DropForeignKey
ALTER TABLE "ResponseContent" DROP CONSTRAINT "ResponseContent_responseId_fkey";

-- AddForeignKey
ALTER TABLE "DocumentEdit" ADD CONSTRAINT "DocumentEdit_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DocumentChunk" ADD CONSTRAINT "DocumentChunk_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatAttachment" ADD CONSTRAINT "ChatAttachment_chatMessageId_fkey" FOREIGN KEY ("chatMessageId") REFERENCES "ChatMessage"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatMessage" ADD CONSTRAINT "ChatMessage_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatParticipant" ADD CONSTRAINT "ChatParticipant_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResponseContent" ADD CONSTRAINT "ResponseContent_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResponseContent" ADD CONSTRAINT "ResponseContent_responseId_fkey" FOREIGN KEY ("responseId") REFERENCES "Response"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QuestionContent" ADD CONSTRAINT "QuestionContent_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "Question"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Question" ADD CONSTRAINT "Question_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Question" ADD CONSTRAINT "Question_responseId_fkey" FOREIGN KEY ("responseId") REFERENCES "Response"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DDQSummary" ADD CONSTRAINT "DDQSummary_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DDQSummary" ADD CONSTRAINT "DDQSummary_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DDQMetadata" ADD CONSTRAINT "DDQMetadata_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DDQMetadata" ADD CONSTRAINT "DDQMetadata_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE CASCADE ON UPDATE CASCADE;
