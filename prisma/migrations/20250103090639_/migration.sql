-- CreateTable
CREATE TABLE "AzureSubscription" (
    "id" TEXT NOT NULL,
    "orgId" TEXT NOT NULL,
    "azureDriveId" TEXT,
    "subscriptionId" TEXT NOT NULL,
    "changeType" TEXT NOT NULL,
    "clientState" TEXT,
    "notificationUrl" TEXT NOT NULL,
    "lifecycleNotificationUrl" TEXT NOT NULL,
    "expirationDateTime" TIMESTAMP(3) NOT NULL,
    "creatorId" TEXT,
    "latestSupportedTlsVersion" TEXT,
    "encryptionCertificate" TEXT,
    "encryptionCertificateId" TEXT,
    "includeResourceData" BOOLEAN,
    "notificationContentType" TEXT,

    CONSTRAINT "AzureSubscription_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "AzureSubscription" ADD CONSTRAINT "AzureSubscription_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AzureSubscription" ADD CONSTRAINT "AzureSubscription_azureDriveId_fkey" FOREIGN KEY ("azureDriveId") REFERENCES "AzureDrive"("id") ON DELETE SET NULL ON UPDATE CASCADE;
