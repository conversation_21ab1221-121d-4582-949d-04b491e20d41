-- CreateTable
CREATE TABLE "UserMSAddInSettings" (
    "id" TEXT NOT NULL,
    "insertTextColor" TEXT NOT NULL,
    "chatWidth" INTEGER NOT NULL,
    "chatHeight" INTEGER NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "UserMSAddInSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserMSAddInSettings_userId_key" ON "UserMSAddInSettings"("userId");

-- AddForeignKey
ALTER TABLE "UserMSAddInSettings" ADD CONSTRAINT "UserMSAddInSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
