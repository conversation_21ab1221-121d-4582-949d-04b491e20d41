-- CreateTable
CREATE TABLE "DDQSummary" (
    "id" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orgId" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,
    "summary" JSONB,

    CONSTRAINT "DDQSummary_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DDQMetadata" (
    "id" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orgId" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,
    "metadata" J<PERSON>N<PERSON>,

    CONSTRAINT "DDQMetadata_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "DDQSummary_documentId_key" ON "DDQSummary"("documentId");

-- CreateIndex
CREATE UNIQUE INDEX "DDQMetadata_documentId_key" ON "DDQMetadata"("documentId");

-- AddForeignKey
ALTER TABLE "DDQSummary" ADD CONSTRAINT "DDQSummary_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DDQSummary" ADD CONSTRAINT "DDQSummary_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DDQSummary" ADD CONSTRAINT "DDQSummary_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DDQMetadata" ADD CONSTRAINT "DDQMetadata_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DDQMetadata" ADD CONSTRAINT "DDQMetadata_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DDQMetadata" ADD CONSTRAINT "DDQMetadata_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_responseId_fkey";

-- AlterTable
ALTER TABLE "Document" DROP COLUMN "responseId";

-- CreateTable
CREATE TABLE "_DocumentToResponse" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_DocumentToResponse_AB_unique" ON "_DocumentToResponse"("A", "B");

-- CreateIndex
CREATE INDEX "_DocumentToResponse_B_index" ON "_DocumentToResponse"("B");

-- AddForeignKey
ALTER TABLE "_DocumentToResponse" ADD CONSTRAINT "_DocumentToResponse_A_fkey" FOREIGN KEY ("A") REFERENCES "Document"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_DocumentToResponse" ADD CONSTRAINT "_DocumentToResponse_B_fkey" FOREIGN KEY ("B") REFERENCES "Response"("id") ON DELETE CASCADE ON UPDATE CASCADE;
