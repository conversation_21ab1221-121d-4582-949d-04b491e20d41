/*
  Warnings:

  - A unique constraint covering the columns `[azureAccessTokenId]` on the table `Org` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Org" ADD COLUMN     "azureAccessTokenId" TEXT;

-- CreateTable
CREATE TABLE "AzureAccessToken" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "accessToken" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AzureAccessToken_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Org_azureAccessTokenId_key" ON "Org"("azureAccessTokenId");

-- AddForeignKey
ALTER TABLE "Org" ADD CONSTRAINT "Org_azureAccessTokenId_fkey" FOREIGN KEY ("azureAccessTokenId") REFERENCES "AzureAccessToken"("id") ON DELETE SET NULL ON UPDATE CASCADE;

/*
  Warnings:

  - Added the required column `refreshToken` to the `AzureAccessToken` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "AzureAccessToken" ADD COLUMN     "refreshToken" TEXT NOT NULL;

-- CreateTable
CREATE TABLE "AzureDrive" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "azureId" TEXT NOT NULL,
    "webUrl" TEXT,
    "driveType" TEXT,
    "orgId" TEXT NOT NULL,

    CONSTRAINT "AzureDrive_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "AzureDrive" ADD CONSTRAINT "AzureDrive_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AlterTable
ALTER TABLE "Document" ADD COLUMN     "azureItemId" TEXT;
