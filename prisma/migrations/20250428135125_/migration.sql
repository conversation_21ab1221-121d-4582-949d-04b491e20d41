-- CreateTable
CREATE TABLE "WorkSheet" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orgId" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,

    CONSTRAINT "WorkSheet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkSheetTable" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orgId" TEXT NOT NULL,
    "workSheetId" TEXT NOT NULL,
    "contents" JSONB,

    CONSTRAINT "WorkSheetTable_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WorkSheetTableCell" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "orgId" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "tableId" TEXT NOT NULL,
    "prompt" TEXT,
    "sheetId" TEXT NOT NULL,

    CONSTRAINT "WorkSheetTableCell_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "WorkSheet" ADD CONSTRAINT "WorkSheet_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkSheet" ADD CONSTRAINT "WorkSheet_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkSheetTable" ADD CONSTRAINT "WorkSheetTable_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkSheetTable" ADD CONSTRAINT "WorkSheetTable_workSheetId_fkey" FOREIGN KEY ("workSheetId") REFERENCES "WorkSheet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkSheetTableCell" ADD CONSTRAINT "WorkSheetTableCell_orgId_fkey" FOREIGN KEY ("orgId") REFERENCES "Org"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkSheetTableCell" ADD CONSTRAINT "WorkSheetTableCell_tableId_fkey" FOREIGN KEY ("tableId") REFERENCES "WorkSheetTable"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkSheetTableCell" ADD CONSTRAINT "WorkSheetTableCell_sheetId_fkey" FOREIGN KEY ("sheetId") REFERENCES "WorkSheet"("id") ON DELETE CASCADE ON UPDATE CASCADE;
