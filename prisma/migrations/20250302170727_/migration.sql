-- DropForeignKey
ALTER TABLE "ChatMessageFeedback" DROP CONSTRAINT "ChatMessageFeedback_chatMessageId_fkey";

-- AlterTable
ALTER TABLE "ChatMessageFeedback" ALTER COLUMN "chatMessageId" DROP NOT NULL;

-- AddFore<PERSON>Key
ALTER TABLE "ChatMessageFeedback" ADD CONSTRAINT "ChatMessageFeedback_chatMessageId_fkey" FOREIGN KEY ("chatMessageId") REFERENCES "ChatMessage"("id") ON DELETE SET NULL ON UPDATE CASCADE;
