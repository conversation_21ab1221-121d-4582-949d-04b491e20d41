import * as z from "zod"
import { SocialType } from "@prisma/client"
import { CompleteUser, RelatedUserModel } from "./index"

export const SocialModel = z.object({
  id: z.string(),
  type: z.nativeEnum(SocialType),
  url: z.string(),
  userId: z.string(),
})

export interface CompleteSocial extends z.infer<typeof SocialModel> {
  user: CompleteUser
}

/**
 * RelatedSocialModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedSocialModel: z.ZodSchema<CompleteSocial> = z.lazy(() => SocialModel.extend({
  user: RelatedUserModel,
}))
