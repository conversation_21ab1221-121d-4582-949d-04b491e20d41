import * as z from "zod"
import { DocumentChunkType } from "@prisma/client"
import { CompleteOrg, RelatedOrgModel, CompleteDocument, RelatedDocumentModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: Json } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const DocumentChunkModel = z.object({
  id: z.string(),
  orgId: z.string(),
  documentId: z.string(),
  content: z.string(),
  metadata: jsonSchema,
  chunkType: z.nativeEnum(DocumentChunkType),
  chunkIndex: z.number().int(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export interface CompleteDocumentChunk extends z.infer<typeof DocumentChunkModel> {
  org: CompleteOrg
  document: CompleteDocument
}

/**
 * RelatedDocumentChunkModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedDocumentChunkModel: z.ZodSchema<CompleteDocumentChunk> = z.lazy(() => DocumentChunkModel.extend({
  org: RelatedOrgModel,
  document: RelatedDocumentModel,
}))
