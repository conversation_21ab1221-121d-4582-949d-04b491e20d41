import * as z from "zod"
import { AnswerGenerationType } from "@prisma/client"
import { CompleteUser, RelatedUserModel, CompleteOrg, RelatedOrgModel, CompleteResponse, RelatedResponseModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: Json } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const ResponseContentModel = z.object({
  id: z.string(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  responseId: z.string(),
  answerGenerationType: z.nativeEnum(AnswerGenerationType),
  content: jsonSchema,
})

export interface CompleteResponseContent extends z.infer<typeof ResponseContentModel> {
  createdBy: CompleteUser
  org: CompleteOrg
  response?: CompleteResponse | null
}

/**
 * RelatedResponseContentModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedResponseContentModel: z.ZodSchema<CompleteResponseContent> = z.lazy(() => ResponseContentModel.extend({
  createdBy: RelatedUserModel,
  org: RelatedOrgModel,
  response: RelatedResponseModel.nullish(),
}))
