import * as z from "zod"
import { User<PERSON><PERSON>, UserTitle } from "@prisma/client"
import { CompleteSocial, RelatedSocialModel, CompleteSession, RelatedSessionModel, CompleteUserOrg, RelatedUserOrgModel, CompleteIntegration, RelatedIntegrationModel, CompleteDocument, RelatedDocumentModel, CompleteConversation, RelatedConversationModel, CompleteChatMessage, RelatedChatMessageModel, CompleteChatAttachment, RelatedChatAttachmentModel, CompleteChatParticipant, RelatedChatParticipantModel, CompleteDocumentEdit, RelatedDocumentEditModel, CompleteResponse, RelatedResponseModel, CompleteResponseContent, RelatedResponseContentModel, CompleteQuestion, RelatedQuestionModel, CompleteQuestionContent, RelatedQuestionContentModel, CompleteDDQSummary, RelatedDDQSummaryModel, CompleteDDQMetadata, RelatedDDQMetadataModel, CompletePluginResponse, RelatedPluginResponseModel, CompleteChatMessageFeedback, RelatedChatMessageFeedbackModel, CompleteUserMSAddInSettings, RelatedUserMSAddInSettingsModel, CompleteResponseFeedback, RelatedResponseFeedbackModel } from "./index"

export const UserModel = z.object({
  id: z.string(),
  clerkId: z.string().nullish(),
  name: z.string().nullish(),
  email: z.string(),
  emailVerified: z.date().nullish(),
  image: z.string().nullish(),
  role: z.nativeEnum(UserRole),
  title: z.nativeEnum(UserTitle),
  bio: z.string().nullish(),
})

export interface CompleteUser extends z.infer<typeof UserModel> {
  socials: CompleteSocial[]
  sessions: CompleteSession[]
  orgs: CompleteUserOrg[]
  Integration: CompleteIntegration[]
  Document: CompleteDocument[]
  Conversation: CompleteConversation[]
  ChatMessage: CompleteChatMessage[]
  ChatAttachment: CompleteChatAttachment[]
  ChatParticipant: CompleteChatParticipant[]
  DocumentEdit: CompleteDocumentEdit[]
  CreatedResponses: CompleteResponse[]
  AssignedResponses: CompleteResponse[]
  ResponseContent: CompleteResponseContent[]
  Question: CompleteQuestion[]
  QuestionContent: CompleteQuestionContent[]
  DDQSummary: CompleteDDQSummary[]
  DDQMetadata: CompleteDDQMetadata[]
  PluginResponse: CompletePluginResponse[]
  ChatMessageFeedback: CompleteChatMessageFeedback[]
  msAddInSettings?: CompleteUserMSAddInSettings | null
  ResponseFeedback: CompleteResponseFeedback[]
}

/**
 * RelatedUserModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedUserModel: z.ZodSchema<CompleteUser> = z.lazy(() => UserModel.extend({
  socials: RelatedSocialModel.array(),
  sessions: RelatedSessionModel.array(),
  orgs: RelatedUserOrgModel.array(),
  Integration: RelatedIntegrationModel.array(),
  Document: RelatedDocumentModel.array(),
  Conversation: RelatedConversationModel.array(),
  ChatMessage: RelatedChatMessageModel.array(),
  ChatAttachment: RelatedChatAttachmentModel.array(),
  ChatParticipant: RelatedChatParticipantModel.array(),
  DocumentEdit: RelatedDocumentEditModel.array(),
  CreatedResponses: RelatedResponseModel.array(),
  AssignedResponses: RelatedResponseModel.array(),
  ResponseContent: RelatedResponseContentModel.array(),
  Question: RelatedQuestionModel.array(),
  QuestionContent: RelatedQuestionContentModel.array(),
  DDQSummary: RelatedDDQSummaryModel.array(),
  DDQMetadata: RelatedDDQMetadataModel.array(),
  PluginResponse: RelatedPluginResponseModel.array(),
  ChatMessageFeedback: RelatedChatMessageFeedbackModel.array(),
  msAddInSettings: RelatedUserMSAddInSettingsModel.nullish(),
  ResponseFeedback: RelatedResponseFeedbackModel.array(),
}))
