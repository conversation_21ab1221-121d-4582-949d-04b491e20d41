import * as z from "zod"
import { CompleteOrg, RelatedOrgModel } from "./index"

export const AzureAccessTokenModel = z.object({
  id: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  accessToken: z.string(),
  refreshToken: z.string(),
  expiresAt: z.date(),
})

export interface CompleteAzureAccessToken extends z.infer<typeof AzureAccessTokenModel> {
  org?: CompleteOrg | null
}

/**
 * RelatedAzureAccessTokenModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedAzureAccessTokenModel: z.ZodSchema<CompleteAzureAccessToken> = z.lazy(() => AzureAccessTokenModel.extend({
  org: RelatedOrgModel.nullish(),
}))
