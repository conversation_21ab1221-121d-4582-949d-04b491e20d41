import * as z from "zod"
import { CompleteUser, RelatedUserModel, CompleteOrg, RelatedOrgModel, CompleteDocument, RelatedDocumentModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: J<PERSON> } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const DDQMetadataModel = z.object({
  id: z.string(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  documentId: z.string(),
  metadata: jsonSchema,
})

export interface CompleteDDQMetadata extends z.infer<typeof DDQMetadataModel> {
  createdBy: CompleteUser
  org: CompleteOrg
  document: CompleteDocument
}

/**
 * RelatedDDQMetadataModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedDDQMetadataModel: z.ZodSchema<CompleteDDQMetadata> = z.lazy(() => DDQMetadataModel.extend({
  createdBy: RelatedUserModel,
  org: RelatedOrgModel,
  document: RelatedDocumentModel,
}))
