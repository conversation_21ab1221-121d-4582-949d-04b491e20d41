import * as z from "zod"
import { OrgType, CorpType } from "@prisma/client"
import { CompleteAddress, RelatedAddressModel, CompleteUserOrg, RelatedUserOrgModel, CompleteIntegration, RelatedIntegrationModel, CompleteDocument, RelatedDocumentModel, CompleteConversation, RelatedConversationModel, CompleteChatMessage, RelatedChatMessageModel, CompleteDocumentChunk, RelatedDocumentChunkModel, CompleteDocumentEdit, RelatedDocumentEditModel, CompleteResponse, RelatedResponseModel, CompleteResponseContent, RelatedResponseContentModel, CompleteQuestion, RelatedQuestionModel, CompleteQuestionContent, RelatedQuestionContentModel, CompleteDDQSummary, RelatedDDQSummaryModel, CompleteDDQMetadata, RelatedDDQMetadataModel, CompletePluginResponse, RelatedPluginResponseModel, CompleteAzureDrive, RelatedAzureDriveModel, CompleteEgnyteAccessToken, RelatedEgnyteAccessTokenModel, CompleteAzureAccessToken, RelatedAzureAccessTokenModel, CompleteAzureSubscription, RelatedAzureSubscriptionModel, CompleteTag, RelatedTagModel, CompleteTagEntityConnection, RelatedTagEntityConnectionModel, CompleteChatMessageFeedback, RelatedChatMessageFeedbackModel, CompleteFund, RelatedFundModel, CompleteFundFamily, RelatedFundFamilyModel, CompleteWorkSheet, RelatedWorkSheetModel, CompleteWorkSheetTable, RelatedWorkSheetTableModel, CompleteWorkSheetTableCell, RelatedWorkSheetTableCellModel, CompleteResponseFeedback, RelatedResponseFeedbackModel, CompleteDocumentSection, RelatedDocumentSectionModel } from "./index"

export const OrgModel = z.object({
  id: z.string(),
  name: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  createdById: z.string(),
  incorporatedAt: z.date().nullish(),
  type: z.nativeEnum(OrgType),
  corpType: z.nativeEnum(CorpType),
  ein: z.string().nullish(),
  bio: z.string().nullish(),
  clerkId: z.string().nullish(),
  webhookSecret: z.string().nullish(),
  addressId: z.string().nullish(),
  egnyteAccessTokenId: z.string().nullish(),
  azureAccessTokenId: z.string().nullish(),
})

export interface CompleteOrg extends z.infer<typeof OrgModel> {
  lenders: CompleteOrg[]
  borrowers: CompleteOrg[]
  address?: CompleteAddress | null
  users: CompleteUserOrg[]
  Integration: CompleteIntegration[]
  Document: CompleteDocument[]
  Conversation: CompleteConversation[]
  ChatMessage: CompleteChatMessage[]
  DocumentChunk: CompleteDocumentChunk[]
  DocumentEdit: CompleteDocumentEdit[]
  Response: CompleteResponse[]
  ResponseContent: CompleteResponseContent[]
  Question: CompleteQuestion[]
  QuestionContent: CompleteQuestionContent[]
  DDQSummary: CompleteDDQSummary[]
  DDQMetadata: CompleteDDQMetadata[]
  PluginResponse: CompletePluginResponse[]
  AzureDrive: CompleteAzureDrive[]
  egnyteAccessToken?: CompleteEgnyteAccessToken | null
  azureAccessToken?: CompleteAzureAccessToken | null
  AzureSubscription: CompleteAzureSubscription[]
  Tag: CompleteTag[]
  TagEntityConnection: CompleteTagEntityConnection[]
  ChatMessageFeedback: CompleteChatMessageFeedback[]
  Fund: CompleteFund[]
  FundFamily: CompleteFundFamily[]
  WorkSheet: CompleteWorkSheet[]
  WorkSheetTable: CompleteWorkSheetTable[]
  WorkSheetTableCell: CompleteWorkSheetTableCell[]
  ResponseFeedback: CompleteResponseFeedback[]
  DocumentSection: CompleteDocumentSection[]
}

/**
 * RelatedOrgModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedOrgModel: z.ZodSchema<CompleteOrg> = z.lazy(() => OrgModel.extend({
  lenders: RelatedOrgModel.array(),
  borrowers: RelatedOrgModel.array(),
  address: RelatedAddressModel.nullish(),
  users: RelatedUserOrgModel.array(),
  Integration: RelatedIntegrationModel.array(),
  Document: RelatedDocumentModel.array(),
  Conversation: RelatedConversationModel.array(),
  ChatMessage: RelatedChatMessageModel.array(),
  DocumentChunk: RelatedDocumentChunkModel.array(),
  DocumentEdit: RelatedDocumentEditModel.array(),
  Response: RelatedResponseModel.array(),
  ResponseContent: RelatedResponseContentModel.array(),
  Question: RelatedQuestionModel.array(),
  QuestionContent: RelatedQuestionContentModel.array(),
  DDQSummary: RelatedDDQSummaryModel.array(),
  DDQMetadata: RelatedDDQMetadataModel.array(),
  PluginResponse: RelatedPluginResponseModel.array(),
  AzureDrive: RelatedAzureDriveModel.array(),
  egnyteAccessToken: RelatedEgnyteAccessTokenModel.nullish(),
  azureAccessToken: RelatedAzureAccessTokenModel.nullish(),
  AzureSubscription: RelatedAzureSubscriptionModel.array(),
  Tag: RelatedTagModel.array(),
  TagEntityConnection: RelatedTagEntityConnectionModel.array(),
  ChatMessageFeedback: RelatedChatMessageFeedbackModel.array(),
  Fund: RelatedFundModel.array(),
  FundFamily: RelatedFundFamilyModel.array(),
  WorkSheet: RelatedWorkSheetModel.array(),
  WorkSheetTable: RelatedWorkSheetTableModel.array(),
  WorkSheetTableCell: RelatedWorkSheetTableCellModel.array(),
  ResponseFeedback: RelatedResponseFeedbackModel.array(),
  DocumentSection: RelatedDocumentSectionModel.array(),
}))
