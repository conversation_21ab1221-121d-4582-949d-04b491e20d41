import * as z from "zod"
import { IntegrationType, IntegrationStatus } from "@prisma/client"
import { CompleteUser, RelatedUserModel, CompleteOrg, RelatedOrgModel } from "./index"

export const IntegrationModel = z.object({
  id: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  type: z.nativeEnum(IntegrationType),
  status: z.nativeEnum(IntegrationStatus),
  createdById: z.string(),
  orgId: z.string(),
})

export interface CompleteIntegration extends z.infer<typeof IntegrationModel> {
  createdBy: CompleteUser
  org: CompleteOrg
}

/**
 * RelatedIntegrationModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedIntegrationModel: z.ZodSchema<CompleteIntegration> = z.lazy(() => IntegrationModel.extend({
  createdBy: RelatedUserModel,
  org: RelatedOrgModel,
}))
