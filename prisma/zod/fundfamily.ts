import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteFund, RelatedFundModel } from "./index"

export const FundFamilyModel = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
})

export interface CompleteFundFamily extends z.infer<typeof FundFamilyModel> {
  org: CompleteOrg
  funds: CompleteFund[]
}

/**
 * RelatedFundFamilyModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedFundFamilyModel: z.ZodSchema<CompleteFundFamily> = z.lazy(() => FundFamilyModel.extend({
  org: RelatedOrgModel,
  funds: RelatedFundModel.array(),
}))
