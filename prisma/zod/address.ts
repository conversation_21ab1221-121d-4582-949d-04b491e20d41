import * as z from "zod"
import { CompleteOrg, RelatedOrgModel } from "./index"

export const AddressModel = z.object({
  id: z.string(),
  street: z.string().nullish(),
  city: z.string().nullish(),
  state: z.string().nullish(),
  zip: z.string().nullish(),
})

export interface CompleteAddress extends z.infer<typeof AddressModel> {
  orgs: CompleteOrg[]
}

/**
 * RelatedAddressModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedAddressModel: z.ZodSchema<CompleteAddress> = z.lazy(() => AddressModel.extend({
  orgs: RelatedOrgModel.array(),
}))
