import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteDocument, RelatedDocumentModel, CompleteWorkSheetTable, RelatedWorkSheetTableModel, CompleteWorkSheetTableCell, RelatedWorkSheetTableCellModel } from "./index"

export const WorkSheetModel = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  documentId: z.string(),
})

export interface CompleteWorkSheet extends z.infer<typeof WorkSheetModel> {
  org: CompleteOrg
  document: CompleteDocument
  tables: CompleteWorkSheetTable[]
  WorkSheetTableCell: CompleteWorkSheetTableCell[]
}

/**
 * RelatedWorkSheetModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedWorkSheetModel: z.ZodSchema<CompleteWorkSheet> = z.lazy(() => WorkSheetModel.extend({
  org: RelatedOrgModel,
  document: RelatedDocumentModel,
  tables: RelatedWorkSheetTableModel.array(),
  WorkSheetTableCell: RelatedWorkSheetTableCellModel.array(),
}))
