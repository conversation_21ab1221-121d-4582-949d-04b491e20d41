import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteDocument, RelatedDocumentModel, CompleteResponse, RelatedResponseModel } from "./index"

export const DocumentSectionModel = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  documentId: z.string(),
})

export interface CompleteDocumentSection extends z.infer<typeof DocumentSectionModel> {
  org: CompleteOrg
  document: CompleteDocument
  responses: CompleteResponse[]
}

/**
 * RelatedDocumentSectionModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedDocumentSectionModel: z.ZodSchema<CompleteDocumentSection> = z.lazy(() => DocumentSectionModel.extend({
  org: RelatedOrgModel,
  document: RelatedDocumentModel,
  responses: RelatedResponseModel.array(),
}))
