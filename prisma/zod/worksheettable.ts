import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteWorkSheet, RelatedWorkSheetModel, CompleteWorkSheetTableCell, RelatedWorkSheetTableCellModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: Json } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const WorkSheetTableModel = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  workSheetId: z.string(),
  contents: jsonSchema,
})

export interface CompleteWorkSheetTable extends z.infer<typeof WorkSheetTableModel> {
  org: CompleteOrg
  workSheet: CompleteWorkSheet
  cells: CompleteWorkSheetTableCell[]
}

/**
 * RelatedWorkSheetTableModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedWorkSheetTableModel: z.ZodSchema<CompleteWorkSheetTable> = z.lazy(() => WorkSheetTableModel.extend({
  org: RelatedOrgModel,
  workSheet: RelatedWorkSheetModel,
  cells: RelatedWorkSheetTableCellModel.array(),
}))
