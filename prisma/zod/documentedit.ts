import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteDocument, RelatedDocumentModel, CompleteUser, RelatedUserModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: J<PERSON> } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const DocumentEditModel = z.object({
  id: z.string(),
  orgId: z.string(),
  documentId: z.string(),
  createdById: z.string(),
  content: jsonSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
})

export interface CompleteDocumentEdit extends z.infer<typeof DocumentEditModel> {
  org: CompleteOrg
  document: CompleteDocument
  createdBy: CompleteUser
}

/**
 * RelatedDocumentEditModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedDocumentEditModel: z.ZodSchema<CompleteDocumentEdit> = z.lazy(() => DocumentEditModel.extend({
  org: RelatedOrgModel,
  document: RelatedDocumentModel,
  createdBy: RelatedUserModel,
}))
