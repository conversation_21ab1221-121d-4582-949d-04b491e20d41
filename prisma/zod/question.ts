import * as z from "zod"
import { QuestionType, QuestionCategory, QuestionStatusType } from "@prisma/client"
import { CompleteUser, RelatedUserModel, CompleteOrg, RelatedOrgModel, CompleteQuestionContent, RelatedQuestionContentModel, CompleteResponse, RelatedResponseModel, CompleteTag, RelatedTagModel, CompleteTagEntityConnection, RelatedTagEntityConnectionModel, CompleteResponseFeedback, RelatedResponseFeedbackModel } from "./index"

export const QuestionModel = z.object({
  id: z.string(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  index: z.number().int().nullish(),
  type: z.nativeEnum(QuestionType),
  answerTemplate: z.string().nullish(),
  orgId: z.string(),
  category: z.nativeEnum(QuestionCategory),
  responseId: z.string().nullish(),
  status: z.nativeEnum(QuestionStatusType),
})

export interface CompleteQuestion extends z.infer<typeof QuestionModel> {
  createdBy: CompleteUser
  org: CompleteOrg
  questionContents: CompleteQuestionContent[]
  response?: CompleteResponse | null
  tags: CompleteTag[]
  TagEntityConnection: CompleteTagEntityConnection[]
  ResponseFeedback: CompleteResponseFeedback[]
}

/**
 * RelatedQuestionModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedQuestionModel: z.ZodSchema<CompleteQuestion> = z.lazy(() => QuestionModel.extend({
  createdBy: RelatedUserModel,
  org: RelatedOrgModel,
  questionContents: RelatedQuestionContentModel.array(),
  response: RelatedResponseModel.nullish(),
  tags: RelatedTagModel.array(),
  TagEntityConnection: RelatedTagEntityConnectionModel.array(),
  ResponseFeedback: RelatedResponseFeedbackModel.array(),
}))
