import * as z from "zod"
import { CompleteUser, RelatedUserModel } from "./index"

export const UserMSAddInSettingsModel = z.object({
  id: z.string(),
  insertTextColor: z.string(),
  chatWidth: z.number().int(),
  chatHeight: z.number().int(),
  userId: z.string(),
  fontSize: z.number().int(),
  bold: z.boolean(),
  italic: z.boolean(),
  underline: z.boolean(),
})

export interface CompleteUserMSAddInSettings extends z.infer<typeof UserMSAddInSettingsModel> {
  user: CompleteUser
}

/**
 * RelatedUserMSAddInSettingsModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedUserMSAddInSettingsModel: z.ZodSchema<CompleteUserMSAddInSettings> = z.lazy(() => UserMSAddInSettingsModel.extend({
  user: RelatedUserModel,
}))
