import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteTag, RelatedTagModel, CompleteDocument, RelatedDocumentModel, CompleteQuestion, RelatedQuestionModel, CompleteResponse, RelatedResponseModel, CompleteConversation, RelatedConversationModel } from "./index"

export const TagEntityConnectionModel = z.object({
  id: z.string(),
  connectionReason: z.string(),
  orgId: z.string(),
  tagId: z.string(),
  documentId: z.string().nullish(),
  questionId: z.string().nullish(),
  responseId: z.string().nullish(),
  conversationId: z.string().nullish(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export interface CompleteTagEntityConnection extends z.infer<typeof TagEntityConnectionModel> {
  org: CompleteOrg
  tag: CompleteTag
  document?: CompleteDocument | null
  question?: CompleteQuestion | null
  response?: CompleteResponse | null
  conversation?: CompleteConversation | null
}

/**
 * RelatedTagEntityConnectionModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedTagEntityConnectionModel: z.ZodSchema<CompleteTagEntityConnection> = z.lazy(() => TagEntityConnectionModel.extend({
  org: RelatedOrgModel,
  tag: RelatedTagModel,
  document: RelatedDocumentModel.nullish(),
  question: RelatedQuestionModel.nullish(),
  response: RelatedResponseModel.nullish(),
  conversation: RelatedConversationModel.nullish(),
}))
