import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteAzureDrive, RelatedAzureDriveModel } from "./index"

export const AzureSubscriptionModel = z.object({
  id: z.string(),
  orgId: z.string(),
  azureDriveId: z.string().nullish(),
  subscriptionId: z.string(),
  changeType: z.string(),
  clientState: z.string().nullish(),
  notificationUrl: z.string(),
  lifecycleNotificationUrl: z.string(),
  expirationDateTime: z.date(),
  creatorId: z.string().nullish(),
  latestSupportedTlsVersion: z.string().nullish(),
  encryptionCertificate: z.string().nullish(),
  encryptionCertificateId: z.string().nullish(),
  includeResourceData: z.boolean().nullish(),
  notificationContentType: z.string().nullish(),
})

export interface CompleteAzureSubscription extends z.infer<typeof AzureSubscriptionModel> {
  org: CompleteOrg
  AzureDrive?: CompleteAzureDrive | null
}

/**
 * RelatedAzureSubscriptionModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedAzureSubscriptionModel: z.ZodSchema<CompleteAzureSubscription> = z.lazy(() => AzureSubscriptionModel.extend({
  org: RelatedOrgModel,
  AzureDrive: RelatedAzureDriveModel.nullish(),
}))
