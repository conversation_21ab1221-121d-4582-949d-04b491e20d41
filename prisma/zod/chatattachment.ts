import * as z from "zod"
import { CompleteUser, RelatedUserModel, CompleteChatMessage, RelatedChatMessageModel } from "./index"

export const ChatAttachmentModel = z.object({
  id: z.string(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  name: z.string(),
  size: z.number().int(),
  type: z.string().nullish(),
  url: z.string(),
  preview: z.string().nullish(),
  chatMessageId: z.string().nullish(),
})

export interface CompleteChatAttachment extends z.infer<typeof ChatAttachmentModel> {
  createdBy: CompleteUser
  chatMessage?: CompleteChatMessage | null
}

/**
 * RelatedChatAttachmentModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedChatAttachmentModel: z.ZodSchema<CompleteChatAttachment> = z.lazy(() => ChatAttachmentModel.extend({
  createdBy: RelatedUserModel,
  chatMessage: RelatedChatMessageModel.nullish(),
}))
