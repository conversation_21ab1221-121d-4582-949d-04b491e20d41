import * as z from "zod"
import { CompleteOrg, RelatedOrgModel } from "./index"

export const EgnyteAccessTokenModel = z.object({
  id: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  accessToken: z.string(),
  expiresAt: z.date(),
})

export interface CompleteEgnyteAccessToken extends z.infer<typeof EgnyteAccessTokenModel> {
  org?: CompleteOrg | null
}

/**
 * RelatedEgnyteAccessTokenModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedEgnyteAccessTokenModel: z.ZodSchema<CompleteEgnyteAccessToken> = z.lazy(() => EgnyteAccessTokenModel.extend({
  org: RelatedOrgModel.nullish(),
}))
