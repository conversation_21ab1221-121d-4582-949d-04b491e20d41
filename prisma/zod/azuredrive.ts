import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteAzureSubscription, RelatedAzureSubscriptionModel } from "./index"

export const AzureDriveModel = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullish(),
  azureId: z.string(),
  deltaLink: z.string().nullish(),
  webUrl: z.string().nullish(),
  driveType: z.string().nullish(),
  orgId: z.string(),
})

export interface CompleteAzureDrive extends z.infer<typeof AzureDriveModel> {
  org: CompleteOrg
  subscriptions: CompleteAzureSubscription[]
}

/**
 * RelatedAzureDriveModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedAzureDriveModel: z.ZodSchema<CompleteAzureDrive> = z.lazy(() => AzureDriveModel.extend({
  org: RelatedOrgModel,
  subscriptions: RelatedAzureSubscriptionModel.array(),
}))
