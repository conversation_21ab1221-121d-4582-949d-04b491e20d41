import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteDocument, RelatedDocumentModel, CompleteConversation, RelatedConversationModel, CompleteQuestion, RelatedQuestionModel, CompleteResponse, RelatedResponseModel, CompleteTagEntityConnection, RelatedTagEntityConnectionModel } from "./index"

export const TagModel = z.object({
  id: z.string(),
  name: z.string(),
  summary: z.string(),
  color: z.string(),
  orgId: z.string(),
  parentId: z.string().nullish(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export interface CompleteTag extends z.infer<typeof TagModel> {
  org: CompleteOrg
  parent?: CompleteTag | null
  children: CompleteTag[]
  documents: CompleteDocument[]
  conversations: CompleteConversation[]
  questions: CompleteQuestion[]
  responses: CompleteResponse[]
  TagEntityConnection: CompleteTagEntityConnection[]
}

/**
 * RelatedTagModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedTagModel: z.ZodSchema<CompleteTag> = z.lazy(() => TagModel.extend({
  org: RelatedOrgModel,
  parent: RelatedTagModel.nullish(),
  children: RelatedTagModel.array(),
  documents: RelatedDocumentModel.array(),
  conversations: RelatedConversationModel.array(),
  questions: RelatedQuestionModel.array(),
  responses: RelatedResponseModel.array(),
  TagEntityConnection: RelatedTagEntityConnectionModel.array(),
}))
