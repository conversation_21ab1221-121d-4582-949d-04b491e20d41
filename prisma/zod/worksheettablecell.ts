import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteWorkSheetTable, RelatedWorkSheetTableModel, CompleteWorkSheet, RelatedWorkSheetModel } from "./index"

export const WorkSheetTableCellModel = z.object({
  id: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  address: z.string(),
  tableId: z.string(),
  prompt: z.string().nullish(),
  sheetId: z.string(),
})

export interface CompleteWorkSheetTableCell extends z.infer<typeof WorkSheetTableCellModel> {
  org: CompleteOrg
  table: CompleteWorkSheetTable
  sheet: CompleteWorkSheet
}

/**
 * RelatedWorkSheetTableCellModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedWorkSheetTableCellModel: z.ZodSchema<CompleteWorkSheetTableCell> = z.lazy(() => WorkSheetTableCellModel.extend({
  org: RelatedOrgModel,
  table: RelatedWorkSheetTableModel,
  sheet: RelatedWorkSheetModel,
}))
