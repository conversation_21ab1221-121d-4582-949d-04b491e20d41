import * as z from "zod"
import { CompleteUser, RelatedUserModel, CompleteOrg, RelatedOrgModel } from "./index"

export const UserOrgModel = z.object({
  userId: z.string(),
  orgId: z.string(),
})

export interface CompleteUserOrg extends z.infer<typeof UserOrgModel> {
  user: CompleteUser
  org: CompleteOrg
}

/**
 * RelatedUserOrgModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedUserOrgModel: z.ZodSchema<CompleteUserOrg> = z.lazy(() => UserOrgModel.extend({
  user: RelatedUserModel,
  org: RelatedOrgModel,
}))
