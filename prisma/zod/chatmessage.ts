import * as z from "zod"
import { ChatMessageStatus } from "@prisma/client"
import { CompleteOrg, RelatedOrgModel, CompleteUser, RelatedUserModel, CompleteChatAttachment, RelatedChatAttachmentModel, CompleteConversation, RelatedConversationModel, CompleteChatMessageFeedback, RelatedChatMessageFeedbackModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: Json } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const ChatMessageModel = z.object({
  id: z.string(),
  orgId: z.string(),
  seq: z.number().int(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  body: z.string(),
  contentType: z.string().nullish(),
  status: z.nativeEnum(ChatMessageStatus),
  conversationId: z.string().nullish(),
  metadata: jsonSchema,
})

export interface CompleteChatMessage extends z.infer<typeof ChatMessageModel> {
  org: CompleteOrg
  createdBy: CompleteUser
  attachments: CompleteChatAttachment[]
  conversation?: CompleteConversation | null
  feedback?: CompleteChatMessageFeedback | null
}

/**
 * RelatedChatMessageModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedChatMessageModel: z.ZodSchema<CompleteChatMessage> = z.lazy(() => ChatMessageModel.extend({
  org: RelatedOrgModel,
  createdBy: RelatedUserModel,
  attachments: RelatedChatAttachmentModel.array(),
  conversation: RelatedConversationModel.nullish(),
  feedback: RelatedChatMessageFeedbackModel.nullish(),
}))
