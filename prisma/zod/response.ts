import * as z from "zod"
import { ResponseStatus, ResponseCategory } from "@prisma/client"
import { CompleteUser, RelatedUserModel, CompleteDocumentResponses, RelatedDocumentResponsesModel, CompleteQuestion, RelatedQuestionModel, CompleteDocumentSection, RelatedDocumentSectionModel, CompleteOrg, RelatedOrgModel, CompleteResponseContent, RelatedResponseContentModel, CompleteTag, RelatedTagModel, CompletePluginResponse, RelatedPluginResponseModel, CompleteTagEntityConnection, RelatedTagEntityConnectionModel, CompleteResponseFeedback, RelatedResponseFeedbackModel } from "./index"

export const ResponseModel = z.object({
  id: z.string(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  documentSectionId: z.string().nullish(),
  orgId: z.string(),
  status: z.nativeEnum(ResponseStatus),
  assignedToId: z.string().nullish(),
  category: z.nativeEnum(ResponseCategory),
})

export interface CompleteResponse extends z.infer<typeof ResponseModel> {
  createdBy: CompleteUser
  documents: CompleteDocumentResponses[]
  questions: CompleteQuestion[]
  documentSection?: CompleteDocumentSection | null
  org: CompleteOrg
  responseContents: CompleteResponseContent[]
  assignedTo?: CompleteUser | null
  tags: CompleteTag[]
  PluginResponse: CompletePluginResponse[]
  TagEntityConnection: CompleteTagEntityConnection[]
  ResponseFeedback: CompleteResponseFeedback[]
}

/**
 * RelatedResponseModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedResponseModel: z.ZodSchema<CompleteResponse> = z.lazy(() => ResponseModel.extend({
  createdBy: RelatedUserModel,
  documents: RelatedDocumentResponsesModel.array(),
  questions: RelatedQuestionModel.array(),
  documentSection: RelatedDocumentSectionModel.nullish(),
  org: RelatedOrgModel,
  responseContents: RelatedResponseContentModel.array(),
  assignedTo: RelatedUserModel.nullish(),
  tags: RelatedTagModel.array(),
  PluginResponse: RelatedPluginResponseModel.array(),
  TagEntityConnection: RelatedTagEntityConnectionModel.array(),
  ResponseFeedback: RelatedResponseFeedbackModel.array(),
}))
