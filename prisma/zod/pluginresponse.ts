import * as z from "zod"
import { QuestionSource } from "@prisma/client"
import { CompleteUser, RelatedUserModel, CompleteOrg, RelatedOrgModel, CompleteResponse, RelatedResponseModel, CompleteDocument, RelatedDocumentModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: Json } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const PluginResponseModel = z.object({
  id: z.string(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  questionSource: z.nativeEnum(QuestionSource),
  responseId: z.string(),
  originLocation: jsonSchema,
  documentId: z.string().nullish(),
})

export interface CompletePluginResponse extends z.infer<typeof PluginResponseModel> {
  createdBy: CompleteUser
  org: CompleteOrg
  response: CompleteResponse
  document?: CompleteDocument | null
}

/**
 * RelatedPluginResponseModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedPluginResponseModel: z.ZodSchema<CompletePluginResponse> = z.lazy(() => PluginResponseModel.extend({
  createdBy: RelatedUserModel,
  org: RelatedOrgModel,
  response: RelatedResponseModel,
  document: RelatedDocumentModel.nullish(),
}))
