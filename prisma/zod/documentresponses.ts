import * as z from "zod"
import { CompleteDocument, RelatedDocumentModel, CompleteResponse, RelatedResponseModel } from "./index"

export const DocumentResponsesModel = z.object({
  documentId: z.string(),
  responseId: z.string(),
})

export interface CompleteDocumentResponses extends z.infer<typeof DocumentResponsesModel> {
  document: CompleteDocument
  response: CompleteResponse
}

/**
 * RelatedDocumentResponsesModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedDocumentResponsesModel: z.ZodSchema<CompleteDocumentResponses> = z.lazy(() => DocumentResponsesModel.extend({
  document: RelatedDocumentModel,
  response: RelatedResponseModel,
}))
