import * as z from "zod"
import { CompleteUser, RelatedUserModel, CompleteOrg, RelatedOrgModel, CompleteChatMessage, RelatedChatMessageModel, CompleteChatParticipant, RelatedChatParticipantModel, CompleteTag, RelatedTagModel, CompleteTagEntityConnection, RelatedTagEntityConnectionModel } from "./index"

export const ConversationModel = z.object({
  id: z.string(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
})

export interface CompleteConversation extends z.infer<typeof ConversationModel> {
  createdBy: CompleteUser
  org: CompleteOrg
  messages: CompleteChatMessage[]
  participants: CompleteChatParticipant[]
  tags: CompleteTag[]
  TagEntityConnection: CompleteTagEntityConnection[]
}

/**
 * RelatedConversationModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedConversationModel: z.ZodSchema<CompleteConversation> = z.lazy(() => ConversationModel.extend({
  createdBy: RelatedUserModel,
  org: RelatedOrgModel,
  messages: RelatedChatMessageModel.array(),
  participants: RelatedChatParticipantModel.array(),
  tags: RelatedTagModel.array(),
  TagEntityConnection: RelatedTagEntityConnectionModel.array(),
}))
