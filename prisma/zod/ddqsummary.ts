import * as z from "zod"
import { CompleteUser, RelatedUserModel, CompleteOrg, RelatedOrgModel, CompleteDocument, RelatedDocumentModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: J<PERSON> } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const DDQSummaryModel = z.object({
  id: z.string(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  documentId: z.string(),
  summary: jsonSchema,
})

export interface CompleteDDQSummary extends z.infer<typeof DDQSummaryModel> {
  createdBy: CompleteUser
  org: CompleteOrg
  document: CompleteDocument
}

/**
 * RelatedDDQSummaryModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedDDQSummaryModel: z.ZodSchema<CompleteDDQSummary> = z.lazy(() => DDQSummaryModel.extend({
  createdBy: RelatedUserModel,
  org: RelatedOrgModel,
  document: RelatedDocumentModel,
}))
