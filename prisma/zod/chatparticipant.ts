import * as z from "zod"
import { ParticipantStatus } from "@prisma/client"
import { CompleteUser, RelatedUserModel, CompleteConversation, RelatedConversationModel } from "./index"

export const ChatParticipantModel = z.object({
  id: z.string(),
  userId: z.string(),
  conversationId: z.string().nullish(),
  status: z.nativeEnum(ParticipantStatus),
})

export interface CompleteChatParticipant extends z.infer<typeof ChatParticipantModel> {
  user: CompleteUser
  conversation?: CompleteConversation | null
}

/**
 * RelatedChatParticipantModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedChatParticipantModel: z.ZodSchema<CompleteChatParticipant> = z.lazy(() => ChatParticipantModel.extend({
  user: RelatedUserModel,
  conversation: RelatedConversationModel.nullish(),
}))
