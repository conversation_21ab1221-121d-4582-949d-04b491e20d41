import * as z from "zod"
import { CompleteOrg, RelatedOrgModel, CompleteFundFamily, RelatedFundFamilyModel, CompleteDocument, RelatedDocumentModel } from "./index"

export const FundModel = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  strategy: z.string().nullish(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  fundFamilyId: z.string().nullish(),
})

export interface CompleteFund extends z.infer<typeof FundModel> {
  org: CompleteOrg
  fundFamily?: CompleteFundFamily | null
  documents: CompleteDocument[]
}

/**
 * RelatedFundModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedFundModel: z.ZodSchema<CompleteFund> = z.lazy(() => FundModel.extend({
  org: RelatedOrgModel,
  fundFamily: RelatedFundFamilyModel.nullish(),
  documents: RelatedDocumentModel.array(),
}))
