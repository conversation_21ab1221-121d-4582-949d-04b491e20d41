import * as z from "zod"
import { ResponseFeedbackType } from "@prisma/client"
import { CompleteOrg, RelatedOrgModel, CompleteResponse, RelatedResponseModel, CompleteUser, RelatedUserModel, CompleteQuestion, RelatedQuestionModel, CompleteDocument, RelatedDocumentModel } from "./index"

export const ResponseFeedbackModel = z.object({
  id: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  responseId: z.string().nullish(),
  createdById: z.string(),
  type: z.nativeEnum(ResponseFeedbackType),
  feedback: z.string().nullish(),
  questionId: z.string().nullish(),
  documentId: z.string().nullish(),
  responseText: z.string().nullish(),
  reason: z.string().nullish(),
})

export interface CompleteResponseFeedback extends z.infer<typeof ResponseFeedbackModel> {
  org: CompleteOrg
  response?: CompleteResponse | null
  createdBy: CompleteUser
  question?: CompleteQuestion | null
  document?: CompleteDocument | null
}

/**
 * RelatedResponseFeedbackModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedResponseFeedbackModel: z.ZodSchema<CompleteResponseFeedback> = z.lazy(() => ResponseFeedbackModel.extend({
  org: RelatedOrgModel,
  response: RelatedResponseModel.nullish(),
  createdBy: RelatedUserModel,
  question: RelatedQuestionModel.nullish(),
  document: RelatedDocumentModel.nullish(),
}))
