import * as z from "zod"
import { ChatMessageFeedbackType } from "@prisma/client"
import { CompleteOrg, RelatedOrgModel, CompleteChatMessage, RelatedChatMessageModel, CompleteUser, RelatedUserModel } from "./index"

export const ChatMessageFeedbackModel = z.object({
  id: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  chatMessageId: z.string().nullish(),
  createdById: z.string(),
  type: z.nativeEnum(ChatMessageFeedbackType),
  feedback: z.string().nullish(),
})

export interface CompleteChatMessageFeedback extends z.infer<typeof ChatMessageFeedbackModel> {
  org: CompleteOrg
  chatMessage?: CompleteChatMessage | null
  createdBy: CompleteUser
}

/**
 * RelatedChatMessageFeedbackModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedChatMessageFeedbackModel: z.ZodSchema<CompleteChatMessageFeedback> = z.lazy(() => ChatMessageFeedbackModel.extend({
  org: RelatedOrgModel,
  chatMessage: RelatedChatMessageModel.nullish(),
  createdBy: RelatedUserModel,
}))
