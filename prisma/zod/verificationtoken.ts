import * as z from "zod"

export const VerificationTokenModel = z.object({
  identifier: z.string(),
  token: z.string(),
  expires: z.date(),
})

export interface CompleteVerificationToken extends z.infer<typeof VerificationTokenModel> {
}

/**
 * RelatedVerificationTokenModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedVerificationTokenModel: z.ZodSchema<CompleteVerificationToken> = z.lazy(() => VerificationTokenModel.extend({
}))
