import * as z from "zod"
import { Complete<PERSON>ser, RelatedUserModel, CompleteOrg, RelatedOrgModel, CompleteQuestion, RelatedQuestionModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: <PERSON><PERSON> } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const QuestionContentModel = z.object({
  id: z.string(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  orgId: z.string(),
  questionId: z.string(),
  content: jsonSchema,
})

export interface CompleteQuestionContent extends z.infer<typeof QuestionContentModel> {
  createdBy: CompleteUser
  org: CompleteOrg
  question?: CompleteQuestion | null
}

/**
 * RelatedQuestionContentModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedQuestionContentModel: z.ZodSchema<CompleteQuestionContent> = z.lazy(() => QuestionContentModel.extend({
  createdBy: RelatedUserModel,
  org: RelatedOrgModel,
  question: RelatedQuestionModel.nullish(),
}))
