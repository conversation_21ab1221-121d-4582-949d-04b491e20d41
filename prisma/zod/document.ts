import * as z from "zod"
import { DocumentType, DocumentStatus, DDQStatus, DocumentSource } from "@prisma/client"
import { CompleteOrg, RelatedOrgModel, CompleteDocumentChunk, RelatedDocumentChunkModel, CompleteDocumentEdit, RelatedDocumentEditModel, CompleteDDQSummary, RelatedDDQSummaryModel, CompleteDDQMetadata, RelatedDDQMetadataModel, CompleteUser, RelatedUserModel, CompleteDocumentResponses, RelatedDocumentResponsesModel, CompletePluginResponse, RelatedPluginResponseModel, CompleteTag, RelatedTagModel, CompleteTagEntityConnection, RelatedTagEntityConnectionModel, CompleteFund, RelatedFundModel, CompleteWorkSheet, RelatedWorkSheetModel, CompleteResponseFeedback, RelatedResponseFeedbackModel, CompleteDocumentSection, RelatedDocumentSectionModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: Json } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const DocumentModel = z.object({
  id: z.string(),
  name: z.string().min(1, { message: "Name is required" }).max(255, { message: "Name must be less than 255 characters" }),
  title: z.string().nullish(),
  url: z.string().min(1, { message: "URL is required" }),
  type: z.nativeEnum(DocumentType),
  size: z.number().int(),
  plainTextContents: z.string().nullish(),
  status: z.nativeEnum(DocumentStatus),
  dueDate: z.date().nullish(),
  ddqStatus: z.nativeEnum(DDQStatus).nullish(),
  source: z.nativeEnum(DocumentSource),
  orgId: z.string(),
  htmlContents: z.string().nullish(),
  jsonContents: jsonSchema,
  summary: z.string().nullish(),
  azureItemId: z.string().nullish(),
  createdById: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export interface CompleteDocument extends z.infer<typeof DocumentModel> {
  org: CompleteOrg
  chunks: CompleteDocumentChunk[]
  edits: CompleteDocumentEdit[]
  DDQSummary?: CompleteDDQSummary | null
  DDQMetadata?: CompleteDDQMetadata | null
  createdBy: CompleteUser
  responses: CompleteDocumentResponses[]
  PluginResponse: CompletePluginResponse[]
  tags: CompleteTag[]
  TagEntityConnection: CompleteTagEntityConnection[]
  funds: CompleteFund[]
  worksheets: CompleteWorkSheet[]
  ResponseFeedback: CompleteResponseFeedback[]
  sections: CompleteDocumentSection[]
}

/**
 * RelatedDocumentModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedDocumentModel: z.ZodSchema<CompleteDocument> = z.lazy(() => DocumentModel.extend({
  org: RelatedOrgModel,
  chunks: RelatedDocumentChunkModel.array(),
  edits: RelatedDocumentEditModel.array(),
  DDQSummary: RelatedDDQSummaryModel.nullish(),
  DDQMetadata: RelatedDDQMetadataModel.nullish(),
  createdBy: RelatedUserModel,
  responses: RelatedDocumentResponsesModel.array(),
  PluginResponse: RelatedPluginResponseModel.array(),
  tags: RelatedTagModel.array(),
  TagEntityConnection: RelatedTagEntityConnectionModel.array(),
  funds: RelatedFundModel.array(),
  worksheets: RelatedWorkSheetModel.array(),
  ResponseFeedback: RelatedResponseFeedbackModel.array(),
  sections: RelatedDocumentSectionModel.array(),
}))
