// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
    provider        = "prisma-client-js"
    binaryTargets   = ["native", "rhel-openssl-3.0.x", "linux-arm64-openssl-3.0.x"]
    previewFeatures = ["postgresqlExtensions", "fullTextSearchPostgres"]
}

datasource db {
    provider = "postgresql"

    // NOTE: When using mysql or sqlserver, uncomment the @db.Text annotations in model Account below
    // Further reading:
    // https://next-auth.js.org/adapters/prisma#create-the-prisma-schema
    // https://www.prisma.io/docs/reference/api-reference/prisma-schema-reference#string
    url        = env("DATABASE_URL")
    extensions = [vector]
}

model Session {
    id           String   @id @default(cuid())
    sessionToken String   @unique
    userId       String
    expires      DateTime
    user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

enum CorpType {
    LLC
    CCorp
    SCorp
    NonProfit
    Partnership
    SoleProprietorship
}

enum QuestionSource {
    Web
    MSExcelPlugin
    MSWordPlugin
    MSPowerpointPlugin
}

model Address {
    id     String  @id @default(cuid())
    street String?
    city   String?
    state  String?
    zip    String?
    orgs   Org[]
}

enum IntegrationType {
    Plaid
    Quickbooks
    Xero
    Stripe
    Square
    Shopify
    Amazon
    Meta
    Google
}

enum IntegrationStatus {
    Pending
    Connected
    Disconnected
}

model Integration {
    id        String   @id @default(cuid())
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    type   IntegrationType
    status IntegrationStatus @default(Disconnected)

    // Relations
    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])
    orgId       String
    org         Org    @relation(fields: [orgId], references: [id])
}

enum OrgType {
    Borrower
    Lender
    Other
}

model Org {
    id             String    @id @default(cuid())
    name           String
    createdAt      DateTime  @default(now())
    updatedAt      DateTime  @updatedAt
    createdById    String
    incorporatedAt DateTime?

    type     OrgType
    corpType CorpType
    ein      String?
    bio      String?

    // Clerk integartion 
    clerkId String?

    // Webhook secret
    webhookSecret String?

    // Relations

    // Each org can have zero or more lenders and borrowers
    lenders   Org[] @relation("LendersBorrowers")
    borrowers Org[] @relation("LendersBorrowers")

    address         Address?          @relation(fields: [addressId], references: [id])
    addressId       String?
    users           UserOrg[]
    Integration     Integration[]
    Document        Document[]
    Conversation    Conversation[]
    ChatMessage     ChatMessage[]
    DocumentChunk   DocumentChunk[]
    DocumentEdit    DocumentEdit[]
    Response        Response[]
    ResponseContent ResponseContent[]
    Question        Question[]
    QuestionContent QuestionContent[]
    DDQSummary      DDQSummary[]
    DDQMetadata     DDQMetadata[]
    PluginResponse  PluginResponse[]
    AzureDrive      AzureDrive[]

    egnyteAccessToken   EgnyteAccessToken? @relation(fields: [egnyteAccessTokenId], references: [id])
    egnyteAccessTokenId String?            @unique

    azureAccessToken    AzureAccessToken?     @relation(fields: [azureAccessTokenId], references: [id])
    azureAccessTokenId  String?               @unique
    AzureSubscription   AzureSubscription[]
    Tag                 Tag[]
    TagEntityConnection TagEntityConnection[]
    ChatMessageFeedback ChatMessageFeedback[]
    Fund                Fund[]
    FundFamily          FundFamily[]
    WorkSheet           WorkSheet[]
    WorkSheetTable      WorkSheetTable[]
    WorkSheetTableCell  WorkSheetTableCell[]
    ResponseFeedback    ResponseFeedback[]
    DocumentSection     DocumentSection[]
}

model EgnyteAccessToken {
    id        String   @id @default(cuid())
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    accessToken String
    expiresAt   DateTime

    org Org?
}

model AzureAccessToken {
    id        String   @id @default(cuid())
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    accessToken  String
    refreshToken String
    expiresAt    DateTime

    org Org?
}

model AzureDrive {
    id            String              @id @default(cuid())
    name          String
    description   String?
    azureId       String
    deltaLink     String?
    webUrl        String?
    driveType     String?
    org           Org                 @relation(fields: [orgId], references: [id])
    orgId         String
    subscriptions AzureSubscription[]
}

model AzureSubscription {
    id    String @id @default(cuid())
    org   Org    @relation(fields: [orgId], references: [id])
    orgId String

    AzureDrive   AzureDrive? @relation(fields: [azureDriveId], references: [id])
    azureDriveId String?

    subscriptionId            String
    changeType                String
    clientState               String?
    notificationUrl           String
    lifecycleNotificationUrl  String
    expirationDateTime        DateTime
    creatorId                 String?
    latestSupportedTlsVersion String?
    encryptionCertificate     String?
    encryptionCertificateId   String?
    includeResourceData       Boolean?
    notificationContentType   String?
}

enum SocialType {
    X
    LinkedIn
    Facebook
    Instagram
    TikTok
    YouTube
    Website
}

model Social {
    id     String     @id @default(cuid())
    type   SocialType
    url    String
    user   User       @relation(fields: [userId], references: [id])
    userId String
}

enum UserTitle {
    CEO
    CFO
    COO
    CTO
    CMO
    CSO
    CIO
    Founder
    BoardMember
    Other
}

enum UserRole {
    Member
    Admin
    SuperAdmin
}

model User {
    id String @id @default(cuid())

    // User properties
    clerkId       String?   @unique
    name          String?
    email         String    @unique
    emailVerified DateTime?
    image         String?
    role          UserRole  @default(Member)
    title         UserTitle
    bio           String?

    // Relations
    socials           Social[]
    sessions          Session[]
    orgs              UserOrg[]
    Integration       Integration[]
    Document          Document[]
    Conversation      Conversation[]
    ChatMessage       ChatMessage[]
    ChatAttachment    ChatAttachment[]
    ChatParticipant   ChatParticipant[]
    DocumentEdit      DocumentEdit[]
    CreatedResponses  Response[]        @relation("CreatedBy")
    AssignedResponses Response[]        @relation("AssignedResponses")
    ResponseContent   ResponseContent[]
    Question          Question[]
    QuestionContent   QuestionContent[]
    DDQSummary        DDQSummary[]
    DDQMetadata       DDQMetadata[]

    PluginResponse      PluginResponse[]
    ChatMessageFeedback ChatMessageFeedback[]

    msAddInSettings  UserMSAddInSettings?
    ResponseFeedback ResponseFeedback[]
}

model UserMSAddInSettings {
    id              String  @id @default(cuid())
    insertTextColor String
    chatWidth       Int
    chatHeight      Int
    user            User    @relation(fields: [userId], references: [id])
    userId          String  @unique
    fontSize        Int     @default(12)
    bold            Boolean @default(false)
    italic          Boolean @default(false)
    underline       Boolean @default(false)
}

model UserOrg {
    user   User   @relation(fields: [userId], references: [id])
    userId String
    org    Org    @relation(fields: [orgId], references: [id])
    orgId  String

    @@id([userId, orgId])
}

enum DocumentType {
    FOLDER
    PDF
    DOC
    DOCX
    XLS
    XLSX
    CSV
    TXT
    IMG
    PPTX
    OTHER
}

enum DocumentStatus {
    PENDING
    PROCESSING
    TAGGING
    GENERATING_ANSWERS
    READY
    ERROR
    EXTERNAL
}

enum DDQStatus {
    PENDING
    REVIEW
    APPROVED
}

enum DocumentSource {
    LOCAL
    EGNYTE
    INTRALINKS
    SHAREPOINT
}

model DocumentSection {
    id String @id @default(cuid())

    title String
    description String
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    orgId  String
    org    Org             @relation(fields: [orgId], references: [id])

    documentId String
    document Document @relation(fields: [documentId], references: [id])

    responses Response[]
}

model Document {
    id                String         @id @default(cuid())
    name              String         @unique /// @zod.min(1, { message: "Name is required" }).max(255, { message: "Name must be less than 255 characters" }  )
    title             String?
    url               String         @unique /// @zod.min(1, { message: "URL is required" })
    type              DocumentType
    size              Int
    plainTextContents String?
    status            DocumentStatus @default(PENDING)
    dueDate           DateTime?
    ddqStatus         DDQStatus?
    source            DocumentSource @default(LOCAL)

    // Relations
    orgId  String
    org    Org             @relation(fields: [orgId], references: [id])
    chunks DocumentChunk[]
    edits  DocumentEdit[]

    // If the document is a DDQ, it will have a summary and metadata
    DDQSummary  DDQSummary?
    DDQMetadata DDQMetadata?

    htmlContents String?
    jsonContents Json?
    summary      String?

    // Azure
    azureItemId String?

    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    createdAt DateTime            @default(now())
    updatedAt DateTime            @updatedAt
    responses DocumentResponses[]

    PluginResponse      PluginResponse[]
    tags                Tag[]
    TagEntityConnection TagEntityConnection[]
    funds               Fund[]
    worksheets          WorkSheet[]
    ResponseFeedback    ResponseFeedback[]
    sections            DocumentSection[]
}

model WorkSheet {
    id String @id @default(cuid())

    name        String
    description String
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    orgId String
    org   Org    @relation(fields: [orgId], references: [id])

    documentId         String
    document           Document             @relation(fields: [documentId], references: [id], onDelete: Cascade)
    tables             WorkSheetTable[]
    WorkSheetTableCell WorkSheetTableCell[]
}

model WorkSheetTable {
    id String @id @default(cuid())

    name        String
    description String
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    orgId String
    org   Org    @relation(fields: [orgId], references: [id])

    workSheetId String
    workSheet   WorkSheet @relation(fields: [workSheetId], references: [id], onDelete: Cascade)

    // Table data
    contents Json?
    cells    WorkSheetTableCell[]
}

model WorkSheetTableCell {
    id String @id @default(cuid())

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    orgId String
    org   Org    @relation(fields: [orgId], references: [id])

    address String

    tableId String
    table   WorkSheetTable @relation(fields: [tableId], references: [id], onDelete: Cascade)

    prompt String?

    sheetId String
    sheet   WorkSheet @relation(fields: [sheetId], references: [id], onDelete: Cascade)
}

model DocumentEdit {
    id String @id @default(cuid())

    // Relations
    orgId String
    org   Org    @relation(fields: [orgId], references: [id])

    documentId String
    document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    // Edit properties
    content Json?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

enum DocumentChunkType {
    NORMAL
    JUMBO
}

model DocumentChunk {
    id String @id @default(cuid())

    // Relations
    orgId String
    org   Org    @relation(fields: [orgId], references: [id])

    documentId String
    document   Document               @relation(fields: [documentId], references: [id], onDelete: Cascade)
    content    String
    metadata   Json?
    vector     Unsupported("vector")?
    chunkType  DocumentChunkType      @default(NORMAL)
    chunkIndex Int                    @default(0)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

model FundFamily {
    id String @id @default(cuid())

    name        String   @unique
    description String
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    // Relations
    orgId String
    org   Org    @relation(fields: [orgId], references: [id])
    funds Fund[]
}

model Fund {
    id String @id @default(cuid())

    name        String   @unique
    description String
    strategy    String?
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    // Relations
    orgId        String
    org          Org         @relation(fields: [orgId], references: [id])
    fundFamily   FundFamily? @relation(fields: [fundFamilyId], references: [id])
    fundFamilyId String?

    documents Document[]
}

model Tag {
    id String @id @default(cuid())

    name    String @unique
    summary String
    color   String

    // Relations
    orgId String
    org   Org    @relation(fields: [orgId], references: [id])

    // Each tag can have a single parent and many children
    parentId String?
    parent   Tag?    @relation("ParentChildrenTags", fields: [parentId], references: [id])
    children Tag[]   @relation("ParentChildrenTags")

    createdAt           DateTime              @default(now())
    updatedAt           DateTime              @updatedAt
    documents           Document[]
    conversations       Conversation[]
    questions           Question[]
    responses           Response[]
    TagEntityConnection TagEntityConnection[]
}

// A table outlining why the tag was applied to the entity
model TagEntityConnection {
    id String @id @default(cuid())

    connectionReason String

    // Relations
    orgId String
    org   Org    @relation(fields: [orgId], references: [id])

    tagId String
    tag   Tag    @relation(fields: [tagId], references: [id])

    document   Document? @relation(fields: [documentId], references: [id])
    documentId String?

    question   Question? @relation(fields: [questionId], references: [id])
    questionId String?

    response   Response? @relation(fields: [responseId], references: [id])
    responseId String?

    conversation   Conversation? @relation(fields: [conversationId], references: [id])
    conversationId String?

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

// Chat Interface
model ChatAttachment {
    id          String @id @default(cuid())
    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    name          String
    size          Int
    type          String?
    url           String
    preview       String?
    chatMessage   ChatMessage? @relation(fields: [chatMessageId], references: [id], onDelete: Cascade)
    chatMessageId String?
}

enum ChatMessageStatus {
    RETRIEVING
    GENERATING_CITATIONS
    VALIDATING_CITATIONS
    GENERATING_ANSWER
    RETRY
    ERROR
    READY
}

enum ChatMessageFeedbackType {
    GOOD
    BAD
}

model ChatMessageFeedback {
    id        String   @id @default(cuid())
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    orgId         String
    org           Org          @relation(fields: [orgId], references: [id])
    chatMessageId String?      @unique
    chatMessage   ChatMessage? @relation(fields: [chatMessageId], references: [id])

    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    type     ChatMessageFeedbackType
    feedback String?
}

enum ResponseFeedbackType {
    GOOD
    BAD
}

model ResponseFeedback {
    id        String   @id @default(cuid())
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    orgId String
    org   Org    @relation(fields: [orgId], references: [id], onDelete: Cascade)

    responseId String?
    response   Response? @relation(fields: [responseId], references: [id])

    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    type     ResponseFeedbackType
    feedback String?

    questionId String?
    question   Question? @relation(fields: [questionId], references: [id])

    documentId String?
    document   Document? @relation(fields: [documentId], references: [id])

    responseText String? @default("") @db.Text
    reason       String? @default("") @db.Text
}

model ChatMessage {
    id String @id @default(cuid())

    orgId String
    org   Org    @relation(fields: [orgId], references: [id])

    // Sequential chat message numbering
    seq         Int    @default(autoincrement())
    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    body           String
    contentType    String?
    status         ChatMessageStatus    @default(READY)
    attachments    ChatAttachment[]
    conversation   Conversation?        @relation(fields: [conversationId], references: [id], onDelete: Cascade)
    conversationId String?
    feedback       ChatMessageFeedback?

    metadata Json?
}

enum ParticipantStatus {
    ONLINE
    OFFLINE
    BUSY
    AWAY
}

model ChatParticipant {
    id             String            @id @default(cuid())
    user           User              @relation(fields: [userId], references: [id])
    userId         String
    conversation   Conversation?     @relation(fields: [conversationId], references: [id], onDelete: Cascade)
    conversationId String?
    status         ParticipantStatus @default(ONLINE)
}

model Conversation {
    id          String @id @default(cuid())
    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    orgId               String
    org                 Org                   @relation(fields: [orgId], references: [id], onDelete: Cascade)
    messages            ChatMessage[]
    participants        ChatParticipant[]
    tags                Tag[]
    TagEntityConnection TagEntityConnection[]
}

enum ResponseStatus {
    DRAFT
    PENDING_APPROVAL
    APPROVED
    REJECTED
}

enum ResponseCategory {
    DATA_ASSURANCE
    STANDARDS_AND_FRAMEWORKS
    INVESTMENT_PROCESS
    OTHER
}

enum QuestionCategory {
    DATA_ASSURANCE
    STANDARDS_AND_FRAMEWORKS
    INVESTMENT_PROCESS
    OTHER
}

model ResponseContent {
    id          String @id @default(cuid())
    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
    // Relations
    orgId     String
    org       Org      @relation(fields: [orgId], references: [id], onDelete: Cascade)

    responseId String
    response   Response? @relation(fields: [responseId], references: [id], onDelete: Cascade)

    // TODO
    // generatedBy user or AI
    answerGenerationType AnswerGenerationType   @default(EXTRACTED)
    // Content can be text, image, etc. Content should also state origin (person, system, AI, etc)
    content              Json? // citation live here
    vector               Unsupported("vector")?

    @@index([updatedAt, createdAt])
    @@index([content(ops: JsonbOps)], type: Gin)
}

enum AnswerGenerationType {
    EXTRACTED
    GENERATED
}

model Response {
    id          String @id @default(cuid())
    createdById String
    createdBy   User   @relation("CreatedBy", fields: [createdById], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // The DDQs this response is used by/included in
    // This should be expanded to inclue location in target document 
    // and potentially a question that this was used to answer
    documents DocumentResponses[]
    questions Question[]

    documentSectionId String?
    documentSection   DocumentSection? @relation(fields: [documentSectionId], references: [id], onDelete: Cascade)

    // Relations
    orgId String
    org   Org    @relation(fields: [orgId], references: [id])

    // Maintain a timeline of responses and edits for traceability
    responseContents ResponseContent[]
    status           ResponseStatus    @default(DRAFT)
    assignedToId     String?
    assignedTo       User?             @relation("AssignedResponses", fields: [assignedToId], references: [id])
    category         ResponseCategory  @default(OTHER)

    tags Tag[]

    PluginResponse      PluginResponse[]
    TagEntityConnection TagEntityConnection[]
    ResponseFeedback    ResponseFeedback[]

    @@index([updatedAt, createdAt])
}

model DocumentResponses {
    documentId String
    document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

    responseId String
    response   Response @relation(fields: [responseId], references: [id], onDelete: Cascade)

    @@id([documentId, responseId])
}

model QuestionContent {
    id          String @id @default(cuid())
    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
    // Relations
    orgId     String
    org       Org      @relation(fields: [orgId], references: [id])

    questionId String
    question   Question? @relation(fields: [questionId], references: [id], onDelete: Cascade)

    // Content can be text, image, etc. 
    content Json?
    vector  Unsupported("vector")?

    @@index([updatedAt, createdAt])
    @@index([content(ops: JsonbOps)], type: Gin)
}

enum QuestionStatusType {
    NEW
    ANSWERED
}

enum QuestionType {
    YES_NO
    YES_NO_EXPANDED
    MULTIPLE_CHOICE
    FREE_TEXT
    TABLE
}

model Question {
    id          String @id @default(cuid())
    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Index of question in document
    index Int?
    type  QuestionType @default(FREE_TEXT)

    // Some DDQs have a specific answer template per question
    answerTemplate String?

    // Relations
    orgId            String
    org              Org               @relation(fields: [orgId], references: [id], onDelete: Cascade)
    questionContents QuestionContent[]
    category         QuestionCategory  @default(OTHER)
    response         Response?         @relation(fields: [responseId], references: [id], onDelete: Cascade)
    responseId       String?

    tags                Tag[]
    TagEntityConnection TagEntityConnection[]
    status              QuestionStatusType    @default(NEW)
    ResponseFeedback    ResponseFeedback[]

    @@index([updatedAt, createdAt])
}

model PluginResponse {
    id          String @id @default(cuid())
    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    orgId          String
    org            Org            @relation(fields: [orgId], references: [id], onDelete: Cascade)
    // plugin type Excel / Word / Outlook
    questionSource QuestionSource @default(Web)
    response       Response       @relation(fields: [responseId], references: [id], onDelete: Cascade)
    responseId     String
    originLocation Json? // for Word Doc its line number, for Excel its a range
    document       Document?      @relation(fields: [documentId], references: [id], onDelete: Cascade)
    documentId     String?
}

model DDQSummary {
    id          String @id @default(cuid())
    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    orgId      String
    org        Org      @relation(fields: [orgId], references: [id], onDelete: Cascade)
    documentId String   @unique
    document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

    summary Json?
}

model DDQMetadata {
    id          String @id @default(cuid())
    createdById String
    createdBy   User   @relation(fields: [createdById], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    orgId      String
    org        Org      @relation(fields: [orgId], references: [id], onDelete: Cascade)
    documentId String   @unique
    document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

    metadata Json?
}

// https://github.com/CarterGrimmeisen/zod-prisma
generator zod {
    provider = "zod-prisma"
    output   = "./zod" // (default) the directory where generated zod schemas will be saved /// @zod.optional()

    relationModel = true // (default) Create and export both plain and related models. /// @zod.optional()
    // relationModel         = "default" // Do not export model without relations. /// @zod.optional()
    // relationModel         = false // Do not generate related model

    modelCase = "PascalCase" // (default) Output models using pascal case (ex. UserModel, PostModel) /// @zod.optional()
    // modelCase             = "camelCase" // Output models using camel case (ex. userModel, postModel)

    modelSuffix = "Model" // (default) Suffix to apply to your prisma models when naming Zod schemas /// @zod.optional()

    // useDecimalJs          = false // (default) represent the prisma Decimal type using as a JS number /// @zod.optional()
    useDecimalJs = true // represent the prisma Decimal type using Decimal.js (as Prisma does)

    // imports = null // (default) will import the referenced file in generated schemas to be used via imports.someExportedVariable /// @zod.optional()

    // https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-by-null-values
    prismaJsonNullability = true // (default) uses prisma's scheme for JSON field nullability /// @zod.optional()
    // prismaJsonNullability = false // allows null assignment to optional JSON fields
}
