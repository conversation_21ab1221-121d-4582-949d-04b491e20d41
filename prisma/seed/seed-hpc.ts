import { faker } from "@faker-js/faker";
import {
  CorpType,
  Org,
  OrgType,
  ParticipantStatus,
  User,
  UserRole,
  UserTitle,
} from "@prisma/client";
import { db as prisma } from "~/server/db";

const userList: User[] = [
  // Virgil and 25M users
  {
    id: faker.string.uuid(),
    clerkId: "user_2sUjTTykSMjgnKVlku2WgArrcqF",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Gilad",
    role: UserRole.SuperAdmin,
    bio: "Engineering Lead of Virgil AI",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sXNSwZNmGr4hckRNdjKH0fcHxn",
    name: "<PERSON>",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Crawford",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sXhyaOYR8dhLUTxCOXi2gBSxIL",
    name: "Calista Reyes",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Calista",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sfowjPbIrwsdqlk5ZlwU74a84k",
    name: "Sanford Spivey",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Kathrine",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },

  // End Virgil and 25M users

  // HPC Users
  {
    id: faker.string.uuid(),
    clerkId: "user_2soLkV7oiUVgES4xkI6Ym4VKTec",
    name: "Tony Hu",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Tony",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2soLuHE2KwsleB7g4WiQLwytj7h",
    name: "Brian Blaney",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Brian",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2soM79n4ej4bnPSWjytPuY930CH",
    name: "Jon Hays",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Jon",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2soMDmnoyC8JoaScSV0g8omUJE2",
    name: "Danielle Tanzman",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Danielle",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },

  {
    id: faker.string.uuid(),
    clerkId: "user_2soMHwZdFV19fdJ6j5a2JvmeAtK",
    name: "Athena Zander",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Athena",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },

  {
    id: faker.string.uuid(),
    clerkId: "user_2soMNUifYwKRrj2pRkMSxa3ugGY",
    name: "Morgan Starr",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Morgan",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2soMR01OZ7tvOnYa9AWz0pa2LYV",
    name: "Thacher Formisano",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Thacher",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },

  {
    id: faker.string.uuid(),
    clerkId: "user_2soMUlkVdivGiW8S9ZHVQVegr9s",
    name: "Thomas Hamblett",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Thomas",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },

  {
    id: faker.string.uuid(),
    clerkId: "user_2soMYj2PqTxsDAb1lt9zd9zmvY4",
    name: "Robert Sobieski",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Robert",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2soMcerKcXld65ikQil4dFUnLLM",
    name: "Brandon Levesque",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Brandon",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },

  // Chat bot user
  {
    id: faker.string.uuid(),
    clerkId: "",
    name: "Virgil AI",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=VirgilAI",
    role: UserRole.SuperAdmin,
    bio: "Chatbot for Virgil AI",
    emailVerified: new Date(),
  },
];

const org: Org = {
  id: faker.string.uuid(),
  name: "Hunter Point Capital",
  clerkId: "org_2soLLQUDOw7FbWtLc6ttRWqgCeu",
  type: OrgType.Lender,
  corpType: CorpType.CCorp,
  createdById: "1",
  createdAt: new Date(),
  updatedAt: new Date(),
  incorporatedAt: faker.date.between({
    from: new Date("2020-01-01"),
    to: new Date("2022-01-01"),
  }),
  ein: faker.string.alphanumeric(10),
  bio: faker.lorem.paragraph(),
  addressId: "00ec2440-b084-4672-bc95-c9ba018520d1",
  webhookSecret: faker.string.alphanumeric(10),
  egnyteAccessTokenId: null,
  azureAccessTokenId: null,
};

const seedUsers = async (users: User[]) => {
  for (const user of users) {
    console.log("user", user);

    await prisma.user.upsert({
      where: { email: user.email ?? "" },
      update: {
        ...user,
      },
      create: {
        ...user,
      },
    });
  }

  const createdUsers = await prisma.user.findMany();

  return createdUsers;
};

const seedAddresses = async () => {
  const addresses = Array.from({ length: 10 }, (_, i) => ({
    id: faker.string.uuid(),
    street: faker.location.streetAddress(),
    city: faker.location.city(),
    state: faker.location.state(),
    zip: faker.location.zipCode(),
  }));

  for (const address of addresses) {
    await prisma.address.upsert({
      where: { id: address.id },
      update: {
        ...address,
      },
      create: {
        ...address,
      },
    });
  }

  const createdAddresses = await prisma.address.findMany();

  return createdAddresses;
};

async function main() {
  // Reset DB

  console.log("Seeding users");
  const users = await seedUsers(userList);
  console.log("Seeding addresses");
  const existingAddresses = await prisma.address.findMany();
  const addresses =
    existingAddresses.length === 0 ? await seedAddresses() : existingAddresses;

  console.log("Seeding organizations");
  await prisma.org.upsert({
    where: { id: org.id },
    update: {
      ...org,
    },
    create: {
      ...org,
      addressId: faker.helpers.arrayElement(addresses).id,
    },
  });

  const orgs = await prisma.org.findMany();

  // Connect users to organizations
  // add all users to the virgil org
  for (const user of users) {
    await prisma.userOrg.upsert({
      where: {
        userId_orgId: {
          userId: user.id,
          orgId: org.id,
        },
      },
      update: {
        userId: user.id,
        orgId: org.id,
      },
      create: {
        userId: user.id,
        orgId: org.id,
      },
    });
  }

  const chatBotId = users.find(
    (user) => user.email === "<EMAIL>",
  )?.id;

  // Seed conversations
  for (const org of orgs) {
    for (const user of users) {
      const existingConversation = await prisma.conversation.count({
        where: {
          participants: {
            some: {
              userId: user.id,
            },
          },
        },
      });

      if (existingConversation === 0) {
        await prisma.conversation.create({
          data: {
            createdAt: new Date(),
            updatedAt: new Date(),

            id: faker.string.uuid(),
            orgId: org.id,
            createdById: user.id,
            messages: {
              create: {
                body: "Hello, how can I help you today?",
                createdById: chatBotId ?? "",
                orgId: org.id,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            },
            participants: {
              create: [
                {
                  userId: chatBotId ?? "",
                  status: ParticipantStatus.ONLINE,
                },
                {
                  userId: user.id,
                  status: ParticipantStatus.ONLINE,
                },
              ],
            },
          },
        });
      }
    }
  }
}

main()
  .then(() => {
    console.log("Seeded database");
  })
  .catch((error) => {
    console.error(error);
  });
