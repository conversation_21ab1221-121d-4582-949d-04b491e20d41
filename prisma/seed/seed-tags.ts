import { type Prisma } from "@prisma/client";
import { stdin, stdout } from "node:process";
import * as readline from "readline/promises";
import { db as prisma } from "~/server/db";

const enabled = false;
// set to the name of the org that matches the database in DATABASE_URL
const orgName = "25 Madison";

type TagWithChildren = Prisma.TagGetPayload<{
  select: {
    name: true;
    color: true;
    summary: true;
    children: {
      select: {
        name: true;
        color: true;
        summary: true;
      };
    };
  };
}>;

const tags: TagWithChildren[] = [
  {
    name: "Risk Management",
    color: "#FFB6C1",
    summary:
      "Risk Management focuses on identifying, assessing, and mitigating risks inherent in financial operations, investments, and data handling processes. It encompasses various sub-categories including Credit Risk which assesses risks related to borrowers' ability to repay loans, Market Risk which evaluates risks due to market fluctuations, and Operational Risk which deals with internal failures. It also includes Portfolio Concentration, Geographic Exposure, Industry/Sector Exposure, Currency Risk, and Leverage Policy.",
    children: [],
  },
  {
    name: "Regulatory Compliance",
    color: "#E6E6FA",
    summary:
      "Regulatory Compliance ensures adherence to laws, regulations, and internal policies during financial data management and reporting. This includes maintaining an Audit Trail for accountability, Anti-Money Laundering measures to prevent illicit financial activities, and adopting Reporting Standards for transparency.",
    children: [],
  },
  {
    name: "Portfolio Management",
    color: "#FFE4B5",
    summary:
      "Portfolio Management covers the strategic management of a collection of investments to optimize returns while controlling risks. It involves Asset Allocation for balanced risk and return, Diversification Strategy to minimize exposure, and Rebalancing Techniques to maintain target asset distribution.",
    children: [],
  },
  {
    name: "Financial Performance",
    color: "#B0E0E6",
    summary:
      "Financial Performance focuses on evaluating and measuring the overall financial outcomes and efficiency of business operations. This includes Profitability Analysis for assessing income, Efficiency Metrics for resource utilization, and Liquidity Measurement for managing cash flow.",
    children: [],
  },
  {
    name: "Capital Planning",
    color: "#F5DEB3",
    summary:
      "Capital Planning involves strategizing and allocating financial resources to support growth initiatives, investments, and operational needs. It includes Budgeting for financial discipline, Forecasting for predicting trends, and Resource Allocation for optimal distribution of capital.",
    children: [],
  },
  {
    name: "General Fund Information",
    color: "#ADD8E6",
    summary: "Use this tag for questions related to general fund information.",
    children: [],
  },
  {
    name: "Human Resources",
    color: "#FFDDC1",
    summary:
      "Human Resources covers questions related to HR inquiries. It includes Talent Management for staff recruitment and retention, Compensation & Benefits details, Organizational Structure, Training & Development initiatives, Workplace Policies, Performance Management, Diversity & Inclusion, Employee Relations, Health & Safety, and HR Compliance.",
    children: [],
  },
  {
    name: "Investment Process",
    color: "#F0E68C",
    summary:
      "The Investment Process tag is used for questions related to the investment process, covering the Investment Pipeline, standardized Due Diligence Process, Deal Sourcing strategies, Exit Strategy planning, and Co-Investment Opportunities.",
    children: [],
  },
  {
    name: "Fund Structure",
    color: "#D8BFD8",
    summary:
      "Fund Structure encompasses Term Length & Extensions, Partnership Agreements, Side Letters, Fund Domicile & Tax Structure, Investment Restrictions, and Key Person Provisions.",
    children: [],
  },
  {
    name: "Legal",
    color: "#F4A460",
    summary:
      "The Legal tag is for questions related to legal matters such as Fund Documentation, Regulatory Filings, Compliance Policies, Litigation & Disputes, Investment Agreements, Intellectual Property, Employment Matters, Corporate Governance, Conflicts of Interest, and Data Privacy & Security.",
    children: [],
  },
  {
    name: "ESG",
    color: "#AFEEEE",
    summary:
      "Use this tag for questions related to ESG (Environmental, Social, and Governance) matters. It includes sub-categories related to environmental, social, and governance issues.",
    children: [],
  },
  {
    name: "Investor Communications",
    color: "#FFDAB9",
    summary:
      "Investor Communications involves questions about general investor communications, covering Subscription Documents, Capital Calls, Distribution Notices, Quarterly/Annual Reports, Limited Partner Meetings, and Marketing Materials.",
    children: [],
  },
  {
    name: "Track Record",
    color: "#D3FFCE",
    summary:
      "Use this tag for questions regarding fund track record and performance history. It includes Historical Performance metrics, Deal Attribution documentation, Investment Case Studies, analysis of Realized/Unrealized Returns, and Portfolio Company Metrics.",
    children: [],
  },
  {
    name: "Operations",
    color: "#C6E2FF",
    summary:
      "Operations involves questions about general fund administration documents. It includes Fund Administration, Valuations Methodology, Cash Management, Service Providers, Technology Systems, and Business Continuity plans.",
    children: [],
  },
  {
    name: "Other",
    color: "#FFFACD",
    summary:
      "Use this tag for questions that don't fit into any other category.",
    children: [],
  },
];

const seedTags = async (tags: TagWithChildren[], orgId: string) => {
  for (const tag of tags) {
    const { children, ...tagWithoutChildren } = tag;

    await prisma.tag.upsert({
      where: { name: tag.name ?? "" },
      update: {
        ...tagWithoutChildren,
      },
      create: {
        ...tagWithoutChildren,
        orgId,
      },
    });
  }

  const createdTags = await prisma.tag.findMany({
    where: {
      orgId,
    },
    include: {
      children: true,
    },
  });

  for (const tag of createdTags) {
    console.log("Processing tag", tag.name);
    for (const children of tags
      .filter((t) => t.name === tag.name)
      .map((t) => t.children)) {
      console.log("Processing children", children);
      for (const child of children) {
        console.log("Processing child", child.name);
        const t = await prisma.tag.upsert({
          where: {
            name: child.name,
          },
          update: {
            parent: {
              connect: {
                id: tag.id,
              },
            },
          },
          create: {
            ...child,
            orgId,
            parentId: tag.id,
          },
        });
      }
    }
  }

  const updatedTags = await prisma.tag.findMany({
    where: {
      orgId,
    },
    include: {
      children: true,
    },
  });

  console.log(
    "Tags",
    updatedTags
      .filter((t) => t.children.length === 0)
      .map((t) => {
        return { name: t.name, children: t.children.map((c) => c.name) };
      }),
  );

  return updatedTags;
};

async function verify() {
  const rl = readline.createInterface({ input: stdin, output: stdout });

  console.log("This will seed/update tags for", orgName);
  console.log("Database URL:", process.env.DATABASE_URL);

  const orgInDb = await prisma.org.findFirstOrThrow({
    where: {
      name: orgName,
    },
  });

  console.log("Org name in DB:", orgInDb?.name);

  const answer = await rl.question("Are you sure you want to continue? (y/n)");

  if (answer !== "y") {
    console.log("Exiting");
    process.exit(0);
  }

  rl.close();
}

async function main() {
  const existingOrg = await prisma.org.findFirst({
    where: { name: orgName },
  });

  if (!existingOrg) {
    throw new Error("Org not found");
  }

  await seedTags(tags, existingOrg.id);
}

if (!enabled) {
  console.log("Edit file to enable");
  process.exit(0);
}

verify()
  .then(() => {
    main()
      .then(() => {
        console.log("Seeded Tags");
      })
      .catch((error) => {
        console.error(error);
      });
  })
  .catch((error) => {
    console.error(error);
  });
