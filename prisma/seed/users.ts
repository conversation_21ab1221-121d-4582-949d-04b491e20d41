import { faker } from "@faker-js/faker";
import { UserRole, UserTitle, type User } from "@prisma/client";

// List of demo users to seed the database with. These should match the names and ids of Users in Clerk.
export const userList: User[] = [
  {
    id: faker.string.uuid(),
    clerkId: "user_2sUfWTjqqUSJNpwC88qFZQd9KYK",
    name: "<PERSON><PERSON> Rom",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Gilad",
    role: UserRole.SuperAdmin,
    bio: "Engineer",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2pD0BVn3h7EoIoNHIDS4M9nY7y3",
    name: "<PERSON>",
    email: "<EMAIL>",
    title: UserTitle.Founder,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Crawford",
    role: UserR<PERSON>.SuperAdmin,
    bio: "Founder of Virgil AI",
    emailVerified: new Date(),
  },

  {
    id: faker.string.uuid(),
    clerkId: "user_2oTfq0704JgxoAWI8N2CZUlctCl",
    name: "Dmitry Tokmakov",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Dmitry",
    role: UserRole.SuperAdmin,
    bio: "Engineer",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2rrhhfievGFNjC9E5F2RvOGlxf8",
    name: "Dmitry Tokmakov",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Dmitry",
    role: UserRole.SuperAdmin,
    bio: "Engineer",
    emailVerified: new Date(),
  },

  {
    id: faker.string.uuid(),
    clerkId: "user_2ucFz5zD3XgVco0SdRHljArsBrQ",
    name: "TruongSinh Tran-Nguyen",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Sinh",
    role: UserRole.SuperAdmin,
    bio: "Engineer",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2tlO10lVXXUbin9SdAzpnI5a1g7",
    name: "Doron Shamia Sadeh",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Doron",
    role: UserRole.SuperAdmin,
    bio: "Engineer",
    emailVerified: new Date(),
  },

  // QA
  {
    id: faker.string.uuid(),
    clerkId: "user_2tGXDzPSFANctXd8VDKzwHLEvA6",
    name: "Thomas Lee",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Thomas",
    role: UserRole.SuperAdmin,
    bio: "QA",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2uRpYW8qXOZ7sGUWmrv404enOav",
    name: "Waqas Zulfiqar",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Waqas",
    role: UserRole.SuperAdmin,
    bio: "QA",
    emailVerified: new Date(),
  },

  // Chat bot user
  {
    id: faker.string.uuid(),
    clerkId: "",
    name: "Virgil AI",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=VirgilAI",
    role: UserRole.SuperAdmin,
    bio: "Chatbot for Virgil AI",
    emailVerified: new Date(),
  },

  // 25Madison
  {
    id: faker.string.uuid(),
    clerkId: "user_2nklWHsyn0Jid4k4dOzyN9mPDCK",
    name: "Sanford Spivey",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Sanford",
    role: UserRole.SuperAdmin,
    bio: "25 Madison",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2nkcprGWWN5b0BLXE9SEBBeVlrd",
    name: "Charlotte Meyers",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Charlotte",
    role: UserRole.SuperAdmin,
    bio: "25 Madison",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2qUGijKJ4WCEjNnkRtJlT427wkt",
    name: "Calista Reyes",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Calista",
    role: UserRole.SuperAdmin,
    bio: "25 Madison",
    emailVerified: new Date(),
  },

  // Test user
  {
    id: faker.string.uuid(),
    clerkId: "user_2lBlw01qSxcwiL38tj5xzMpfwKq",
    name: "Gilad Rom",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Gilad",
    role: UserRole.SuperAdmin,
    bio: "Bio for Gilad Rom",
    emailVerified: new Date(),
  },
];
