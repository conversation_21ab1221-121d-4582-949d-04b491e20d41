import { faker } from "@faker-js/faker";
import { CorpType, type Org, OrgType } from "@prisma/client";

export type OrgWithoutTokens = Omit<
  Org,
  "egnyteAccessTokenId" | "azureAccessTokenId"
>;

// List of demo organizations to seed the database with. These should match the names and ids of Orgs in Clerk.
export const orgList: OrgWithoutTokens[] = [
  {
    id: faker.string.uuid(),
    name: "Virgil <PERSON>",
    clerkId: "org_2lN93uPdFW3Ci7x0PiOyObyyAk5",
    type: OrgType.Lender,
    corpType: CorpType.CCorp,
    createdById: "1",
    createdAt: new Date(),
    updatedAt: new Date(),
    incorporatedAt: faker.date.between({
      from: new Date("2020-01-01"),
      to: new Date("2022-01-01"),
    }),
    ein: faker.string.alphanumeric(10),
    bio: faker.lorem.paragraph(),
    addressId: "1",
    webhookSecret: faker.string.alphanumeric(10),
  },
  {
    id: faker.string.uuid(),
    name: "<PERSON><PERSON>",
    clerkId: "org_2m9bVVtUiGAJxC4s7Qwn1IoYvsT",
    type: OrgType.Borrower,
    corpType: CorpType.CCorp,
    createdById: "1",
    createdAt: new Date(),
    updatedAt: new Date(),
    incorporatedAt: faker.date.between({
      from: new Date("2020-01-01"),
      to: new Date("2022-01-01"),
    }),
    ein: faker.string.alphanumeric(10),
    bio: faker.lorem.paragraph(),
    addressId: "1",
    webhookSecret: faker.string.alphanumeric(10),
  },
  {
    id: faker.string.uuid(),
    name: "Lacking Foods",
    clerkId: "org_2m9bWzuzsBAtrEYiEWUp9ZzILh4",
    type: OrgType.Borrower,
    corpType: CorpType.CCorp,
    createdById: "1",
    createdAt: new Date(),
    updatedAt: new Date(),
    incorporatedAt: faker.date.between({
      from: new Date("2020-01-01"),
      to: new Date("2022-01-01"),
    }),
    ein: faker.string.alphanumeric(10),
    bio: faker.lorem.paragraph(),
    addressId: "1",
    webhookSecret: faker.string.alphanumeric(10),
  },
  {
    id: faker.string.uuid(),
    name: "Trader Moses",
    clerkId: "org_2m9bYF2ocMrwHSrdV2eAwwoJDci",
    type: OrgType.Borrower,
    corpType: CorpType.CCorp,
    createdById: "1",
    createdAt: new Date(),
    updatedAt: new Date(),
    incorporatedAt: faker.date.between({
      from: new Date("2020-01-01"),
      to: new Date("2022-01-01"),
    }),
    ein: faker.string.alphanumeric(10),
    bio: faker.lorem.paragraph(),
    addressId: "1",
    webhookSecret: faker.string.alphanumeric(10),
  },
];
