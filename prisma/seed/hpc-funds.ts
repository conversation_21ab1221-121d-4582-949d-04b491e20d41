import { type Fund, type FundFamily } from "@prisma/client";

export type FundFamilyType = Omit<
  FundFamily,
  "orgId" | "createdAt" | "updatedAt" | "id"
> & {
  funds: Omit<
    Fund,
    "orgId" | "createdAt" | "updatedAt" | "id" | "fundFamilyId"
  >[];
};

export const funds: FundFamilyType[] = [
  {
    name: "Hunter Point Capital",
    description: "",
    funds: [
      {
        name: "HPC Investors, L.P.",
        description: "This fund is also known as: HPC Fund I, HPC GP Stakes Fund I, Stakes Fund I",
        strategy: "",
      },
      {
        name: "HPC GPFS - NAV Lending, L.P.",
        description: "This fund is also known as: HPC NAV Lending Fund I, NAV Lending Fund I, NAV Fund I, GPFS NAV Lending Fund I, GPFS NAV Lending",
        strategy: "",
      },
      {
        name: "HPC GPFS - Preferred, L.P.",
        description: "This fund is also known as: HPC Preferred Solutions Fund I, Preferred Solutions Fund I, Pref Fund I, GPFS Preferred Solutions Fund I, GPFS Preferred Solutions",
        strategy: "",
      },     
    ],
  },
];
