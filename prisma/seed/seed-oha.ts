import { faker } from "@faker-js/faker";
import {
  CorpType,
  Org,
  OrgType,
  ParticipantStatus,
  User,
  UserRole,
  UserTitle,
} from "@prisma/client";
import { db as prisma } from "~/server/db";

const userList: User[] = [
  // Virgil and 25M users
  {
    id: faker.string.uuid(),
    clerkId: "user_2sUjTTykSMjgnKVlku2WgArrcqF",
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Gilad",
    role: UserRole.SuperAdmin,
    bio: "Engineering Lead of Virgil AI",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sXNSwZNmGr4hckRNdjKH0fcHxn",
    name: "<PERSON>",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Crawford",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sXhyaOYR8dhLUTxCOXi2gBSxIL",
    name: "Calista Reyes",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Calista",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sfowjPbIrwsdqlk5ZlwU74a84k",
    name: "Sanford Spivey",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Kathrine",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },

  // End Virgil and 25M users

  // OHA Users
  {
    id: faker.string.uuid(),
    clerkId: "user_2sob7FXCKYHLxSBA4jcHg7l9hkf",
    name: "Kurt Wehr",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Kurt",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobDBnVECwaP9hVdVTYkC74NeS",
    name: "Brendan Caltavuturo",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Brendan",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobJzIw4TEljypZ1zMPsDVV5lY",
    name: "Yonna Xhixho",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Yonna",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobQjN2B4ujyntEflC44E9eCFg",
    name: "Emma Lee",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Emma",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobU2lIMjBRfl1FFA99GCsGzPZ",
    name: "Erin Coghlan",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Erin",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobXWnXNh9piMH2ouyR6cDCocD",
    name: "Nikhita Hingorani",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Nikhita",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobaQXXyDKADUwiQS2cHn8Hg3b",
    name: "Will Seffens",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Will",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobdQI1Zih35arro54M0whUu53",
    name: "Tony Rozman",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Tony",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobgMUy0h1Y1p2IaacuVH2gRQj",
    name: "Sahara Majumber",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Sahara",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobjlJAdjlJ2hFML4jFByHTq5h",
    name: "Astrid Day",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Astrid",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobmJUXHGN7PreY31uPT2togRy",
    name: "Julianna Rosen",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Julianna",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobpzZ1miDfnuV1sbmkpCTEb1x",
    name: "Oliver Chase",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Oliver",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobt1rAA4LK77V62ihLGd2lA8N",
    name: "Edward Allen",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Edward",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2sobx1VtcKhYUaedE9cqjVa8FJ7",
    name: "Andre Quintiliani",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Andre",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2soc1iGP4kXCbZ9liUhkc2saAGV",
    name: "Joe Castilla",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Joe",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2soc4VQ7gN0QtN5FdW62QpSQxWl",
    name: "Andrew Fleisher",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Andrew",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
  {
    id: faker.string.uuid(),
    clerkId: "user_2su9PsDld7zK6vN2phgLAJiwYnY",
    name: "Xavier Allain",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Xavier",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },

  // End OHA Users

  // Chat bot user
  {
    id: faker.string.uuid(),
    clerkId: "",
    name: "Virgil AI",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=VirgilAI",
    role: UserRole.SuperAdmin,
    bio: "Chatbot for Virgil AI",
    emailVerified: new Date(),
  },
];

const org: Org = {
  id: faker.string.uuid(),
  name: "Oak Hill Advisors",
  clerkId: "org_2soXlsTuYv3FtbpifMHgUedkYTW",
  type: OrgType.Lender,
  corpType: CorpType.CCorp,
  createdById: "1",
  createdAt: new Date(),
  updatedAt: new Date(),
  incorporatedAt: faker.date.between({
    from: new Date("2020-01-01"),
    to: new Date("2022-01-01"),
  }),
  ein: faker.string.alphanumeric(10),
  bio: faker.lorem.paragraph(),
  addressId: "00ec2440-b084-4672-bc95-c9ba018520d1",
  webhookSecret: faker.string.alphanumeric(10),
  egnyteAccessTokenId: null,
  azureAccessTokenId: null,
};

const seedUsers = async (users: User[]) => {
  for (const user of users) {
    console.log("user", user);

    await prisma.user.upsert({
      where: { email: user.email ?? "" },
      update: {
        ...user,
      },
      create: {
        ...user,
      },
    });
  }

  const createdUsers = await prisma.user.findMany();

  return createdUsers;
};

const seedAddresses = async () => {
  const addresses = Array.from({ length: 10 }, (_, i) => ({
    id: faker.string.uuid(),
    street: faker.location.streetAddress(),
    city: faker.location.city(),
    state: faker.location.state(),
    zip: faker.location.zipCode(),
  }));

  for (const address of addresses) {
    await prisma.address.upsert({
      where: { id: address.id },
      update: {
        ...address,
      },
      create: {
        ...address,
      },
    });
  }

  const createdAddresses = await prisma.address.findMany();

  return createdAddresses;
};

async function main() {
  // Reset DB

  console.log("Seeding users");
  const users = await seedUsers(userList);
  console.log("Seeding addresses");
  const existingAddresses = await prisma.address.findMany();
  const addresses =
    existingAddresses.length === 0 ? await seedAddresses() : existingAddresses;

  console.log("Seeding organizations");

  const existingOrg = await prisma.org.findFirst({
    where: { name: org.name },
  });

  if (existingOrg) {
    await prisma.org.update({
      where: { id: existingOrg.id },
      data: {
        ...org,
        id: existingOrg.id,
        addressId: faker.helpers.arrayElement(addresses).id,
      },
    });
  } else {
    await prisma.org.create({
      data: {
        ...org,
        addressId: faker.helpers.arrayElement(addresses).id,
      },
    });
  }

  const orgs = await prisma.org.findMany();

  const orgId = existingOrg?.id ?? orgs[0]?.id;

  // Connect users to organizations
  // add all users to the virgil org
  for (const user of users) {
    console.log("connecting user", user.id, orgId);
    await prisma.userOrg.upsert({
      where: {
        userId_orgId: {
          userId: user.id,
          orgId: orgId ?? "",
        },
      },
      update: {
        userId: user.id,
        orgId: orgId ?? "",
      },
      create: {
        userId: user.id,
        orgId: orgId ?? "",
      },
    });
  }

  const chatBotId = users.find(
    (user) => user.email === "<EMAIL>",
  )?.id;

  // Seed conversations
  for (const org of orgs) {
    for (const user of users) {
      const existingConversation = await prisma.conversation.count({
        where: {
          participants: {
            some: {
              userId: user.id,
            },
          },
        },
      });

      if (existingConversation === 0) {
        await prisma.conversation.create({
          data: {
            createdAt: new Date(),
            updatedAt: new Date(),

            id: faker.string.uuid(),
            orgId: orgId ?? "",
            createdById: user.id,
            messages: {
              create: {
                body: "Hello, how can I help you today?",
                createdById: chatBotId ?? "",
                orgId: orgId ?? "",
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            },
            participants: {
              create: [
                {
                  userId: chatBotId ?? "",
                  status: ParticipantStatus.ONLINE,
                },
                {
                  userId: user.id,
                  status: ParticipantStatus.ONLINE,
                },
              ],
            },
          },
        });
      }
    }
  }
}

main()
  .then(() => {
    console.log("Seeded database");
  })
  .catch((error) => {
    console.error(error);
  });
