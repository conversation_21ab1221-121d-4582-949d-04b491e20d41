import { ParticipantStatus } from "@prisma/client";
import { db as prisma } from "~/server/db";

// Set this to the org name in Clerk/DB
const ORG_NAME = "Virgil AI";

const enabled = false;

const createConversation = async (userId: string) => {
  const chatBot = await prisma.user.findFirst({
    where: {
      email: "<EMAIL>",
    },
  });

  const org = await prisma.org.findFirst({
    where: {
      name: ORG_NAME,
    },
  });

  const existingConvesation = await prisma.conversation.findFirst({
    where: {
      participants: {
        some: {
          userId: userId,
        },
      },
    },
  });

  if (existingConvesation) {
    return existingConvesation;
  }

  return await prisma.conversation.create({
    data: {
      createdAt: new Date(),
      updatedAt: new Date(),
      orgId: org?.id ?? "",
      createdById: userId,
      messages: {
        create: {
          body: "Hello, how can I help you today?",
          createdById: chatBot?.id ?? "",
          orgId: org?.id ?? "",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      },
      participants: {
        create: [
          {
            userId: chatBot?.id ?? "",
            status: ParticipantStatus.ONLINE,
          },
          {
            userId: userId,
            status: ParticipantStatus.ONLINE,
          },
        ],
      },
    },
  });
};

async function main() {
  if (enabled) {
    await createConversation("5F91A268-348F-4128-822F-7CD5A46E69FD");
  } else {
    console.log("Edit add-user.ts to enable user seeding");
  }
}

main()
  .then(() => {
    console.log("Done");
  })
  .catch((error) => {
    console.error(error);
  });
