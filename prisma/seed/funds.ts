import { type Fund, type FundFamily } from "@prisma/client";
import { db as prisma } from "~/server/db";
export type FundFamilyType = Omit<
  FundFamily,
  "orgId" | "createdAt" | "updatedAt" | "id"
> & {
  funds: Omit<
    Fund,
    "orgId" | "createdAt" | "updatedAt" | "id" | "fundFamilyId"
  >[];
};

export const funds: FundFamilyType[] = [
  {
    name: "Vistria",
    description:
      "Vistria is a private equity firm that invests in middle market companies.",
    funds: [
      {
        name: "Vistria Housing Fund",
        description:
          "Vistria Housing Fund is a private equity fund that invests in housing companies.",
        strategy: "Housing",
      },
    ],
  },
  {
    name: "Green Forrest",
    description: `Green Forrest Fund is a private equity firm focused on control and non-control investments across various sectors including technology, industrials, consumer, healthcare, and financial services.`,
    funds: [
      {
        name: "Green Forrest Fund",
        description:
          "Green Forrest Fund is a private equity firm focused on control and non-control investments across various sectors including technology, industrials, consumer, healthcare, and financial services.",
        strategy: "Green Energy",
      },
    ],
  },
  {
    name: "Clearlake Capital Partners",
    description: `Founded in 2006, Clearlake is led by <PERSON> and <PERSON><PERSON><PERSON> (the “Co-Founders”), who have together
built a team that has invested 11 funds focused on control and non-control investing strategies. With a sector-focused
approach, the Firm seeks to partner with experienced management teams by providing patient, long-term capital to
businesses that can benefit from Clearlake's operational improvement approach, O.P.S.® (Operations, People,
Strategy). The Firm's target core sectors are technology, industrials, and consumer. Headquartered in Santa Monica,
California, Clearlake believes its team has complementary skills, bringing a balance of private equity, special situations,
and credit/distressed experience, as well as entrepreneurial abilities.`,
    funds: [
      {
        name: "CCP VII",
        description: `Private Equity/Special Situations/Distressed `,
        strategy: "Technology",
      },
      {
        name: "CCP VI",
        description: `Private Equity/Special Situations/Distressed`,
        strategy: "Technology",
      },
      {
        name: "CCP V",
        description: `Private Equity/Special Situations/Distressed`,
        strategy: "Technology",
      },
      {
        name: "CCP IV",
        description: "",
        strategy: "Technology",
      },
      {
        name: "CCP III",
        description: "",
        strategy: "Technology",
      },
      {
        name: "CCP II",
        description: "",
        strategy: "Technology",
      },
      {
        name: "COP III",
        description: "",
        strategy: "Technology",
      },
      {
        name: "COP II",
        description: "",
        strategy: "Technology",
      },
    ],
  },
  {
    name: "Francisco Partners",
    description: `Francisco Partners is a leading technology investment firm with deep sector focus and a track record of delivering outstanding returns. Through our private equity and credit funds, we provide flexible capital and partnership to growth-aspiring technology companies.`,
    funds: [
      {
        name: "FP VI",
        description: "",
        strategy: "Technology",
      },
      {
        name: "FP V",
        description: "",
        strategy: "Technology",
      },
      {
        name: "FP IV",
        description: "",
        strategy: "Technology",
      },
      {
        name: "FP III",
        description: "",
        strategy: "Technology",
      },
      {
        name: "FP II",
        description: "",
        strategy: "Technology",
      },
      {
        name: "FP Agility II",
        description: "",
        strategy: "Technology",
      },
      {
        name: "FP Agility",
        description: "",
        strategy: "Technology",
      },
    ],
  },
  {
    name: "Bottleneck Credit Fund",
    description: "",
    funds: [
      {
        name: "Bottleneck Credit Fund I, LP",
        description: `Private Credit Fund, primarily focusing on Inventory financing and cost of goods sold (COGS) loans for consumer
packaged goods (CPG) companies.`,
        strategy: "Technology",
      },
    ],
  },
  {
    name: "Gallatin Capital Partners",
    description:
      "Gallatin Capital Partners was founded in 2008 by James Gallatin and Robert Chen, two veteran private equity investors with prior experience at leading firms. The firm began with a $750 million inaugural fund focused on middle-market buyouts in North America.",
    funds: [
      {
        name: "Gallatin Equity I",
        description:
          "($750 million, 2009 vintage) is fully realized with exceptional performance metrics. The fund has delivered a 2.9x Gross MOIC and 2.4x Net MOIC, with Gross IRR of 28.3% and Net IRR of 23.7%.",
        strategy: "Technology",
      },
      {
        name: "Gallatin Equity II",
        description:
          " ($1.1 billion, 2013 vintage) is in late harvesting stage with 87% of invested capital realized.",
        strategy: "Technology",
      },
      {
        name: "Gallatin Equity III",
        description:
          "($1.8 billion, 2017 vintage) is in active harvesting mode with 42% of the portfolio realized. ",
        strategy: "Technology",
      },
      {
        name: "Gallatin Equity IV",
        description:
          " ($2.3 billion, 2020 vintage) remains in the value creation phase with 10% of the portfolio realized to date.",
        strategy: "Technology",
      },
      {
        name: "Gallatin Equity V",
        description:
          " ($2.75 billion, 2022 vintage) is in active deployment phase with promising early results.",
        strategy: "Technology",
      },
    ],
  },
];

export const seedFunds = async (funds: FundFamilyType[], orgId: string) => {
  for (const fund of funds) {
    console.log("Seeding fund", fund.name);

    await prisma.fundFamily.upsert({
      where: { name: fund.name ?? "", orgId },
      update: {
        ...fund,
        orgId,
        funds: {
          connect: fund.funds.map((f) => ({
            name: f.name,
          })),
        },
      },
      create: {
        ...fund,
        orgId,
        funds: {
          create: fund.funds.map((f) => ({
            ...f,
            orgId,
          })),
        },
      },
    });
  }

  const createdFunds = await prisma.fundFamily.findMany({
    where: {
      orgId,
    },
    include: {
      funds: true,
    },
  });

  return createdFunds;
};
