import { faker } from "@faker-js/faker";
import {
  ParticipantStatus,
  type User,
  UserRole,
  UserTitle,
} from "@prisma/client";
import { db as prisma } from "~/server/db";

// Set this to the org name in Clerk/DB
const ORG_NAME = "Virgil AI";

const enabled = false;

type UserWithoutId = Omit<User, "id">;
// List of demo users to seed the database with. These should match the names and ids of Users in Clerk.
const userList: UserWithoutId[] = [
  {
    clerkId: "user_2ud6ipXMSUkfzI2mdELbxpPqLpY",
    name: "<PERSON>",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Cole",
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  },
];

const seedUsers = async (users: UserWithoutId[]) => {
  const createdUsers = [];
  for (const user of users) {
    const u = await prisma.user.upsert({
      where: {
        clerkId: user.clerkId || "",
      },
      update: user,
      create: {
        ...user,
        id: faker.string.uuid(),
      },
    });
    createdUsers.push(u);
  }

  return createdUsers;
};

const createConversation = async (userId: string) => {
  const chatBot = await prisma.user.findFirst({
    where: {
      email: "<EMAIL>",
    },
  });

  const org = await prisma.org.findFirst({
    where: {
      name: ORG_NAME,
    },
  });

  const existingConvesation = await prisma.conversation.findFirst({
    where: {
      participants: {
        some: {
          userId: userId,
        },
      },
    },
  });

  if (existingConvesation) {
    return existingConvesation;
  }

  return await prisma.conversation.create({
    data: {
      createdAt: new Date(),
      updatedAt: new Date(),
      orgId: org?.id ?? "",
      createdById: userId,
      messages: {
        create: {
          body: "Hello, how can I help you today?",
          createdById: chatBot?.id ?? "",
          orgId: org?.id ?? "",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      },
      participants: {
        create: [
          {
            userId: chatBot?.id ?? "",
            status: ParticipantStatus.ONLINE,
          },
          {
            userId: userId,
            status: ParticipantStatus.ONLINE,
          },
        ],
      },
    },
  });
};

async function addUserToOrg(users: User[]) {
  const org = await prisma.org.findFirst({
    where: {
      name: ORG_NAME,
    },
  });

  if (!org) {
    throw new Error(`${ORG_NAME} org not found`);
  }

  for (const user of users) {
    await prisma.userOrg.upsert({
      where: {
        userId_orgId: {
          userId: user.id,
          orgId: org.id,
        },
      },
      update: {},
      create: {
        userId: user.id,
        orgId: org.id,
      },
    });
  }
}

async function main() {
  if (enabled) {
    console.log("Seeding users");
    const users = await seedUsers(userList);
    await addUserToOrg(users);

    for (const user of users) {
      await createConversation(user.id);
    }
  } else {
    console.log("Edit add-user.ts to enable user seeding");
  }
}

main()
  .then(() => {
    console.log("Done");
  })
  .catch((error) => {
    console.error(error);
  });
