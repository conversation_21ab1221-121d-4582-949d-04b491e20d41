import { faker } from "@faker-js/faker";
import {
  CorpType,
  Org,
  OrgType,
  ParticipantStatus,
  User,
  UserRole,
  UserTitle,
} from "@prisma/client";
import { db as prisma } from "~/server/db";

// List of users to seed the database with. These should match the names and ids of Users in Clerk.
const userList: User[] = [
  {
    id: faker.string.uuid(),
    clerkId: "user_2sUjTTykSMjgnKVlku2WgArrcqF",
    name: "<PERSON><PERSON>om",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=Gilad",
    role: UserRole.SuperAdmin,
    bio: "Engineering Lead of Virgil AI",
    emailVerified: new Date(),
  },

  // Chat bot user
  {
    id: faker.string.uuid(),
    clerkId: "",
    name: "Virgil <PERSON>",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=VirgilAI",
    role: UserR<PERSON>.SuperAdmin,
    bio: "Chatbot for Virgil AI",
    emailVerified: new Date(),
  },
];

const org: Org = {
  id: faker.string.uuid(),
  name: "Vistria",
  clerkId: "org_2sWVAA8ZkI89BxiEcmXiLFrvoMU",
  type: OrgType.Lender,
  corpType: CorpType.CCorp,
  createdById: "1",
  createdAt: new Date(),
  updatedAt: new Date(),
  incorporatedAt: faker.date.between({
    from: new Date("2020-01-01"),
    to: new Date("2022-01-01"),
  }),
  ein: faker.string.alphanumeric(10),
  bio: faker.lorem.paragraph(),
  addressId: "1",
  webhookSecret: faker.string.alphanumeric(10),
  egnyteAccessTokenId: null,
  azureAccessTokenId: null,
};

const seedUsers = async (users: User[]) => {
  for (const user of users) {
    await prisma.user.upsert({
      where: { clerkId: user.clerkId ?? "" },
      update: {
        ...user,
      },
      create: {
        ...user,
      },
    });
  }

  const createdUsers = await prisma.user.findMany();

  return createdUsers;
};

const seedAddresses = async () => {
  const addresses = Array.from({ length: 10 }, (_, i) => ({
    id: faker.string.uuid(),
    street: faker.location.streetAddress(),
    city: faker.location.city(),
    state: faker.location.state(),
    zip: faker.location.zipCode(),
  }));

  for (const address of addresses) {
    await prisma.address.upsert({
      where: { id: address.id },
      update: {
        ...address,
      },
      create: {
        ...address,
      },
    });
  }

  const createdAddresses = await prisma.address.findMany();

  return createdAddresses;
};

async function main() {
  // Reset DB

  console.log("Seeding users");
  const users = await seedUsers(userList);
  console.log("Seeding addresses");
  const existingAddresses = await prisma.address.findMany();
  const addresses =
    existingAddresses.length === 0 ? await seedAddresses() : existingAddresses;

  console.log("Seeding organizations");
  await prisma.org.upsert({
    where: { id: org.id },
    update: {
      ...org,
    },
    create: {
      ...org,
      addressId: faker.helpers.arrayElement(addresses).id,
    },
  });

  const orgs = await prisma.org.findMany();

  // Connect users to organizations
  // add all users to the virgil org
  for (const user of users) {
    await prisma.userOrg.upsert({
      where: {
        userId_orgId: {
          userId: user.id,
          orgId: org.id,
        },
      },
      update: {
        userId: user.id,
        orgId: org.id,
      },
      create: {
        userId: user.id,
        orgId: org.id,
      },
    });
  }

  const chatBotId = users.find(
    (user) => user.email === "<EMAIL>",
  )?.id;

  const existingConversations = await prisma.conversation.findMany();
  if (existingConversations.length === 0) {
    // Seed conversations
    for (const org of orgs) {
      for (const user of users) {
        await prisma.conversation.create({
          data: {
            createdAt: new Date(),
            updatedAt: new Date(),

            id: faker.string.uuid(),
            orgId: org.id,
            createdById: user.id,
            messages: {
              create: {
                body: "Hello, how can I help you today?",
                createdById: chatBotId ?? "",
                orgId: org.id,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            },
            participants: {
              create: [
                {
                  userId: chatBotId ?? "",
                  status: ParticipantStatus.ONLINE,
                },
                {
                  userId: user.id,
                  status: ParticipantStatus.ONLINE,
                },
              ],
            },
          },
        });
      }
    }
  }
}

main()
  .then(() => {
    console.log("Seeded database");
  })
  .catch((error) => {
    console.error(error);
  });
