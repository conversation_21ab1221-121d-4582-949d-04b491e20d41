import { type Fund, type FundFamily } from "@prisma/client";

export type FundFamilyType = Omit<
  FundFamily,
  "orgId" | "createdAt" | "updatedAt" | "id"
> & {
  funds: Omit<
    Fund,
    "orgId" | "createdAt" | "updatedAt" | "id" | "fundFamilyId"
  >[];
};

export const funds: FundFamilyType[] = [
  {
    name: "Vistria",
    description: "",
    funds: [
      {
        name: "Vistria Housing Fund, LP",
        description: "",
        strategy: "Real Estate",
      },
      {
        name: "Vistria Housing Fund F-1, LP",
        description: "",
        strategy: "Real Estate",
      },
      {
        name: "Vistria Housing Fund F-2, LP",
        description: "",
        strategy: "Real Estate",
      },

      {
        name: "Vistria Housing Fund GP, LP",
        description: "",
        strategy: "Real Estate",
      },

      {
        name: "Vistria Fund I, LP",
        description: "",
        strategy: "Real Estate",
      },
      {
        name: "Vistria Fund II, LP",
        description: "",
        strategy: "Real Estate",
      },
      {
        name: "Vistria Fund III, LP",
        description: "",
        strategy: "Real Estate",
      },
      {
        name: "Vistria Fund IV, LP",
        description: "",
        strategy: "Real Estate",
      },
      {
        name: "Vistria Fund V, LP",
        description: "",
        strategy: "Real Estate",
      },
      {
        name: "Structured Credit Fund I, L.P.",
        description: "",
        strategy: "Real Estate",
      },
    ],
  },
];
