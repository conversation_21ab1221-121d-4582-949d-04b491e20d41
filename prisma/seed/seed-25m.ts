import { faker } from "@faker-js/faker";
import {
  CorpType,
  type Org,
  OrgType,
  ParticipantStatus,
  type User,
  UserRole,
  UserTitle,
} from "@prisma/client";
import { db as prisma } from "~/server/db";

const usersClerk = [
  {
    id: "user_2wXDdBhh5kcTnE6CqcNO4Xk8kCw",
    object: "user",
    username: null,
    first_name: null,
    last_name: null,
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18yc1F2bWpvemV2NFJhQ1pTWHF4TWcyQUNaVVkiLCJyaWQiOiJ1c2VyXzJ3WERkQmhoNWtjVG5FNkNxY05PNFhrOGtDdyJ9",
    has_image: false,
    primary_email_address_id: "idn_2wXDd9Voa6ohR5DsqB31eKqsEIw",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: true,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2wXDd9Voa6ohR5DsqB31eKqsEIw",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "admin",
          attempts: null,
          expire_at: null,
        },
        linked_to: [],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {},
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: null,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: false,
    last_active_at: null,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url: "https://www.gravatar.com/avatar?d=mp",
  },
  {
    id: "user_2wXDXWAJF9DiVSW6aVoKAw0Frpx",
    object: "user",
    username: null,
    first_name: null,
    last_name: null,
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18yc1F2bWpvemV2NFJhQ1pTWHF4TWcyQUNaVVkiLCJyaWQiOiJ1c2VyXzJ3WERYV0FKRjlEaVZTVzZhVm9LQXcwRnJweCJ9",
    has_image: false,
    primary_email_address_id: "idn_2wXDXi0J0VdJSseGMUibatJGtuK",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: true,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2wXDXi0J0VdJSseGMUibatJGtuK",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "admin",
          attempts: null,
          expire_at: null,
        },
        linked_to: [],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {},
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: null,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: false,
    last_active_at: null,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url: "https://www.gravatar.com/avatar?d=mp",
  },
  {
    id: "user_2wXDQ86XidkpTLO79zLB6qlUPHo",
    object: "user",
    username: null,
    first_name: null,
    last_name: null,
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18yc1F2bWpvemV2NFJhQ1pTWHF4TWcyQUNaVVkiLCJyaWQiOiJ1c2VyXzJ3WERRODZYaWRrcFRMTzc5ekxCNnFsVVBIbyJ9",
    has_image: false,
    primary_email_address_id: "idn_2wXDQ5DnCxhBhMFV6wgkg6AZXVd",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: true,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2wXDQ5DnCxhBhMFV6wgkg6AZXVd",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "admin",
          attempts: null,
          expire_at: null,
        },
        linked_to: [],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {},
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: null,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: false,
    last_active_at: null,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url: "https://www.gravatar.com/avatar?d=mp",
  },
  {
    id: "user_2wXDNwSkJi7SczFqVxJs7oPlB1l",
    object: "user",
    username: null,
    first_name: null,
    last_name: null,
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18yc1F2bWpvemV2NFJhQ1pTWHF4TWcyQUNaVVkiLCJyaWQiOiJ1c2VyXzJ3WEROd1NrSmk3U2N6RnFWeEpzN29QbEIxbCJ9",
    has_image: false,
    primary_email_address_id: "idn_2wXDNvSBWa0lSrdFbObLHowp9To",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: true,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2wXDNvSBWa0lSrdFbObLHowp9To",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "admin",
          attempts: null,
          expire_at: null,
        },
        linked_to: [],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {},
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: null,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: false,
    last_active_at: null,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url: "https://www.gravatar.com/avatar?d=mp",
  },
  {
    id: "user_2wXDLEgwH9RanVxIWZdysoi4l0d",
    object: "user",
    username: null,
    first_name: null,
    last_name: null,
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18yc1F2bWpvemV2NFJhQ1pTWHF4TWcyQUNaVVkiLCJyaWQiOiJ1c2VyXzJ3WERMRWd3SDlSYW5WeElXWmR5c29pNGwwZCJ9",
    has_image: false,
    primary_email_address_id: "idn_2wXDLD886rhTpNe5XMqCOG0xIAC",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: true,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2wXDLD886rhTpNe5XMqCOG0xIAC",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "admin",
          attempts: null,
          expire_at: null,
        },
        linked_to: [],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {},
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: null,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: false,
    last_active_at: null,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url: "https://www.gravatar.com/avatar?d=mp",
  },
  {
    id: "user_2vrf8GQfEtI06fTobjM5IYvU2Zg",
    object: "user",
    username: null,
    first_name: "TruongSinh",
    last_name: "Tran-Nguyen",
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoicHJveHkiLCJzcmMiOiJodHRwczovL2ltYWdlcy5jbGVyay5kZXYvb2F1dGhfZ29vZ2xlL2ltZ18ydnJmOE5WNEgzS09XMjBHVGhINzd2WGxvVnIifQ",
    has_image: true,
    primary_email_address_id: "idn_2vrf4qtoEjkoFxD9xJRcZ4odEBT",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: false,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2vrf4qtoEjkoFxD9xJRcZ4odEBT",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "ticket",
          attempts: null,
          expire_at: null,
        },
        linked_to: [
          {
            type: "oauth_google",
            id: "idn_2vrf8GvbjuPZlBuYjEHu21j76zA",
          },
        ],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [
      {
        object: "google_account",
        id: "idn_2vrf8GvbjuPZlBuYjEHu21j76zA",
        provider: "oauth_google",
        identification_id: "idn_2vrf8GvbjuPZlBuYjEHu21j76zA",
        provider_user_id: "118153185824898354377",
        approved_scopes:
          "email https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile openid profile",
        email_address: "<EMAIL>",
        first_name: "TruongSinh",
        last_name: "Tran-Nguyen",
        avatar_url:
          "https://lh3.googleusercontent.com/a/ACg8ocI0ZureMaEfG6C9E0dTUU_NdpWF6oTKmHmVFxyltTJABElWyw=s1000-c",
        image_url:
          "https://img.clerk.com/eyJ0eXBlIjoicHJveHkiLCJzcmMiOiJodHRwczovL2xoMy5nb29nbGV1c2VyY29udGVudC5jb20vYS9BQ2c4b2NJMFp1cmVNYUVmRzZDOUUwZFRVVV9OZHBXRjZvVEttSG1WRnh5bHRUSkFCRWxXeXc9czEwMDAtYyIsInMiOiIxT3hVZHlmamRUaFNwL1J1T0FIWkdwWTE0QnFJZ1crM3pYQUtXMnlndWhFIn0",
        username: null,
        public_metadata: {},
        label: null,
        created_at: *************,
        updated_at: *************,
        verification: {
          status: "verified",
          strategy: "oauth_google",
          attempts: null,
          expire_at: *************,
        },
        external_account_id: "eac_2vrf8J7kv4ZbXwtBkWzfoHJxXS1",
        google_id: "118153185824898354377",
        given_name: "TruongSinh",
        family_name: "Tran-Nguyen",
        picture:
          "https://lh3.googleusercontent.com/a/ACg8ocI0ZureMaEfG6C9E0dTUU_NdpWF6oTKmHmVFxyltTJABElWyw=s1000-c",
      },
    ],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {
      demoMode: "true",
    },
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: *************,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: false,
    last_active_at: *************,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url:
      "https://images.clerk.dev/oauth_google/img_2vrf8NV4H3KOW20GThH77vXloVr",
  },
  {
    id: "user_2tBa9CK2HuXrMLPGszT0AhWmRAx",
    object: "user",
    username: null,
    first_name: "Dmitry",
    last_name: "Tokmakov",
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18yc1F2bWpvemV2NFJhQ1pTWHF4TWcyQUNaVVkiLCJyaWQiOiJ1c2VyXzJ0QmE5Q0sySHVYck1MUEdzelQwQWhXbVJBeCIsImluaXRpYWxzIjoiRFQifQ",
    has_image: false,
    primary_email_address_id: "idn_2tBa9FPofvJuQYPYyVf1iBcUyCM",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: true,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2tBa9FPofvJuQYPYyVf1iBcUyCM",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "admin",
          attempts: null,
          expire_at: null,
        },
        linked_to: [
          {
            type: "oauth_google",
            id: "idn_2tBaHZhaYxc5RCfEb5OSiKsZkpb",
          },
        ],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [
      {
        object: "google_account",
        id: "idn_2tBaHZhaYxc5RCfEb5OSiKsZkpb",
        provider: "oauth_google",
        identification_id: "idn_2tBaHZhaYxc5RCfEb5OSiKsZkpb",
        provider_user_id: "114710527711368025536",
        approved_scopes:
          "email https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile openid profile",
        email_address: "<EMAIL>",
        first_name: "Dmitry",
        last_name: "Tokmakov",
        avatar_url:
          "https://lh3.googleusercontent.com/a/ACg8ocJB_TU-mfpGH5VUFVAkIZiozOrBXjNZKfho1IyPR9n_smp4mg=s1000-c",
        image_url:
          "https://img.clerk.com/eyJ0eXBlIjoicHJveHkiLCJzcmMiOiJodHRwczovL2xoMy5nb29nbGV1c2VyY29udGVudC5jb20vYS9BQ2c4b2NKQl9UVS1tZnBHSDVWVUZWQWtJWmlvek9yQlhqTlpLZmhvMUl5UFI5bl9zbXA0bWc9czEwMDAtYyIsInMiOiJNcHcrMmlkbktOYmwveHBqTUJuaDZjRnZSNmhjQUlMYWNBaU51VDdwZFN3In0",
        username: "",
        public_metadata: {},
        label: null,
        created_at: *************,
        updated_at: *************,
        verification: {
          status: "verified",
          strategy: "oauth_google",
          attempts: null,
          expire_at: *************,
          error: {
            code: "external_account_missing_refresh_token",
            message: "Missing refresh token",
            long_message:
              "We cannot refresh your OAuth access token because the server didn't provide a refresh token. Please re-connect your account.",
          },
        },
        external_account_id: "eac_2tBaHdKg5VIbgnxhO3yAnSDAPEo",
        google_id: "114710527711368025536",
        given_name: "Dmitry",
        family_name: "Tokmakov",
        picture:
          "https://lh3.googleusercontent.com/a/ACg8ocJB_TU-mfpGH5VUFVAkIZiozOrBXjNZKfho1IyPR9n_smp4mg=s1000-c",
      },
    ],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {
      demoMode: "true",
    },
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: *************,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: false,
    last_active_at: *************,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url: "https://www.gravatar.com/avatar?d=mp",
  },
  {
    id: "user_2sfowjPbIrwsdqlk5ZlwU74a84k",
    object: "user",
    username: null,
    first_name: null,
    last_name: null,
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18yc1F2bWpvemV2NFJhQ1pTWHF4TWcyQUNaVVkiLCJyaWQiOiJ1c2VyXzJzZm93alBiSXJ3c2RxbGs1Wmx3VTc0YTg0ayJ9",
    has_image: false,
    primary_email_address_id: "idn_2sfowj2SNMMufdcUV2TOUOQlUPL",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: true,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2sfowj2SNMMufdcUV2TOUOQlUPL",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "ticket",
          attempts: null,
          expire_at: null,
        },
        linked_to: [],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {},
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: *************,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: false,
    last_active_at: *************,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url: "https://www.gravatar.com/avatar?d=mp",
  },
  {
    id: "user_2sXhyaOYR8dhLUTxCOXi2gBSxIL",
    object: "user",
    username: null,
    first_name: null,
    last_name: null,
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18yc1F2bWpvemV2NFJhQ1pTWHF4TWcyQUNaVVkiLCJyaWQiOiJ1c2VyXzJzWGh5YU9ZUjhkaExVVHhDT1hpMmdCU3hJTCJ9",
    has_image: false,
    primary_email_address_id: "idn_2sXhyblV3XOCgz1wC7071TnZvp4",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: true,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2sXhyblV3XOCgz1wC7071TnZvp4",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "ticket",
          attempts: null,
          expire_at: null,
        },
        linked_to: [],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {},
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: *************,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: false,
    last_active_at: *************,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url: "https://www.gravatar.com/avatar?d=mp",
  },
  {
    id: "user_2sXNSwZNmGr4hckRNdjKH0fcHxn",
    object: "user",
    username: null,
    first_name: "Crawford",
    last_name: "Hawkins",
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoiZGVmYXVsdCIsImlpZCI6Imluc18yc1F2bWpvemV2NFJhQ1pTWHF4TWcyQUNaVVkiLCJyaWQiOiJ1c2VyXzJzWE5Td1pObUdyNGhja1JOZGpLSDBmY0h4biIsImluaXRpYWxzIjoiQ0gifQ",
    has_image: false,
    primary_email_address_id: "idn_2sXNSvRrZklx6ypasJGBGyX7mpD",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: false,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2sXNSvRrZklx6ypasJGBGyX7mpD",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "ticket",
          attempts: null,
          expire_at: null,
        },
        linked_to: [
          {
            type: "oauth_google",
            id: "idn_2sXgKFAMIwECET9zHXdTWDJqQw5",
          },
        ],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [
      {
        object: "google_account",
        id: "idn_2sXgKFAMIwECET9zHXdTWDJqQw5",
        provider: "oauth_google",
        identification_id: "idn_2sXgKFAMIwECET9zHXdTWDJqQw5",
        provider_user_id: "106910136913447488180",
        approved_scopes:
          "email https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile openid profile",
        email_address: "<EMAIL>",
        first_name: "Crawford",
        last_name: "Hawkins",
        avatar_url:
          "https://lh3.googleusercontent.com/a/ACg8ocK7Pzq53fxnjrCH4MydDwv5z1bFw0IvrJ2ixwz23a4xL5EHSg=s1000-c",
        image_url:
          "https://img.clerk.com/eyJ0eXBlIjoicHJveHkiLCJzcmMiOiJodHRwczovL2xoMy5nb29nbGV1c2VyY29udGVudC5jb20vYS9BQ2c4b2NLN1B6cTUzZnhuanJDSDRNeWREd3Y1ejFiRncwSXZySjJpeHd6MjNhNHhMNUVIU2c9czEwMDAtYyIsInMiOiJwSGoydWlpMWJ4eU11aUdzMHh2UnhzVk9LNjBkUVN2V2ZEQTgxNHREYTJrIn0",
        username: "",
        public_metadata: {},
        label: null,
        created_at: *************,
        updated_at: *************,
        verification: {
          status: "verified",
          strategy: "oauth_google",
          attempts: null,
          expire_at: *************,
          error: {
            code: "external_account_missing_refresh_token",
            message: "Missing refresh token",
            long_message:
              "We cannot refresh your OAuth access token because the server didn't provide a refresh token. Please re-connect your account.",
          },
        },
        external_account_id: "eac_2sXgKH1ZmKfPmtZ4pltxhCedLCe",
        google_id: "106910136913447488180",
        given_name: "Crawford",
        family_name: "Hawkins",
        picture:
          "https://lh3.googleusercontent.com/a/ACg8ocK7Pzq53fxnjrCH4MydDwv5z1bFw0IvrJ2ixwz23a4xL5EHSg=s1000-c",
      },
    ],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {
      demoMode: "true",
    },
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: *************,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: false,
    last_active_at: *************,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url: "https://www.gravatar.com/avatar?d=mp",
  },
  {
    id: "user_2sUjTTykSMjgnKVlku2WgArrcqF",
    object: "user",
    username: null,
    first_name: "Gilad",
    last_name: "Rom",
    image_url:
      "https://img.clerk.com/eyJ0eXBlIjoicHJveHkiLCJzcmMiOiJodHRwczovL2ltYWdlcy5jbGVyay5kZXYvb2F1dGhfZ29vZ2xlL2ltZ18yc1VqVE84T2htMFFkZU8xYkZjNEZISUYzVHEifQ",
    has_image: true,
    primary_email_address_id: "idn_2sUjT9KdwvwO708m7X2oPqQwg4P",
    primary_phone_number_id: null,
    primary_web3_wallet_id: null,
    password_enabled: true,
    two_factor_enabled: false,
    totp_enabled: false,
    backup_code_enabled: false,
    email_addresses: [
      {
        id: "idn_2sUjT9KdwvwO708m7X2oPqQwg4P",
        object: "email_address",
        email_address: "<EMAIL>",
        reserved: false,
        verification: {
          status: "verified",
          strategy: "from_oauth_google",
          attempts: null,
          expire_at: null,
        },
        linked_to: [
          {
            type: "oauth_google",
            id: "idn_2sUjTClvWzXo5Uagq9pljkL0LX5",
          },
        ],
        matches_sso_connection: false,
        created_at: *************,
        updated_at: *************,
      },
    ],
    phone_numbers: [],
    web3_wallets: [],
    passkeys: [],
    external_accounts: [
      {
        object: "google_account",
        id: "idn_2sUjTClvWzXo5Uagq9pljkL0LX5",
        provider: "oauth_google",
        identification_id: "idn_2sUjTClvWzXo5Uagq9pljkL0LX5",
        provider_user_id: "104037372603857164519",
        approved_scopes:
          "email https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile openid profile",
        email_address: "<EMAIL>",
        first_name: "Gilad",
        last_name: "Rom",
        avatar_url:
          "https://lh3.googleusercontent.com/a/ACg8ocIe-3yVKrjaTJrR0Z0GqW3mTx7OLGTbHwnPy7zywRl5lScjGg=s1000-c",
        image_url:
          "https://img.clerk.com/eyJ0eXBlIjoicHJveHkiLCJzcmMiOiJodHRwczovL2xoMy5nb29nbGV1c2VyY29udGVudC5jb20vYS9BQ2c4b2NJZS0zeVZLcmphVEpyUjBaMEdxVzNtVHg3T0xHVGJId25QeTd6eXdSbDVsU2NqR2c9czEwMDAtYyIsInMiOiI3bkhGTTRLNm1uMzRIN1gzRzZiWk5LMkkvOWwzKzVjZjZEdmZUQ2JMWkhZIn0",
        username: "",
        public_metadata: {},
        label: null,
        created_at: *************,
        updated_at: *************,
        verification: {
          status: "verified",
          strategy: "oauth_google",
          attempts: null,
          expire_at: *************,
        },
        external_account_id: "eac_2sUjTCSLYFtWfrwp4aUGItQuc6r",
        google_id: "104037372603857164519",
        given_name: "Gilad",
        family_name: "Rom",
        picture:
          "https://lh3.googleusercontent.com/a/ACg8ocIe-3yVKrjaTJrR0Z0GqW3mTx7OLGTbHwnPy7zywRl5lScjGg=s1000-c",
      },
    ],
    saml_accounts: [],
    enterprise_accounts: [],
    public_metadata: {
      demoMode: "true",
    },
    private_metadata: {},
    unsafe_metadata: {},
    external_id: null,
    last_sign_in_at: *************,
    banned: false,
    locked: false,
    lockout_expires_in_seconds: null,
    verification_attempts_remaining: 5,
    created_at: *************,
    updated_at: *************,
    delete_self_enabled: false,
    create_organization_enabled: true,
    last_active_at: *************,
    mfa_enabled_at: null,
    mfa_disabled_at: null,
    legal_accepted_at: null,
    profile_image_url:
      "https://images.clerk.dev/oauth_google/img_2sUjTO8Ohm0QdeO1bFc4FHIF3Tq",
  },
];

// List of users to seed the database with. These should match the names and ids of Users in Clerk.
const userList: User[] = [
  ...usersClerk.map((user) => ({
    id: faker.string.uuid(),
    clerkId: user.id,
    name: user.first_name + " " + user.last_name,
    email: user.email_addresses[0]?.email_address ?? "",
    title: UserTitle.Other,
    image: user.image_url,
    role: UserRole.SuperAdmin,
    bio: "",
    emailVerified: new Date(),
  })),
  {
    id: faker.string.uuid(),
    clerkId: "",
    name: "Virgil AI",
    email: "<EMAIL>",
    title: UserTitle.Other,
    image: "https://api.dicebear.com/9.x/initials/svg?seed=VirgilAI",
    role: UserRole.SuperAdmin,
    bio: "Chatbot for Virgil AI",
    emailVerified: new Date(),
  },
];

// List of demo organizations to seed the database with. These should match the names and ids of Orgs in Clerk.
const org: Org = {
  id: faker.string.uuid(),
  name: "25 Madison",
  clerkId: "org_2wX8PGNBF5BhCZESMJA79RW6nkK",
  type: OrgType.Lender,
  corpType: CorpType.CCorp,
  createdById: "1",
  createdAt: new Date(),
  updatedAt: new Date(),
  incorporatedAt: faker.date.between({
    from: new Date("2020-01-01"),
    to: new Date("2022-01-01"),
  }),
  ein: faker.string.alphanumeric(10),
  bio: faker.lorem.paragraph(),
  addressId: "1",
  webhookSecret: faker.string.alphanumeric(10),
  egnyteAccessTokenId: null,
  azureAccessTokenId: null,
};

const seedUsers = async (users: User[]) => {
  for (const user of users) {
    await prisma.user.upsert({
      where: { clerkId: user.clerkId ?? "" },
      update: {
        ...user,
      },
      create: {
        ...user,
      },
    });
  }

  const createdUsers = await prisma.user.findMany();

  return createdUsers;
};

const seedAddresses = async () => {
  const addresses = Array.from({ length: 10 }, (_, i) => ({
    id: faker.string.uuid(),
    street: faker.location.streetAddress(),
    city: faker.location.city(),
    state: faker.location.state(),
    zip: faker.location.zipCode(),
  }));

  for (const address of addresses) {
    await prisma.address.upsert({
      where: { id: address.id },
      update: {
        ...address,
      },
      create: {
        ...address,
      },
    });
  }

  const createdAddresses = await prisma.address.findMany();

  return createdAddresses;
};

async function main() {
  // Reset DB

  console.log("Seeding users");
  const users = await seedUsers(userList);
  console.log("Seeding addresses");
  const existingAddresses = await prisma.address.findMany();
  const addresses =
    existingAddresses.length === 0 ? await seedAddresses() : existingAddresses;

  console.log("Seeding organizations");
  const existingOrg =
    (await prisma.org.findFirst({
      where: { name: org.name ?? "" },
    })) ??
    (await prisma.org.create({
      data: {
        ...org,
        addressId: faker.helpers.arrayElement(addresses).id,
      },
    }));

  // Connect users to organizations
  // add all users to the virgil org
  for (const user of users) {
    await prisma.userOrg.upsert({
      where: {
        userId_orgId: {
          userId: user.id,
          orgId: existingOrg.id,
        },
      },
      update: {
        userId: user.id,
        orgId: existingOrg.id,
      },
      create: {
        userId: user.id,
        orgId: existingOrg.id,
      },
    });
  }

  const chatBotId = users.find(
    (user) => user.email === "<EMAIL>",
  )?.id;

  const existingConversations = await prisma.conversation.findMany();
  if (existingConversations.length === 0) {
    // Seed conversations
    for (const user of users) {
      await prisma.conversation.create({
        data: {
          createdAt: new Date(),
          updatedAt: new Date(),

          id: faker.string.uuid(),
          orgId: existingOrg.id,
          createdById: user.id,
          messages: {
            create: {
              body: "Hello, how can I help you today?",
              createdById: chatBotId ?? "",
              orgId: existingOrg.id,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          },
          participants: {
            create: [
              {
                userId: chatBotId ?? "",
                status: ParticipantStatus.ONLINE,
              },
              {
                userId: user.id,
                status: ParticipantStatus.ONLINE,
              },
            ],
          },
        },
      });
    }
  }
}

main()
  .then(() => {
    console.log("Seeded database");
  })
  .catch((error) => {
    console.error(error);
  });
