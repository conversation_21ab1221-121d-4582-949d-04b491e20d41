import { faker } from "@faker-js/faker";
import { ParticipantStatus, type User } from "@prisma/client";
import { db as prisma } from "~/server/db";
import { funds, seedFunds } from "./funds";
import { orgList } from "./orgs";
import { tags, type TagWithChildren } from "./tags";
import { userList } from "./users";

const resetDb = false;

const seedUsers = async (users: User[]) => {
  for (const user of users) {
    await prisma.user.upsert({
      where: { email: user.email ?? "" },
      update: {
        ...user,
      },
      create: {
        ...user,
      },
    });
  }

  const createdUsers = await prisma.user.findMany();

  return createdUsers;
};

const seedTags = async (tags: TagWithChildren[], orgId: string) => {
  for (const tag of tags) {
    const { children, ...tagWithoutChildren } = tag;

    await prisma.tag.upsert({
      where: { name: tag.name ?? "" },
      update: {
        ...tagWithoutChildren,
      },
      create: {
        ...tagWithoutChildren,
        orgId,
      },
    });
  }

  const createdTags = await prisma.tag.findMany({
    where: {
      orgId,
    },
    include: {
      children: true,
    },
  });

  for (const tag of createdTags) {
    console.log("Processing tag", tag.name);
    for (const children of tags
      .filter((t) => t.name === tag.name)
      .map((t) => t.children)) {
      console.log("Processing children", children);
      for (const child of children) {
        console.log("Processing child", child.name);
        await prisma.tag.upsert({
          where: {
            name: child.name,
          },
          update: {
            parent: {
              connect: {
                id: tag.id,
              },
            },
          },
          create: {
            ...child,
            orgId,
            parentId: tag.id,
          },
        });
      }
    }
  }

  const updatedTags = await prisma.tag.findMany({
    where: {
      orgId,
    },
    include: {
      children: true,
    },
  });

  console.log(
    "Tags",
    updatedTags
      .filter((t) => t.children.length > 0)
      .map((t) => {
        return { name: t.name, children: t.children.map((c) => c.name) };
      }),
  );

  return updatedTags;
};

const seedAddresses = async () => {
  const addresses = Array.from({ length: 10 }, (_, i) => ({
    id: faker.string.uuid(),
    street: faker.location.streetAddress(),
    city: faker.location.city(),
    state: faker.location.state(),
    zip: faker.location.zipCode(),
  }));

  for (const address of addresses) {
    await prisma.address.upsert({
      where: { id: address.id },
      update: {
        ...address,
      },
      create: {
        ...address,
      },
    });
  }

  const createdAddresses = await prisma.address.findMany();

  return createdAddresses;
};

async function main() {
  // Reset DB
  if (resetDb) {
    console.log("Resetting database");
    await prisma.questionContent.deleteMany();
    await prisma.question.deleteMany();
    await prisma.responseContent.deleteMany();
    await prisma.response.deleteMany();
    await prisma.conversation.deleteMany();
    await prisma.chatMessage.deleteMany();
    await prisma.chatParticipant.deleteMany();
    await prisma.documentChunk.deleteMany();
    await prisma.documentEdit.deleteMany();
    await prisma.document.deleteMany();
    await prisma.azureDrive.deleteMany();
    await prisma.azureSubscription.deleteMany();
    await prisma.azureAccessToken.deleteMany();
    await prisma.userOrg.deleteMany();
    await prisma.org.deleteMany();
    await prisma.address.deleteMany();
    await prisma.user.deleteMany();
  }

  console.log("Seeding users");
  const users = await seedUsers(userList);
  console.log("Seeding addresses");
  const existingAddresses = await prisma.address.findMany();
  const addresses =
    existingAddresses.length === 0 ? await seedAddresses() : existingAddresses;

  console.log("Seeding organizations");

  const org = orgList[0]!;

  const existingOrg = await prisma.org.findFirst({
    where: { name: org.name },
  });

  if (existingOrg) {
    await prisma.org.update({
      where: { id: existingOrg.id },
      data: {
        ...org,
        id: existingOrg.id,
        addressId: faker.helpers.arrayElement(addresses).id,
      },
    });
  } else {
    await prisma.org.create({
      data: {
        ...org,
        addressId: faker.helpers.arrayElement(addresses).id,
      },
    });
  }

  const orgs = await prisma.org.findMany();

  const orgId = existingOrg?.id ?? orgs[0]?.id;

  console.log("Seeding tags");
  const createdTags = await seedTags(tags, orgId ?? "");

  console.log("Seeding funds");
  const createdFunds = await seedFunds(funds, orgId ?? "");

  // Connect users to organizations
  // add all users to the virgil org
  for (const user of users) {
    console.log("connecting user", user.id, orgId);
    await prisma.userOrg.upsert({
      where: {
        userId_orgId: {
          userId: user.id,
          orgId: orgId ?? "",
        },
      },
      update: {
        userId: user.id,
        orgId: orgId ?? "",
      },
      create: {
        userId: user.id,
        orgId: orgId ?? "",
      },
    });
  }

  const chatBotId = users.find(
    (user) => user.email === "<EMAIL>",
  )?.id;

  // Seed conversations
  for (const org of orgs) {
    for (const user of users) {
      const existingConversation = await prisma.conversation.count({
        where: {
          participants: {
            some: {
              userId: user.id,
            },
          },
        },
      });

      if (existingConversation === 0) {
        await prisma.conversation.create({
          data: {
            createdAt: new Date(),
            updatedAt: new Date(),

            id: faker.string.uuid(),
            orgId: orgId ?? "",
            createdById: user.id,
            messages: {
              create: {
                body: "Hello, how can I help you today?",
                createdById: chatBotId ?? "",
                orgId: orgId ?? "",
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            },
            participants: {
              create: [
                {
                  userId: chatBotId ?? "",
                  status: ParticipantStatus.ONLINE,
                },
                {
                  userId: user.id,
                  status: ParticipantStatus.ONLINE,
                },
              ],
            },
          },
        });
      }
    }
  }
}

main()
  .then(() => {
    console.log("Seeded database");
  })
  .catch((error) => {
    console.error(error);
  });
