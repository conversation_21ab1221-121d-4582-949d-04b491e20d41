import { type Prisma } from "@prisma/client";

export function generatePastelColor() {
  // Generate each color component in the range [127, 255] for a pastel tone
  const r = Math.floor(Math.random() * 128) + 127;
  const g = Math.floor(Math.random() * 128) + 127;
  const b = Math.floor(Math.random() * 128) + 127;

  // Convert each component to a two-digit hexadecimal string and join them
  return "#" + [r, g, b].map((x) => x.toString(16).padStart(2, "0")).join("");
}

export type TagWithChildren = Prisma.TagGetPayload<{
  select: {
    name: true;
    color: true;
    summary: true;
    children: {
      select: {
        name: true;
        color: true;
        summary: true;
      };
    };
  };
}>;

export const tags: TagWithChildren[] = [
  {
    name: "Risk Management",
    color: "#e67e22",
    summary:
      "Focuses on identifying, assessing, and mitigating risks inherent in financial operations, investments, and data handling processes.",
    children: [
      {
        name: "Credit Risk",
        color: generatePastelColor(),
        summary:
          "Assessment of risks related to borrowers' ability to repay loans and the potential for defaults.",
      },
      {
        name: "Market Risk",
        color: generatePastelColor(),

        summary:
          "Evaluation of risks arising from market fluctuations, such as changes in interest rates, currency values, and asset prices.",
      },
      {
        name: "Operational Risk",
        color: generatePastelColor(),

        summary:
          "Focuses on risks due to internal failures, including breakdowns in processes, systems, or human error.",
      },
    ],
  },
  {
    name: "Regulatory Compliance",
    color: "#3498db",
    summary:
      "Ensures adherence to laws, regulations, and internal policies during financial data management and reporting.",
    children: [
      {
        name: "Audit Trail",
        color: generatePastelColor(),

        summary:
          "Maintains detailed records and traceability of financial transactions for accountability and review.",
      },
      {
        name: "Anti-Money Laundering (AML)",
        color: generatePastelColor(),

        summary:
          "Implements measures to detect, prevent, and report activities related to money laundering.",
      },
      {
        name: "Reporting Standards",
        color: generatePastelColor(),

        summary:
          "Adopts standardized practices and guidelines for consistent and transparent financial reporting.",
      },
    ],
  },
  {
    name: "Portfolio Management",
    color: "#9b59b6",
    summary:
      "Covers the strategic process of managing a collection of investments to optimize returns while controlling risks.",
    children: [
      {
        name: "Asset Allocation",
        color: generatePastelColor(),

        summary:
          "Determines the distribution of investments across various asset classes to achieve balanced risk and return.",
      },
      {
        name: "Diversification Strategy",
        color: generatePastelColor(),

        summary:
          "Employs methods to spread risk across multiple investments to minimize exposure to any single asset.",
      },
      {
        name: "Rebalancing Techniques",
        color: generatePastelColor(),

        summary:
          "Involves periodic adjustment of portfolio holdings to maintain target asset distribution and risk levels.",
      },
    ],
  },
  {
    name: "Financial Performance",
    color: "#2ecc71",
    summary:
      "Focuses on evaluating and measuring the overall financial outcomes and efficiency of business operations.",
    children: [
      {
        name: "Profitability Analysis",
        color: generatePastelColor(),

        summary:
          "Assesses income generation, cost management, and overall profit margins across business operations.",
      },
      {
        name: "Efficiency Metrics",
        color: generatePastelColor(),

        summary:
          "Tracks key performance indicators that gauge how effectively financial resources are utilized.",
      },
      {
        name: "Liquidity Measurement",
        color: generatePastelColor(),

        summary:
          "Evaluates the ability of an entity to meet short-term obligations through effective cash flow management.",
      },
    ],
  },
  {
    name: "Capital Planning",
    color: "#f1c40f",
    summary:
      "Involves strategizing and allocating financial resources to support growth initiatives, investments, and operational needs.",
    children: [
      {
        name: "Budgeting",
        color: generatePastelColor(),

        summary:
          "Plans and allocates funds for future operations and projects to ensure financial discipline.",
      },
      {
        name: "Forecasting",
        color: generatePastelColor(),

        summary:
          "Predicts future financial trends and performance based on historical data and market conditions.",
      },
      {
        name: "Resource Allocation",
        color: generatePastelColor(),

        summary:
          "Determines the optimal distribution of financial capital across various business activities and investment opportunities.",
      },
    ],
  },
  {
    name: "General Fund Information",
    color: "#95a5a6",
    summary: "Use this tag for questions related to general fund information.",
    children: [],
  },
  {
    name: "Human Resources",
    color: "#95a5a6",
    summary:
      "Use this tag for questions related to human resources and general staff inquiries.",
    children: [],
  },
  {
    name: "Investment Process",
    color: "#95a5a6",
    summary: "Use this tag for questions related to the investment process.",
    children: [],
  },
  {
    name: "Fees and Charges",
    color: "#95a5a6",
    summary: "Use this tag for questions related to fees and charges.",
    children: [],
  },
  {
    name: "Legal",
    color: "#95a5a6",
    summary: "Use this tag for questions related to legal matters.",
    children: [],
  },
  {
    name: "ESG",
    color: "#95a5a6",
    summary:
      "Use this tag for questions related to ESG (Environmental, social, and governance) matters.",
    children: [
      {
        name: "Environmental",
        color: generatePastelColor(),
        summary: "Use this tag for questions related to environmental matters.",
      },
      {
        name: "Social",
        color: generatePastelColor(),
        summary: "Use this tag for questions related to social matters.",
      },
      {
        name: "Governance",
        color: generatePastelColor(),
        summary: "Use this tag for questions related to governance matters.",
      },
    ],
  },
  {
    name: "Other",
    color: "#95a5a6",
    summary:
      "Use this tag for questions that don't fit into any other category.",
    children: [],
  },
];
