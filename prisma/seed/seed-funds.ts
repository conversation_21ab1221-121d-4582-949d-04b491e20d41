import { db as prisma } from "~/server/db";
import { seedFunds } from "./funds";
import { funds as ohaFunds } from "./oha-funds";

// Set this to the org name in Neon DB
const ORG_NAME = "Set Org Name";

const enabled = false;

async function main() {
  if (enabled) {
    const org = await prisma.org.findFirstOrThrow({
      where: {
        name: ORG_NAME,
      },
    });
    if (!org) {
      throw new Error("Org not found");
    }

    console.log("Seeding funds");
    const funds = await seedFunds(ohaFunds, org.id);
    console.log("Funds seeded", funds);
  } else {
    console.log("Disabled");
  }
}

main()
  .then(() => {
    console.log("Done");
  })
  .catch((error) => {
    console.error(error);
  });
