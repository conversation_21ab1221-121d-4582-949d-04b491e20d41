#!/bin/sh

set -xe

# ECR_HOST=183295412412.dkr.ecr.us-east-1.amazonaws.com
# ECR_PATH=pyrpc-api/python-api

ECR_HOST=$1
ECR_PATH=$2

GIT_HASH=$(git log --pretty=format:'%h' -n 1)

aws ecr get-login-password --region us-east-1 |
    docker login --username AWS --password-stdin $ECR_HOST

docker buildx build --platform=linux/amd64 -f infra/containers/pyrpc/Dockerfile -t $ECR_HOST/$ECR_PATH:latest infra/containers/pyrpc
docker tag $ECR_HOST/$ECR_PATH:latest $ECR_HOST/$ECR_PATH:$GIT_HASH
docker push $ECR_HOST/$ECR_PATH:latest
