from pathlib import Path
import json
import os
import sys
from enum import Enum
import inspect
from pydantic import BaseModel
from typing import get_type_hints, Optional, Union, get_args

# Get the absolute path to the project root
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Add the api directory to Python path so we can import the models module
sys.path.append(project_root)

class Compiler:
    def _process_schema(self, schema: dict, model_type: type) -> dict:
        # Get type hints for the model
        type_hints = get_type_hints(model_type)
        
        # Process properties if they exist
        if "properties" in schema:
            for field_name, field_schema in schema["properties"].items():
                if field_name in type_hints:
                    field_type = type_hints[field_name]
                    # Check if field is Optional
                    if get_args(field_type) and Optional[get_args(field_type)[0]] == field_type:
                        schema["properties"][field_name]["optional"] = True
                        # Just remove from required list for optional fields
                        if "required" in schema and field_name in schema["required"]:
                            schema["required"].remove(field_name)
                        
                        # Clean up any anyOf structure if exists
                        if "anyOf" in field_schema:
                            # Get the main type definition (usually the first non-null item)
                            main_type = next((item for item in field_schema["anyOf"] 
                                            if item.get("type") != "null"), field_schema["anyOf"][0])
                            # Replace the field schema with the cleaned version
                            schema["properties"][field_name] = main_type
        return schema

    def parse(self, module_path: str) -> dict:
        # Import the module dynamically
        try:
            module_parts = module_path.split('.')
            models_module = __import__(module_path, fromlist=module_parts[-1:])
        except ImportError as e:
            print(f"Error: Could not import module {module_path}", file=sys.stderr)
            sys.exit(1)

        # Get all models from the module automatically
        models = []
        for name, obj in inspect.getmembers(models_module):
            # Check if it's a class and either inherits from BaseModel or is an Enum
            if inspect.isclass(obj) and (issubclass(obj, BaseModel) or issubclass(obj, Enum)):
                # Avoid including BaseModel itself
                if obj != BaseModel:
                    models.append(obj)
        
        # Generate JSON schema for all models
        schemas = {}
        for model in models:
            if hasattr(model, 'model_json_schema'):
                # For Pydantic v2 models
                schema = model.model_json_schema()
                # Process Optional fields
                if issubclass(model, BaseModel):
                    schema = self._process_schema(schema, model)
            elif issubclass(model, Enum):
                # Handle Enum types specially
                schema = {
                    "title": model.__name__,
                    "type": "string",
                    "enum": [e.value for e in model]
                }
            schemas[model.__name__] = schema
            
        return schemas

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 convert-pydantic-2-json.py <module_path>", file=sys.stderr)
        print("Example: python3 convert-pydantic-2-json.py api.pyrpc.models", file=sys.stderr)
        sys.exit(1)

    module_path = sys.argv[1]
    json_schemas = Compiler().parse(module_path)
    print(json.dumps(json_schemas, indent=2))
