import fs from "fs";
import { jsonSchemaToZod } from "json-schema-to-zod";
import * as prettier from "prettier";
import * as jsonRefs from "json-refs";

const outputFile = process.argv[2] || "./src/lib/types-from-pydantic.zod.ts";

let input = "";
process.stdin.on("data", (chunk) => {
  input += chunk;
});

process.stdin.on("end", async () => {
  const schemas = JSON.parse(input);
  const header =
    '// Generated Zod schemas from Pydantic with JSON Schema exporter\nimport { z } from "zod";\n\n';
  
  // Build dependency graph
  const dependencyGraph = new Map<string, Set<string>>();
  const schemaNames = Object.keys(schemas);
  
  // Initialize graph
  schemaNames.forEach(name => {
    dependencyGraph.set(name, new Set());
  });

  // Populate dependencies
  schemaNames.forEach(schemaName => {
    const schema = schemas[schemaName];
    const findDependencies = (obj: any) => {
      if (!obj) return;
      if (typeof obj === 'object') {
        if (obj.title && schemas[obj.title]) {
          dependencyGraph.get(schemaName)?.add(obj.title);
        }
        Object.values(obj).forEach(findDependencies);
      }
    };
    findDependencies(schema);
  });

  // Topological sort
  const visited = new Set<string>();
  const sorted: string[] = [];
  
  function visit(name: string, path = new Set<string>()) {
    if (path.has(name)) {
      // Handle circular dependencies
      return;
    }
    if (visited.has(name)) {
      return;
    }
    path.add(name);
    dependencyGraph.get(name)?.forEach(dep => {
      visit(dep, path);
    });
    path.delete(name);
    visited.add(name);
    sorted.push(name);
  }

  schemaNames.forEach(name => {
    visit(name);
  });

  // Generate schemas in dependency order
  let mergedSchemas = "";
  for (const schemaName of sorted) {
    const result = await jsonRefs.resolveRefs(schemas[schemaName]);
    const schemaObj = result.resolved;

    const zodSchema = jsonSchemaToZod(schemaObj, {
      module: "esm",
      type: true,
      name: schemaName,
      noImport: true,
      parserOverride: (schema, refs) => {
        if(schemas[schema.title] && refs.path.length) {
          return schema.title;
        }
      },
    });

    const schemaWithoutImport = zodSchema.trim();
    mergedSchemas += schemaWithoutImport + "\n\n";
  }

  // Combine header with cleaned schemas
  const mergedOutput = header + mergedSchemas;

  // Format the output using prettier
  prettier
    .format(mergedOutput, {
      parser: "typescript",
      semi: true,
      singleQuote: false,
      trailingComma: "es5",
      printWidth: 100,
      tabWidth: 2,
    })
    .then((formattedOutput) => {
      // Write to a file
      fs.writeFileSync(outputFile, formattedOutput);
    })
    .catch((err) => {
      console.error(err);
    });
});
