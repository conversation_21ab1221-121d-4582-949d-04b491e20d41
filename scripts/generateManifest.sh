#!/bin/bash

# Get command line arguments
env=$1
plugin=$2

# Validate arguments
if [ -z "$env" ] || [ -z "$plugin" ]; then
  echo "Usage: $0 <env> <plugin>"
  exit 1
fi

# Check if environment config exists
if [ ! -f "./ms-office-addin/manifests/env/${env}.json" ]; then
  echo "Error: Environment config file not found at ./ms-office-addin/manifests/env/${env}.json"
  echo "Available environments:"
  ls -1 ./ms-office-addin/manifests/env/*.json | sed 's/.*\/\([^/]*\)\.json$/\1/'
  exit 1
fi

if [ "$plugin" != "excel" ] && [ "$plugin" != "word" ]; then
  echo "Error: plugin must be either 'excel' or 'word'"
  exit 1
fi

# Generate manifest
node scripts/generateManifest.js $env $plugin

# Validate manifest
office-addin-manifest validate ./ms-office-addin/manifests/dist/manifest.$plugin.$env.xml
