#!/bin/sh -x

set -e
ROOT_DIR=$(pwd)
# AWS_PROFILE=bottleneck_infra_dev
LAMBDA_ENV=$1

for lambda in infra/lambda/*; do
    LAMBDA_NAME=$(basename $lambda)
    echo "Deploying $LAMBDA_NAME"
    cd $lambda
    if [ -e "index.ts" ]; then
        rm -rf dist
        npx esbuild index.ts --bundle --minify --sourcemap --platform=node --target=es2020 --outfile=dist/index.js --loader:.node=file
        cd dist && cp ../../../../prisma/schema.prisma . && cp ../../../../node_modules/.prisma/client/libquery_engine-linux-arm64-openssl-3.0.x.so.node . && zip -r index.zip index.js* schema.prisma libquery_engine*
        aws lambda update-function-code --function-name $LAMBDA_NAME-$LAMBDA_ENV --zip-file fileb://index.zip --output json 2>&1 >lambda-deploy.log
        cd $ROOT_DIR
        rm -rf $lambda/dist
    elif [ -e "lambda_function.py" ]; then
        rm -rf dist
        # pip install -r requirements.txt -t ./dist
        cd dist && zip -r ../function.zip . && cd ..
        zip function.zip lambda_function.py
        aws lambda update-function-code --function-name $LAMBDA_NAME-$LAMBDA_ENV --zip-file fileb://function.zip --output json 2>&1 >lambda-deploy.log
        rm function.zip
        rm -rf dist
        cd $ROOT_DIR
    fi
done
