#bin/bash
# should be executed from the root of the project


if [ -d ~/Library/Containers/com.microsoft.Excel/Data/Documents/wef ]; then
    echo "Wef directory already exists. Skipping creation"
else
    mkdir -p ~/Library/Containers/com.microsoft.Excel/Data/Documents/wef
    echo "Wef directory created"
fi

# Get environment parameter with default value of 'dev'
env=${1:-dev}
plugin_type=${2}

# Validate environment parameter
if [ ! -f "./ms-office-addin/manifests/env/${env}.json" ]; then
    echo "Invalid environment. Must be 'dev' or 'prod' or 'local'"
    exit 1
fi

# Validate plugin type parameter
if [ "$plugin_type" != "excel" ] && [ "$plugin_type" != "word" ]; then
    echo "Invalid plugin type. Must be 'excel' or 'word'"
    exit 1
fi

# Generate manifest
if node scripts/generateManifest.js $env $plugin_type; then
    echo "Manifest generated successfully"
else
    echo "Failed to generate manifest"
    exit 1
fi

# Validate manifest
# if office-addin-manifest validate ./ms-office-addin/manifests/dist/manifest.${plugin_type}.${env}.xml; then
#     echo "Manifest validated successfully"
# else
#     echo "Failed to validate manifest"
#     exit 1
# fi

# Set manifest source based on environment
manifest_source="./ms-office-addin/manifests/dist/manifest.${plugin_type}.${env}.xml"

# Check if manifest exists
if [ ! -f "$manifest_source" ]; then
    echo "Manifest file not found at ${manifest_source}"
    exit 1
fi

manifest_destination=""

if [ "$plugin_type" == "excel" ]; then
    manifest_destination="$HOME/Library/Containers/com.microsoft.Excel/Data/Documents/wef/manifest.xml"
fi

if [ "$plugin_type" == "word" ]; then
    manifest_destination="$HOME/Library/Containers/com.microsoft.Word/Data/Documents/wef/manifest.xml"
fi

rm -rf "$manifest_destination"

echo "Removing existing manifest at ${manifest_destination}"

if cp "$manifest_source" "$manifest_destination"; then
    echo "Success: Sideloaded manifest: from ${manifest_source} to ${manifest_destination}"
    echo "You can click on ${manifest_destination} to open the manifest in the IDE and edit directly"
else
    echo "Error: Failed to copy manifest" # Permissions issues or something else
    exit 1
fi

