import type { Preview } from "@storybook/react";
import "./storybook.css";
import { ThemeProvider } from "~/theme/theme-provider";
import { defaultSettings, SettingsProvider } from "src/components/settings";

const preview: Preview = {
  decorators: [
    (Story) => (
      <div id="storybook-root">
        <SettingsProvider
          settings={defaultSettings}
          caches="cookie"
        >
          <ThemeProvider>
            <Story />
          </ThemeProvider>
        </SettingsProvider>
      </div>
    ),
  ],
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/',
      }
    }
  },
};

// TODO: fix jsx in storybook
// export const decorators = [
//   (Story) => (
//     <MainLayout>
//       <Story />
//     </MainLayout>
//   )
// ]

export default preview;
