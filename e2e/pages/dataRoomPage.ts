import type { Locator, <PERSON> } from "@playwright/test";
import path from "path";

export class DataRoomPage {
  constructor(private page: Page) { }

  manageIntegrationsButton: Locator = this.page.getByRole("button", {
    name: "Manage Integrations",
  });
  uploadFileButton: Locator = this.page.getByRole("button", {
    name: "Upload",
  });
  filtersButton: Locator = this.page.getByRole("button", {
    name: "Filters",
  });
  processButtonDisabled: Locator = this.page.getByRole("button", {
    name: "Process",
    disabled: true,
  });
  listViewToggle: Locator = this.page.locator('button[value="list"]');
  gridViewToggle: Locator = this.page.locator('button[value="grid"]');

  allTab: Locator = this.page.getByRole("tab", { name: "All" });
  localTab: Locator = this.page.getByRole("tab", { name: "Local" });
  egnyteTab: Locator = this.page.getByRole("tab", { name: "Egnyte" });
  sharepointTab: Locator = this.page.getByRole("tab", { name: "Sharepoint" });
  intralinksTab: Locator = this.page.getByRole("tab", { name: "Intralinks" });
  searchInput: Locator = this.page.locator('input[placeholder="Search..."]');
  fileInput: Locator = this.page.locator('input[type="file"]');
  uploadedFileName: Locator = this.page.locator(".uploaded-file-name");
  uploadButton: Locator = this.page.locator(".upload-button");
  statusProcessingCell: Locator = this.page.locator('[data-field="status"]').filter({ hasText: 'PROCESSING' });
  statusReadyCell: Locator = this.page.locator('[data-field="status"]').filter({ hasText: 'READY' });


  sourceLocalCell: Locator = this.page.locator('[data-field="source"]');
  investmentProcessTag: Locator = this.page.locator('[data-field="tags"]').filter({ hasText: 'Investment Process' });
  toast: Locator = this.page.locator('[data-sonner-toast][data-type="success"]').filter({ hasText: 'Delete success!' });

  processButton: Locator = this.page.getByRole('button', { name: 'Process' });
  deleteButton: Locator = this.page.getByRole('button', { name: 'Delete' });
  testDataCheckbox: Locator = this.page
    .locator('[data-field="__tree_data_group__"]')
    .filter({ hasText: "test-data.docx" })
    .locator('xpath=../../preceding-sibling::div[contains(@data-field, "__check__")]//input[@type="checkbox"]');
  selectAllRowsCheckbox: Locator = this.page.locator('input[name="select_all_rows"]');


  async uploadFile() {
    const filePath = path.resolve(__dirname, "../test-data/test-data.docx");
    await this.uploadFileButton.click();
    await this.fileInput.setInputFiles(filePath);

  }
  async selectAndDeleteFile() {
    await this.selectAllRowsCheckbox.click();
    await this.deleteButton.waitFor({ state: 'visible' });
    await this.deleteButton.click();
  }

}