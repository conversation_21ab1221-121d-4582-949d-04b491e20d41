import type { Locat<PERSON>, <PERSON> } from "@playwright/test";

export class DashboardPage {
  constructor(private page: Page) {}

  ddqManagerLink: Locator = this.page.locator('[href="/dashboard/ddq"]');
  dataRoomLink: Locator = this.page.locator('[href="/dashboard/data-room"]');
  responseLibraryLink: Locator = this.page.locator(
    '[href="/dashboard/response"]',
  );
  chatLink: Locator = this.page.locator('[href="/dashboard/chat"]');
  // developmentLink: Locator = this.page.locator(
  //   '[href="/dashboard/development"]',
  // );
}
