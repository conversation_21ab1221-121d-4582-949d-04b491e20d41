import type { Lo<PERSON><PERSON>, <PERSON> } from "@playwright/test";

export class ChatPage {
    constructor(private page: Page) { }

    messageInput: Locator = this.page.locator("#chat-message-input");
    async sendMessage(text: string) {
        await this.messageInput.fill(text);
        await this.page.keyboard.press("Enter");
    }

    async getLatestVirgilResponse(timeout = 120_000): Promise<string> {
        await this.page
            .locator('input[placeholder="Generating answer"]')
            .waitFor({ state: "detached" });
        const label = this.page.locator('span:has-text("Virgil")').last();
        const markdown = label
            .locator(
                'xpath=following-sibling::div//div[contains(@class,"markdown-body")]',
            )
            .first();
        await markdown.waitFor({ timeout });
        return (await markdown.innerText())?.trim() ?? "";
    }
}
