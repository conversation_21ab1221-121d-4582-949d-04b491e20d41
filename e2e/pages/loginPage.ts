import type { Locator, <PERSON> } from "@playwright/test";

export class LoginPage {
  constructor(private page: Page) { }

  appTitle: Locator = this.page.getByText(
    "Welcome back! Please sign in to continue",
  );
  subtitle: Locator = this.page.getByText(
    "Welcome back! Please sign in to continue",
  );
  googleLoginButton: Locator = this.page.getByRole("button", {
    name: "Google",
  });
  microsoftLoginButton: Locator = this.page.getByRole("button", {
    name: "Microsoft",
  });
  emailInput: Locator = this.page.locator('input[name="identifier"]');
  passwordInput: Locator = this.page.locator(
    'input[type="password"][name="password"]',
  );
  editEmailButton: Locator = this.page.locator(".cl-identityPreviewEditButton");
  showPasswordButton: Locator = this.page.locator(
    "button[aria-label='Show password']",
  );
  continueButton: Locator = this.page.getByRole("button", { name: "Continue" });
  identifierErrorMessage: Locator = this.page.locator("#error-identifier");
  forgotPasswordLink: Locator = this.page.getByText(/forgot password/i);
  errorMessageEmail: Locator = this.page.locator(
    '[name="identifier"] + div[aria-invalid="true"]',
  );
  passwordRequiredError: Locator = this.page.locator("#error-password");
  footerText: Locator = this.page.getByText("Development mode");
  logoImage: Locator = this.page.locator("img.cl-logoImage");
  logOutButton: Locator = this.page.getByRole("button", { name: "Logout" });

  async login(email: string, password: string) {
    await this.emailInput.clear();
    await this.emailInput.fill(email);
    await this.passwordInput.clear();
    await this.passwordInput.fill(password);
    await this.continueButton.click();
  }

  async getEmailError() {
    return await this.errorMessageEmail.textContent();
  }
}
