import { test, expect } from "@playwright/test";
import type { BrowserContext, Page } from "@playwright/test";
import { LoginPage } from "../pages/loginPage";
import { DashboardPage } from "../pages/dashboardPage";
import { ChatPage } from "../pages/chatPage";
import { loadDdq } from "../utils/loadDdq";
import { llmSimilarity } from "../utils/anthropicJudge";

const {
    E2E_PLAYWRIGHT_USER_NAME,
    E2E_PLAYWRIGHT_USER_PASSWORD,
    E2E_PLAYWRIGHT_BASEURL,
} = process.env;

test.describe("Chat Test cases", () => {
    let context: BrowserContext;
    let page: Page;
    let chat: ChatPage;
    let qaList: { question: string; answer: string }[];

    test.beforeAll(async ({ browser }) => {
        context = await browser.newContext();
        page = await context.newPage();
        chat = new ChatPage(page);
        const login = new LoginPage(page);
        await page.goto(E2E_PLAYWRIGHT_BASEURL!, { waitUntil: "networkidle" });
        await login.login(E2E_PLAYWRIGHT_USER_NAME!, E2E_PLAYWRIGHT_USER_PASSWORD!);
        const dash = new DashboardPage(page);
        await dash.chatLink.click();
        await expect(chat.messageInput).toBeVisible();
        qaList = await loadDdq();
    });

    test.afterEach(async ({ }, testInfo) => {
        const videoPath = await page.video()?.path();
        if (videoPath) {
            await testInfo.attach("video", {
                path: videoPath,
                contentType: "video/webm",
            });
        }
    });

    test.afterAll(async () => {
        await context.close();
    });

    test("should answer all DDQ questions ≥ 85 % (LLM judge)", async () => {
        test.setTimeout(10 * 60 * 1000);
        for (const { question, answer } of qaList) {
            await chat.sendMessage(question);
            const reply = await chat.getLatestVirgilResponse();

            const score = await llmSimilarity(reply, answer);
            const good = score >= 85;
            test.info().annotations.push({
                type: "similarity",
                description: `Q: ${question} - Claude: ${score}% ${good ? "✅" : "❌"}`,
            });
            expect.soft(score).toBeGreaterThanOrEqual(85);
        }
    });
});
