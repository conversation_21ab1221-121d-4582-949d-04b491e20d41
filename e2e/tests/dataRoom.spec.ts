import { test, expect } from "@playwright/test";
import type { BrowserContext, Page } from "@playwright/test";
import { LoginPage } from "../pages/loginPage";
import { DashboardPage } from "../pages/dashboardPage";
import { DataRoomPage } from "../pages/dataRoomPage";

const E2E_PLAYWRIGHT_USER_NAME = process.env.E2E_PLAYWRIGHT_USER_NAME;
const E2E_PLAYWRIGHT_USER_PASSWORD = process.env.E2E_PLAYWRIGHT_USER_PASSWORD;
const E2E_PLAYWRIGHT_BASEURL = process.env.E2E_PLAYWRIGHT_BASEURL;

if (
  !E2E_PLAYWRIGHT_USER_NAME ||
  !E2E_PLAYWRIGHT_USER_PASSWORD ||
  !E2E_PLAYWRIGHT_BASEURL
) {
  throw new Error("Missing required variables.");
}

test.describe.serial("DataRoom Page", () => {
  let context: BrowserContext;
  let page: Page;
  let loginPage: LoginPage;
  let dashboardPage: DashboardPage;
  let dataRoomPage: DataRoomPage;

  test.beforeAll(async ({ browser }) => {
    context = await browser.newContext({
      recordVideo: {
        dir: "videos/",
        size: { width: 1280, height: 720 },
      },
    });
    page = await context.newPage();
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    dataRoomPage = new DataRoomPage(page);
    await page.goto(E2E_PLAYWRIGHT_BASEURL, { waitUntil: "networkidle" });
  });

  test.afterEach(async ({ }, testInfo) => {
    const videoPath = await page.video()?.path();
    if (videoPath) {
      await testInfo.attach("video", {
        path: videoPath,
        contentType: "video/webm",
      });
    }
  });

  test.afterAll(async () => {
    await context.close();
  });

  test("should login to the system and verify dashboard", async () => {
    await loginPage.login(
      E2E_PLAYWRIGHT_USER_NAME,
      E2E_PLAYWRIGHT_USER_PASSWORD,
    );
    await page.waitForURL(/\/dashboard\/.+/, { timeout: 30000 });
    await expect(dashboardPage.ddqManagerLink).toBeVisible();
    await expect(dashboardPage.dataRoomLink).toBeVisible();
    await expect(dashboardPage.responseLibraryLink).toBeVisible();
    await expect(dashboardPage.chatLink).toBeVisible();

  });

  test("should upload a file in the data room", async () => {
    await dashboardPage.dataRoomLink.click();
    await expect(dataRoomPage.uploadFileButton).toBeVisible();
    await dataRoomPage.uploadFile()
    await expect(page.getByText("test-data.docx")).toBeVisible();
    await dataRoomPage.uploadFileButton.click();
    await page.waitForTimeout(2000);
  });

  test("should verify the file is uploaded to the data room", async () => {
    await dataRoomPage.localTab.click();
    await page.waitForTimeout(3000);
    await expect(page.getByText("test-data.docx")).toBeVisible();
    await expect(dataRoomPage.sourceLocalCell.nth(1)).toHaveText('LOCAL');
    await expect(dataRoomPage.statusProcessingCell).toContainText("PROCESSING");
    let success = false;
    for (let i = 0; i < 3; i++) {
      await page.waitForTimeout(3000);
      await page.reload();
      await dataRoomPage.localTab.click();
      try {
        await expect(dataRoomPage.statusReadyCell).toContainText("READY");
        // await expect(dataRoomPage.investmentProcessTag).toContainText("Investment Process");
        success = true;
        break;
      } catch (error) {
        if (i === 2) {
          throw error;
        }
      }
    }
    if (!success) {
      throw new Error("Conditions not met after 3 attempts.");
    }
  });

  test("should delete the uploaded file", async () => {
    await dataRoomPage.selectAndDeleteFile();
    await page.waitForTimeout(2000);
    await expect(dataRoomPage.toast).toBeVisible();
    await expect(page.getByText("test-data.docx")).not.toBeVisible();
  });

});