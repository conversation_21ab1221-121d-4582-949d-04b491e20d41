import { test, expect } from "@playwright/test";
import type { BrowserContext, Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { LoginPage } from "../pages/loginPage";
import { DashboardPage } from "../pages/dashboardPage";

const E2E_PLAYWRIGHT_USER_NAME = process.env.E2E_PLAYWRIGHT_USER_NAME;
const E2E_PLAYWRIGHT_USER_PASSWORD = process.env.E2E_PLAYWRIGHT_USER_PASSWORD;
const E2E_PLAYWRIGHT_BASEURL = process.env.E2E_PLAYWRIGHT_BASEURL;

if (
  !E2E_PLAYWRIGHT_USER_NAME ||
  !E2E_PLAYWRIGHT_USER_PASSWORD ||
  !E2E_PLAYWRIGHT_BASEURL
) {
  throw new Error("Missing required variables.");
}

test.describe("Login Page", () => {
  let context: BrowserContext;
  let page: Page;
  let loginPage: LoginPage;
  let dashboardPage: DashboardPage;

  test.beforeAll(async ({ browser }) => {
    context = await browser.newContext({
      recordVideo: {
        dir: "videos/",
        size: { width: 1280, height: 720 },
      },
    });
    page = await context.newPage();
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    await page.goto(E2E_PLAYWRIGHT_BASEURL, { waitUntil: "networkidle" });
  });

  test.afterEach(async ({ }, testInfo) => {
    const videoPath = await page.video()?.path();
    if (videoPath) {
      await testInfo.attach("video", {
        path: videoPath,
        contentType: "video/webm",
      });
    }
  });

  test.afterAll(async () => {
    await context.close();
  });

  test.describe("Login Page - Negative Flow", () => {
    test("should Verify the login page", async () => {
      await loginPage.appTitle.waitFor({ state: "visible" });
      await expect(loginPage.emailInput).toBeVisible();
      await expect(loginPage.passwordInput).toBeVisible();
      await expect(loginPage.continueButton).toBeVisible();
    });

    test("should show error message for empty username", async () => {
      await loginPage.continueButton.click();
      const isValid = await loginPage.emailInput.evaluate((input) => {
        const htmlInput = input as HTMLInputElement;
        return htmlInput.checkValidity();
      });
      expect(isValid).toBe(false);
    });

    test("should show error message for empty password", async () => {
      await loginPage.login(E2E_PLAYWRIGHT_USER_NAME, "");
      await loginPage.continueButton.click();
      await expect(loginPage.passwordRequiredError).toBeVisible();
      await expect(loginPage.passwordRequiredError).toHaveText(
        "This field is required and cannot be empty.",
      );
    });

    test("should show error message for invalid credentials", async () => {
      await loginPage.editEmailButton.click();
      await loginPage.login(faker.internet.email(), faker.internet.password());
      await expect(loginPage.identifierErrorMessage).toHaveText(
        "No account found with this identifier. Please check and try again.",
      );
    });
  });
  test.describe("Login Page - Positive Tests", () => {
    test("should login to the system and verify dashboard, logout and verify login page again", async () => {
      await loginPage.login(
        E2E_PLAYWRIGHT_USER_NAME,
        E2E_PLAYWRIGHT_USER_PASSWORD,
      );
      await page.waitForURL(/\/dashboard\/.+/, { timeout: 30000 });
      await expect(dashboardPage.ddqManagerLink).toBeVisible();
      await expect(dashboardPage.dataRoomLink).toBeVisible();
      await expect(dashboardPage.responseLibraryLink).toBeVisible();
      await expect(dashboardPage.chatLink).toBeVisible();
    });
  });
});
