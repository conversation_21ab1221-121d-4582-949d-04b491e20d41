import { setTimeout as wait } from "timers/promises";

// Use Claude 3.5 Sonnet model
const MODEL = process.env.ANTHROPIC_MODEL ?? "claude-3-sonnet-20240229";
const KEY = process.env.ANTHROPIC_API_KEY;

if (!KEY) throw new Error("Missing ANTHROPIC_API_KEY");

type Score = number; // 0–100
let lastCall = 0;
async function throttle(minGapMs = 1000) {
    const gap = Date.now() - lastCall;
    if (gap < minGapMs) await wait(minGapMs - gap);
    lastCall = Date.now();
}

/** Ask <PERSON> to rate `candidate` vs `reference` and return 0-100 */
export async function llmSimilarity(
    candidate: string,
    reference: string,
): Promise<Score> {
    await throttle();

    const system = `You are a strict grader.
Return ONLY an integer from 0 to 100, indicating how well
the candidate answer matches the meaning and intent of the reference. Consider semantic similarity, factual accuracy, and completeness of the response.
100 = equivalent meaning with all key information present, 0 = completely different meaning.
Do not add any words or symbols—just the number.`;

    const user = `Reference answer:
"""${reference.replace(/"""|```/g, "")}"""

Candidate answer:
"""${candidate.replace(/"""|```/g, "")}"""`;

    const res = await fetch("https://api.anthropic.com/v1/messages", {
        method: "POST",
        headers: {
            "x-api-key": KEY!,
            "anthropic-version": "2023-06-01",
            "content-type": "application/json",
        } as HeadersInit,
        body: JSON.stringify({
            model: MODEL,
            max_tokens: 10,
            temperature: 0,
            system,
            messages: [{ role: "user", content: user }],
        }),
    });

    if (!res.ok) {
        const msg = await res.text();
        throw new Error(`Anthropic error ${res.status}: ${msg}`);
    }

    const { content } = (await res.json()) as {
        content: { text: string }[];
    };

    // Claude replies in content[0].text
    const raw = content?.[0]?.text?.trim() ?? "0";
    const score = parseInt(raw, 10);
    if (Number.isNaN(score)) throw new Error(`Non-numeric score: "${raw}"`);
    return Math.max(0, Math.min(100, score));
}
