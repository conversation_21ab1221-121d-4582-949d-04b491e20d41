import path from "path";
import mammoth from "mammoth";

interface QaPair {
    question: string;
    answer: string;
    questionNumber: number;
}

export class DocumentParsingError extends Error {
    constructor(message: string) {
        super(message);
        this.name = "DocumentParsingError";
    }
}

/**
 * Loads and parses a document file containing Q&A pairs.
 */

export async function loadDdq(
    fileName = "chat-ddq.docx",
    dir = path.resolve(__dirname, "../test-data"),
): Promise<QaPair[]> {
    try {
        const filePath = path.join(dir, fileName);
        const { value: rawText } = await mammoth.extractRawText({ path: filePath });
        if (!rawText) {
            throw new DocumentParsingError("Empty document or parsing failed");
        }
        const normalizedText = normalizeText(rawText);
        return extractQaPairs(normalizedText);
    } catch (error) {
        if (error instanceof DocumentParsingError) throw error;
        const errorMessage =
            error instanceof Error ? error.message : "Unknown error";
        throw new DocumentParsingError(
            `Failed to parse ${fileName}: ${errorMessage}`,
        );
    }
}

function normalizeText(text: string): string {
    return text.replace(/\s+/g, " ").trim();
}

function extractQaPairs(text: string): QaPair[] {
    const QA_PATTERN =
        /Question\s*(\d+):\s*(.*?)\s*Answer\s*:\s*(.*?)(?=Question\s*\d+:|$)/gi;
    const pairs: QaPair[] = [];
    let match: RegExpExecArray | null;
    while ((match = QA_PATTERN.exec(text))) {
        const [, num, question, answer] = match;
        if (question?.trim() && answer?.trim()) {
            pairs.push({
                questionNumber: parseInt(num, 10),
                question: question.trim(),
                answer: answer.trim(),
            });
        }
    }

    if (pairs.length === 0) {
        throw new DocumentParsingError("No valid Q&A pairs found in document");
    }

    return pairs;
}
