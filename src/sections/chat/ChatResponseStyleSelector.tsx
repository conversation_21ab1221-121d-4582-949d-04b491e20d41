import { Collapse } from "@mui/material";
import { useState } from "react";
import Select, { type SingleValue } from "react-select";
import { useBoolean } from "src/hooks/use-boolean";
import { ResponseStyle } from "~/lib/types";
import { type ModelParameters } from "./ChatModelParametersSelector";
import { CollapseButton } from "./styles";

type Props = {
  modelParameters: ModelParameters;
  setModelParameters: (modelParameters: ModelParameters) => void;
};

export function ChatResponseStyleSelector({
  modelParameters,
  setModelParameters,
}: Props) {
  const options = Object.entries(ResponseStyle).map(([key, value]) => ({
    value: value,
    label: value,
  }));

  const [value, setValue] = useState<string | undefined>(
    modelParameters.responseStyle,
  );
  const collapse = useBoolean(true);

  const handleChange = (
    newValue: SingleValue<{ value: string; label: string }>,
  ) => {
    const selectedResponseStyle = newValue?.value;

    setValue(selectedResponseStyle);
    setModelParameters({
      ...modelParameters,
      responseStyle: selectedResponseStyle as ResponseStyle,
    });
  };

  return (
    <>
      <CollapseButton selected={collapse.value} onClick={collapse.onToggle}>
        {`Response Style`}
      </CollapseButton>

      <Collapse in={collapse.value}>
        <Select
          value={options.filter((option) => value === option.value)}
          isClearable={false}
          placeholder="Response style"
          isLoading={false}
          name="responseStyle"
          className="basic-multi-select"
          classNamePrefix="select"
          onChange={handleChange}
          options={options}
          styles={{
            container: (base) => ({
              ...base,
              padding: 2,
              borderRadius: 2,
              marginTop: 2,
            }),
          }}
        />
      </Collapse>
    </>
  );
}
