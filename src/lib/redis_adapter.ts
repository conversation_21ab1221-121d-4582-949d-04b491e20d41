// import { createClient } from "redis";
//
// export async function writeFileBlobToRedis(filePath: string, buffer: any) {
//   // Create a new Redis client instance.
//   const client = createClient();
//
//   // Define a key under which the file content will be saved.
//   const redisKey = filePath;
//
//   try {
//     // Connect to the Redis server.
//     await client.connect();
//
//     await client.set(redisKey, buffer);
//
//     console.log(
//       `File content successfully written to Redis with key '${redisKey}'`,
//     );
//   } catch (err) {
//     console.error("Error writing file to Redis:", err);
//     return -1;
//   } finally {
//     // Disconnect the client.
//     await client.quit();
//   }
//
//   return redisKey;
// }
//
// export async function getJsonFromRedis(redisKey: string): Promise<any> {
//   // Create a Redis client instance.
//   const client = createClient();
//
//   // Listen for errors.
//   client.on("error", (err) => console.error("Redis Client Error", err));
//
//   // Connect to Redis.
//   await client.connect();
//
//   try {
//     // Get the JSON text stored in Redis by the provided key.
//     const jsonString = await client.get(redisKey);
//     if (!jsonString) {
//       throw new Error(`No value found for key: ${redisKey}`);
//     }
//
//     // Parse the JSON string into an object.
//     const jsonObject = JSON.parse(jsonString);
//     return jsonObject;
//   } catch (error) {
//     console.error("Error reading JSON from Redis:", error);
//     throw error;
//   } finally {
//     // Ensure the client disconnects.
//     await client.quit();
//   }
// }
//
// export async function waitForKey<T>(redisKey: string, timeout = 60000): Promise<T> {
//
//   //
//   // TODO Note: remember to set "CONFIG SET notify-keyspace-events KE" on Redis
//   //
//
//   // Create the main client and a duplicate for subscriptions.
//   const client = createClient();
//   const subscriber = client.duplicate();
//
//   client.on('error', (err) => console.error('Redis Client Error', err));
//   subscriber.on('error', (err) => console.error('Redis Subscriber Error', err));
//
//   await client.connect();
//   await subscriber.connect();
//
//   // Check immediately if the key is already available.
//   const existingValue = await client.get(redisKey);
//   if (existingValue !== null) {
//     await subscriber.disconnect();
//     await client.disconnect();
//     return JSON.parse(existingValue) as T;
//   }
//
//   // Build the event channel for keyspace notifications.
//   // In Redis, the keyspace channel format is: __keyspace@<db>__:<key>
//   // Here we assume we're using database 0.
//   const eventChannel = `__keyspace@0__:${redisKey}`;
//
//   return new Promise<T>(async (resolve, reject) => {
//     // Set up a timeout in case the key is never set.
//     const timeoutHandle = setTimeout(async () => {
//       await subscriber.unsubscribe(eventChannel);
//       await subscriber.disconnect();
//       await client.disconnect();
//       reject(new Error('Timeout waiting for key to be set'));
//     }, timeout);
//
//     // Subscribe to the keyspace notification channel for this key.
//     await subscriber.subscribe(eventChannel, async (message) => {
//       // Listen for the 'set' event indicating that the key was written.
//       if (message === 'set') {
//         try {
//           const newValue = await client.get(redisKey);
//           if (newValue !== null) {
//             clearTimeout(timeoutHandle);
//             await subscriber.unsubscribe(eventChannel);
//             await subscriber.disconnect();
//             await client.disconnect();
//             resolve(JSON.parse(newValue) as T);
//           }
//         } catch (error) {
//           clearTimeout(timeoutHandle);
//           await subscriber.unsubscribe(eventChannel);
//           await subscriber.disconnect();
//           await client.disconnect();
//           reject(error);
//         }
//       }
//     });
//   });
// }