import { Command } from "commander";
import { promises as fs } from "fs";
import path from "path";
import { db } from "~/server/db";
import { insertEmbeddedIntoDb } from "./vectorizer.insert-embedded";
import { DocumentChunkType } from "@prisma/client";

async function main() {
  const program = new Command();
  program
    .name("vectorizer-insert-chunks")
    .description("Insert chunks from vectorize.json into DB")
    .requiredOption(
      "-i, --input <path>",
      "Path to vectorize.json",
      "data/gallatin/intermediate/vectorize.json",
    )
    .option(
      '-u, --user-email <address>',
      'user email to immitate, if not provided, will use the first user in the database',
    )
    .option("-o, --output <path>", "Optional output path for logs/results");

  program.parse();
  const options = program.opts();
  const inputPath: string = options.input;
  const outputPath: string | undefined = options.output;

  const { orgId } = await db.userOrg.findFirstOrThrow(
    true ? {
      where: {
        user: {
          email: options.userEmail
        }
      }
    } : void 0
  );

  const docs = JSON.parse(await fs.readFile(inputPath, "utf-8"));
  const results: { doc_id: string; status: string; error?: string }[] = [];
  for (const doc of docs) {
    const docId = doc.doc_id;
    const docName = doc.doc_context || doc.doc_id;
    const chunks = doc.contextual_chunks;
    try {
      // Clean old chunks
      await db.documentChunk.deleteMany({ where: { documentId: docId } });
      // Insert new chunks
      await insertEmbeddedIntoDb(
        chunks,
        {
          db,
          document: { id: docId, name: docName },
          orgId,
        },
        undefined,
        DocumentChunkType.JUMBO,
      );
      console.log(`Inserted chunks for doc_id=${docId}`);
      results.push({ doc_id: docId, status: "success" });
    } catch (err) {
      console.error(`Failed for doc_id=${docId}:`, err);
      results.push({ doc_id: docId, status: "error", error: String(err) });
    }
  }
  if (outputPath) {
    await fs.mkdir(path.dirname(outputPath), { recursive: true });
    await fs.writeFile(outputPath, JSON.stringify(results, null, 2), "utf-8");
  }
}

if (require.main === module) {
  main().catch(console.error);
}
