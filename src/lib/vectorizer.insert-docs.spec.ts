import { faker } from "@faker-js/faker";
import { DocumentStatus } from "@prisma/client";
import { Command } from "commander";
import { promises as fs } from "fs";
import path from "path";
import xlsx from "xlsx";
import { db } from "~/server/db";
import { getDocumentTypeByExtension } from "~/utils/document";

function redirectLog() {
  const log = console.log;
  // console.log = () => {};
  const restore = () => {
    console.log = log;
  };
  return { log, restore };
}

// const log = console.log;
const { log, restore } = redirectLog();

async function processReadmeFile(
  filePath: string,
): Promise<{ absolutePath: string; path: string }[]> {
  // Read the Excel file
  const workbook = xlsx.readFile(filePath);

  const sheet = workbook.Sheets.Index;

  // Convert sheet to JSON
  const jsonData = xlsx.utils.sheet_to_json<{ Path: string }>(sheet!);

  const baseDir = path.dirname(filePath);

  // Extract paths from each row
  const paths = jsonData
    .map((row) => row.Path)
    .filter(Boolean)
    .map((relativePath) => ({
      path: relativePath,
      absolutePath: path.resolve(baseDir, relativePath),
    }));

  return paths;
}

async function main() {

  await db.document.deleteMany();

  const program = new Command();

  program
    .name("vectorizer-insert-docs-chunks")
    .description("Process documents from URI")
    .argument("<path>", "Path to README.xlsx file")
    .option("-v, --verbose", "enable verbose output")
    .option(
      '-u, --user-email <address>',
      'user email to immitate, if not provided, will use the first user in the database',
    )
    .requiredOption("-o, --ouput-path <path>", "path to save ouput results")
    .version("1.0.0");

  program.parse();
  const options = program.opts();
  const readmePath = program.args[0]!;

  const paths = await processReadmeFile(readmePath);

  const ouputPath: string = options.ouputPath;

  const { userId, orgId } = await db.userOrg.findFirstOrThrow(
    true ? {
      where: {
        user: {
          email: options.userEmail
        }
      }
    } : void 0
  );

  await fs.mkdir(path.dirname(ouputPath), { recursive: true });
  const res = await Promise.all(
    paths.map(async (p) => {
      console.log(`Processing ${p.path}`);

      // Inside your async function:
      const stat = await fs.stat(p.absolutePath);
      const fileSize = stat.size;

      const docReponse = await db.document.create({
        data: {
          id: faker.string.uuid(),
          name: p.path,
          size: fileSize,
          type: getDocumentTypeByExtension(path.extname(p.absolutePath)),
          orgId,
          createdById: userId,
          status: DocumentStatus.READY,
          url: p.path,
        },
      });

      console.log(`Processed ${p.path} -> ${docReponse.id}`);
      return {
        doc: docReponse,
        path: path.relative(process.cwd(), p.absolutePath),
        type: path.extname(p.absolutePath),
      };
    }),
  );

  await fs.writeFile(ouputPath, JSON.stringify(res, null, 2), "utf-8");
}

// Execute if run directly (not imported as module)
// if (require.main === module) {
main().catch(console.error);
// }
