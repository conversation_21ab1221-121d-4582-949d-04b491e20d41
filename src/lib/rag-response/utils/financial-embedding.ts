import { env } from "~/env";
import { getPyrpcApiUrl } from "~/utils/url";

export async function financial_embed_document(
  sentences: string[],
): Promise<number[][]> {
  const response = await fetch(
    `${getPyrpcApiUrl()}/pyrpc/financial-embedding/embed`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        input: { message: sentences },
      }),
    },
  );

  if (!response.ok || response.status !== 200 || !response.body) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const { embeddings: embedding } = await response.json();

  return embedding;
}

export async function financial_embed_query(
  sentence: string,
): Promise<number[]> {
  const response = await fetch(
    `${getPyrpcApiUrl()}/pyrpc/financial-embedding/embed`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${env.CHUNKING_API_SECRET}`,
      },
      body: JSON.stringify({
        input: { message: sentence },
      }),
    },
  );

  if (!response.ok || response.status !== 200 || !response.body) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const { embeddings: embedding } = await response.json();

  return embedding;
}
