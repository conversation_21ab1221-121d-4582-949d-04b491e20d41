import { OpenAIEmbeddings } from "@langchain/openai";

const DELIM = "__@__";

export async function embed_document(
  embedder: OpenAIEmbeddings,
  sentences: string[],
): Promise<number[][]> {
  // Get the contents, and compute element-wise average for each sentence embedding
  const averagedEmbeddings: Promise<number[]>[] = sentences.map(
    (sentence: string) => {
      try {
        const content: string = JSON.parse(sentence).content;

        if (!content.includes(DELIM)) {
          const _sentence = content;
          return (async (): Promise<number[]> => {
            return embedder.embedQuery(_sentence);
          })();
        } else {
          const [docContext, _sentence] = content.split(DELIM, 2);

          const avrgSentenceEmbedding:Promise<number[]> = (async () => {
            // early exit if input is missing
            if (!docContext || !_sentence) {
              throw new Error(
                `No sentence or document context found for embedder: [sentence=${sentence} [doc ctx=${docContext}]`,
              );
            }

            try {
              // now T<PERSON> knows docContext and _sentence are string, not undefined
              const [docCtxEmb, sentenceEmb] = await Promise.all([
                embedder.embedQuery(docContext),
                embedder.embedQuery(_sentence),
              ]);

              // simple element-wise average
              return Promise.resolve(docCtxEmb.map((v, i) => (v + sentenceEmb[i]!) / 2));
            } catch (err) {
              console.error(err);
              throw err;
            }
          })();

          return avrgSentenceEmbedding;
        }
      } catch (err) {
        throw err;
      }
    },
  );

  return Promise.all(averagedEmbeddings)
}

export async function embed_query(
  embedder: OpenAIEmbeddings,
  sentence: string,
): Promise<number[]> {
  if (!sentence.includes(DELIM)) {
    return embedder.embedQuery(sentence);
  } else {
    const [docContext, _sentence] = sentence.split(DELIM, 2);

    const [docCtxEmb, sentenceEmb] = await Promise.all([
      embedder.embedQuery(docContext!),
      embedder.embedQuery(_sentence!),
    ]);
    return docCtxEmb.map((v, i) => (v + sentenceEmb[i]!) / 2);
  }
}
