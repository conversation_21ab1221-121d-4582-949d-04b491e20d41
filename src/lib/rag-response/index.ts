import type { PrismaClientType } from "~/server/db";
import {
  type RAGResponseWithCitations,
  type RAGResponseWithCitationsAndSimilarQuestions,
} from "../types";

import { isValidJSON } from "~/utils/json";

import { AIMessage, HumanMessage } from "@langchain/core/messages";
import { type ModelParameters } from "~/sections/chat/ChatModelParametersSelector";
import { backoffDelay } from "~/server/utils/backoff";
import { generateAnswer as generateAnswerAgentic } from "./agentic-response";
import {
  type ContextualizeQSchema,
  contextualizeQuestion,
} from "./steps/1-contextualize-question";
import { conversation } from "./steps/2a-conversation";
import { retrieveDocuments } from "./steps/3-retrieve-documents";
import { generateCitations } from "./steps/5a-generate-citations";
import {
  generateAnswer as generateAnswer5b,
  generateDDQResponses,
} from "./steps/5b-generate-answer";
import { processResponse } from "./steps/6-process-response";
import { saveResponse } from "./steps/7-save-response";
import type { DocumentChunkType } from "./steps/types";
import { VoyageAIEmbedding } from "./utils/voyageai-embedding-openai-compat";
import type { AgenticResponse } from "~/lib/types-from-pydantic.zod";
import { tracing } from "../../server/api/managers/tracingManager";
import { randomBytes } from "crypto";

export type GenerateRAGResponse = {
  response: RAGResponseWithCitationsAndSimilarQuestions | null;
  chunks: DocumentChunkType[] | null;
  chatMessageId: string | undefined;
  createdById: string | undefined;
  debug?: ContextualizeQSchema;
  agenticResponseDebug?: AgenticResponse;
};

type RagResponse = {
  db: PrismaClientType;
  message: string;
  messageHistory: { role: string; content: string }[];
  orgId: string;
  worksheet: string | undefined;
  openDocumentId: string | undefined;
  address?: string;
  currentUserId: string;
  tagIds: string[];
  fundIds: string[];
  chatMessageId: string | undefined;
  createdById: string | undefined;
  modelParameters: ModelParameters;
  conversationId?: string;
};

export async function* generateRAGResponse(
  opts: RagResponse,
): AsyncGenerator<GenerateRAGResponse, void, unknown> {
  console.log("opts.modelParameters", opts.modelParameters);
  if (opts.modelParameters.agentic_mode_langgraph) {
    console.log("Running RAG response agentic");
    yield* generateRAGResponseAgentic(opts);
  } else {
    console.log("Running RAG response with pipeline");
    yield* generateRAGResponseWithPipeline(opts);
  }
}

async function* generateRAGResponseAgentic({
  db,
  message,
  messageHistory,
  orgId,
  worksheet,
  openDocumentId,
  address,
  currentUserId,
  tagIds,
  fundIds,
  chatMessageId,
  createdById,
  modelParameters,
  conversationId,
}: RagResponse): AsyncGenerator<GenerateRAGResponse, void, unknown> {
  try {
    const orgInfo = await db.org.findFirstOrThrow({
      where: {
        id: orgId,
      },
      select: {
        name: true,
      },
    });

    const orgContext = {
      institutionName: orgInfo.name,
      orgId: orgId,
    };

    const answer = "";
    yield {
      response: {
        answer: answer,
        citations: [],
        similarQuestions: [],
      },
      chunks: null,
      chatMessageId,
      createdById,
    };

    const responseWithCitationsChunk = generateAnswerAgentic(
      message,
      messageHistory,
      orgContext,
      chatMessageId,
      tagIds,
      fundIds,
      modelParameters,
      conversationId,
    );

    let responseWithCitations: RAGResponseWithCitations = {};

    for await (const chunk of responseWithCitationsChunk) {
      responseWithCitations = chunk;
      const chunkResponse = {
        response: {
          agenticResponseDebug: chunk.agenticResponseDebug,
          status: chunk.status,
          answer: chunk.answer,
          citations: [],
          similarQuestions: [],
        },
        chunks: null,
        chatMessageId,
        createdById,
      };
      console.log("chunkResponse", chunkResponse);
      console.log("chunkResponseDebug", chunk.agenticResponseDebug);
      yield chunkResponse;
    }

    await saveResponse(message, responseWithCitations, {
      db: db,
      orgId: orgId,
      currentUserId: currentUserId,
      openDocumentId: openDocumentId,
      address: address,
    });

    yield {
      response: {
        ...responseWithCitations,
        similarQuestions: [],
      },
      chunks: [],
      chatMessageId,
      createdById,
    };
  } catch (error) {
    console.error("Error generating response", error);
    yield {
      response: null,
      chunks: null,
      chatMessageId,
      createdById,
    };
  }
}

async function* generateRAGResponseWithPipeline({
  db,
  message,
  messageHistory,
  orgId,
  worksheet,
  openDocumentId,
  address,
  currentUserId,
  tagIds,
  fundIds,
  chatMessageId,
  createdById,
  modelParameters,
  conversationId,
}: RagResponse): AsyncGenerator<GenerateRAGResponse, void, unknown> {
  const rootTrace = tracing.trace({
    id: randomBytes(16).toString("hex"),
    name: "generateRAGResponseWithPipeline",
    sessionId: conversationId,
    input: {
      message,
      messageHistory,
      orgId,
      worksheet,
      openDocumentId,
      address,
      currentUserId,
      tagIds,
      fundIds,
      chatMessageId
    }
  });

  try {
    const messageHistoryText = messageHistory.map((message) =>
      message.role === "user"
        ? new HumanMessage(
          isValidJSON(message.content)
            ? String(JSON.parse(message.content).answer)
            : String(message.content),
        )
        : new AIMessage(
          isValidJSON(message.content)
            ? String(JSON.parse(message.content).answer)
            : String(message.content),
        ),
    );

    const orgInfo = await db.org.findFirstOrThrow({
      where: {
        id: orgId,
      },
      select: {
        name: true,
      },
    });
    const orgContext = {
      institutionName: orgInfo.name,
      sessionId: conversationId,
    };

    // Add this before processing the message

    const classifyingInputAndContextulizedQ = await contextualizeQuestion(
      {
        message,
        messageHistory: messageHistoryText,
      },
      orgContext,
      rootTrace,
    );

    const { type: inputType, explanation } = classifyingInputAndContextulizedQ;

    // Only proceed with RAG if it's a question
    if (classifyingInputAndContextulizedQ.type === "casual") {
      const response = await conversation(
        {
          message,
          messageHistory: messageHistoryText,
        },
        orgContext,
        modelParameters,
      );

      yield {
        response: {
          answer: response,
          citations: [],
          similarQuestions: [],
        },
        chunks: null,
        chatMessageId,
        createdById,
        debug: classifyingInputAndContextulizedQ,
      };
      return;
    } else if (classifyingInputAndContextulizedQ.type === "unclear") {
      yield {
        response: {
          answer:
            "I apologize, but I'm having trouble understanding your question. Could you rephrase it for me?",
          citations: [],
          similarQuestions: [],
        },
        chunks: null,
        chatMessageId,
        createdById,
        debug: classifyingInputAndContextulizedQ,
      };

      return;
    }

    if (classifyingInputAndContextulizedQ.type !== "work") {
      throw new Error("Invalid input type");
    }

    const contextualizedQ = Promise.resolve(
      classifyingInputAndContextulizedQ.question ?? "",
    );

    const chunks = retrieveDocuments(
      message,
      {
        db,
        orgId,
        openDocumentId,
        worksheet,
        limit: 50,
        tagIds,
        fundIds,
      },
      rootTrace,
    );

    const citations = generateCitations(
      {
        contextualizedQuestion: contextualizedQ,
        chunks,
      },
      orgContext,
      rootTrace
    );

    let answer = "";
    yield {
      response: {
        answer: answer,
        citations: [],
        similarQuestions: [],
      },
      chunks: null,
      chatMessageId,
      createdById,
      debug: classifyingInputAndContextulizedQ,
    };

    // TODO DORON 070525 chunks instead of citations, passing both
    const response = generateAnswer5b(
      contextualizedQ,
      chunks,
      citations,
      orgContext,
      chatMessageId,
      modelParameters,
      rootTrace,
    );

    for await (const chunk of response) {
      answer = chunk.answer;
      yield {
        response: {
          answer: answer,
          citations: [],
          similarQuestions: [],
        },
        chunks: null,
        chatMessageId,
        createdById,
        debug: classifyingInputAndContextulizedQ,
      };
    }

    // Format the LLM response to a RAGResponseWithCitations
    const { citationsWithSourceIdsAndQuotes } = await citations;
    const responseWithCitations = await processResponse(
      {
        answer,
        citationsWithSourceIdsAndQuotes,
      },
      chunks,
    );

    await saveResponse(message, responseWithCitations, {
      db: db,
      orgId: orgId,
      currentUserId: currentUserId,
      openDocumentId: openDocumentId,
      address: address,
    });

    yield {
      response: {
        ...responseWithCitations,
        similarQuestions: [],
      },
      chunks: await chunks,
      chatMessageId,
      createdById,
      debug: classifyingInputAndContextulizedQ,
    };
  } catch (error) {
    console.error("Error generating response", error);
    yield {
      response: null,
      chunks: null,
      chatMessageId,
      createdById,
      debug: { type: "unclear", explanation: "Error generating response" },
    };
  }
}

export type QuestionInput = {
  questionId: string;
  question: string;
  questionType: string;
  answerTemplate: string;
  customPrompt: string;
};

export type GeneratedResponse = RAGResponseWithCitations & {
  questionId: string;
  reason: string;
};

export async function generateResponseFromDocumentQuestions(
  db: PrismaClientType,
  orgId: string,
  documentId: string,
  questions: QuestionInput[],
  tagIds: string[],
  fundIds: string[],
  modelParameters: ModelParameters,
  secret?: Record<string, string>,
): Promise<GeneratedResponse[] | null> {
  try {
    const orgInfo = await db.org.findFirstOrThrow({
      where: {
        id: orgId,
      },
      select: {
        name: true,
      },
    });

    const orgContext = {
      institutionName: orgInfo.name,
    };

    // Extract chunks and deduplicate them
    // Show how many duplicate chunks there are
    // Show overall token usage and token savings
    const totalChunks = await Promise.all(
      questions.map(async (q) => {
        // Check to see if we need to add the custom prompt to the question
        const chunks = await retrieveDocuments(q.question, {
          db,
          orgId,
          openDocumentId: documentId,
          worksheet: undefined,
          // Need to check if this is the correct limit or if we're pulling too many chunks
          limit: 50,
          tagIds,
          fundIds,
          secret,
        });
        return chunks;
      }),
    );

    const totalChunksFlattened = totalChunks.flat();
    const totalChunksFlattenedMetadataIds = totalChunksFlattened.map(
      (chunk) => chunk.metadata.id,
    );

    const totalChunksCount = totalChunksFlattened.length;

    console.log("totalChunksCount", totalChunksCount);
    const duplicateIds = findDuplicates(totalChunksFlattenedMetadataIds);
    console.log("Duplicate chunks:", duplicateIds.length);

    // Keep one instance of each chunk, removing subsequent duplicates
    const uniqueChunks = totalChunksFlattened.filter(
      (chunk, index, self) =>
        index === self.findIndex((c) => c.metadata.id === chunk.metadata.id),
    );

    console.log("uniqueChunksCount", uniqueChunks.length);

    console.time("filterUniqueChunks");

    // If there are more than 140 chunks, filter out similar chunks. To try and reduce number of tokens.
    const filteredUniqueChunks =
      uniqueChunks.length > 140
        ? await filterSimilarChunks(uniqueChunks, secret)
        : uniqueChunks;

    console.timeEnd("filterUniqueChunks");
    console.log("filteredUniqueChunksCount", filteredUniqueChunks.length);

    const questionsInput = await Promise.all(
      questions.map(async (q) => {
        console.log(
          "generateResponseFromDocumentQuestion: retrieving documents ",
        );

        const contextualizedQuestion = Promise.resolve(q.question);

        console.log(
          "generateResponseFromDocumentQuestion: generating citations",
        );

        const citations = await generateCitations(
          {
            contextualizedQuestion,
            chunks: Promise.resolve(Array.from(filteredUniqueChunks)),
          },
          orgContext,
          undefined,
          3,
          3,
          secret,
        );

        return {
          questionId: q.questionId,
          citations,
          contextualizedQuestion,
          questionType: q.questionType,
          answerTemplate: q.answerTemplate,
          customPrompt: q.customPrompt,
        };
      }),
    );

    console.log("generateResponseFromDocumentQuestion: generating response");

    const MAX_RETRIES = 3;
    let retries = 0;

    while (retries < MAX_RETRIES) {
      try {
        const responses = await generateDDQResponses(
          questionsInput,
          Array.from(filteredUniqueChunks),
          orgContext,
          modelParameters,
          undefined,
          secret,
        );

        console.log("generateDDQResponses returned responses", responses);

        if (responses.length === 0) {
          throw new Error("No responses returned. Retrying...");
        }

        if (responses === null) {
          throw new Error("Response is null. Retrying...");
        }
        // Validate response payload question IDsmatches input question IDs
        const validatedResponseIds = questionsInput.map((p) =>
          responses.find((r) => r.questionId === p.questionId),
        );

        if (validatedResponseIds.length !== questionsInput.length) {
          throw new Error(
            "Response question IDs do not match input question IDs",
          );
        }

        const processedResponses = await Promise.all(
          responses.map(async (response) => {
            const citationsWithSourceIdsAndQuotes = questionsInput.find(
              (q) => q.questionId == response.questionId,
            )?.citations.citationsWithSourceIdsAndQuotes;

            const responseWithCitations = await processResponse(
              {
                answer: response.answer,
                citationsWithSourceIdsAndQuotes:
                  citationsWithSourceIdsAndQuotes ?? [],
              },
              Promise.resolve(Array.from(filteredUniqueChunks)),
            );

            return {
              ...responseWithCitations,
              questionId: response.questionId,
              reason: response.reason,
            };
          }),
        );

        return processedResponses;
      } catch (error) {
        console.error(
          "generateResponseFromDocumentQuestion: Error generating response",
          error,
        );
        retries++;

        await backoffDelay(retries);
      }
    }

    console.error(`Failed to generate response after ${MAX_RETRIES} retries`);
    return await Promise.all(
      questionsInput.map(async (q) => ({
        answer: "Virgil is unable to answer this question",
        citations: [],
        similarQuestions: [],
        questionId: q.questionId,
        reason: "Failed to generate response after 3 retries",
      })),
    );
  } catch (error) {
    console.error(
      "generateResponseFromDocumentQuestion: Error generating response",
      error,
    );
    return null;
  }
}

const findDuplicates = (arr: string[]) => {
  return arr.filter((item, index) => arr.indexOf(item) !== index);
};

// Filter out similar chunks using cosine similarity
// TODO: Check to see if sanitizing strings and doing a simple string comparison would be faster
const filterSimilarChunks = async (
  chunks: DocumentChunkType[],
  secret?: Record<string, string>,
) => {
  const embedder = new VoyageAIEmbedding(secret);

  // Get embeddings for all chunks
  const chunkTexts = chunks.map((chunk) => chunk.pageContent);
  const embeddings = await embedder.embedDocuments(chunkTexts);

  // Calculate cosine similarity between all pairs of chunks
  const similarityThreshold = 0.99; // Chunks with similarity above this will be considered duplicates
  const uniqueChunks: DocumentChunkType[] = [];
  const processedIndices = new Set<number>();

  for (let i = 0; i < chunks.length; i++) {
    if (processedIndices.has(i)) continue;

    const currentChunk = chunks[i];
    if (currentChunk) {
      uniqueChunks.push(currentChunk);
    }
    processedIndices.add(i);

    // Compare with remaining chunks
    for (let j = i + 1; j < chunks.length; j++) {
      if (processedIndices.has(j)) continue;

      const similarity = cosineSimilarity(embeddings[i]!, embeddings[j]!);
      if (similarity > similarityThreshold) {
        processedIndices.add(j);
      }
    }
  }

  return uniqueChunks;
};

// Helper function to calculate cosine similarity between two vectors
function cosineSimilarity(vecA: number[], vecB: number[]): number {
  const dotProduct = vecA.reduce((acc, val, i) => acc + val * vecB[i]!, 0);
  const normA = Math.sqrt(vecA.reduce((acc, val) => acc + val * val, 0));
  const normB = Math.sqrt(vecB.reduce((acc, val) => acc + val * val, 0));
  return dotProduct / (normA * normB);
}
