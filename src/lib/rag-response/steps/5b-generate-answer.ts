import { ChatMessageStatus } from "@prisma/client";
import type { LangfuseTraceClient } from "langfuse";
import { z } from "zod";
import { env } from "~/env";
import { type ModelParameters } from "~/sections/chat/ChatModelParametersSelector";
import { backoffDelay } from "~/server/utils/backoff";
import { updateChatMessageStatus } from "~/server/utils/chat";
import {
  AnswerDataMode,
  getAnswerGenDataMode,
} from "~/utils/answer_gen_data_mode";
import { getPyrpcApiUrl } from "~/utils/url";
import {
  type CitationResponse,
  type DocumentChunkType,
  type LLMResponse,
} from "./types";

export const citationSchema = z
  .object({
    source_id: z
      .number()
      .describe(
        "The integer ID of a SPECIFIC source which justifies the answer.",
      ),
    quote: z
      .string()
      .describe(
        "The VERBATIM quote from the specified source that justifies the answer.",
      ),
    named_entity_mentioned: z
      .boolean()
      .describe(
        "Whether the quote mentions the named entity from the question.",
      ),
  })
  .describe("A cited source from the given text");

export const structuredOutputSchema = z
  .object({
    answer: z.string().describe(`The answer to the user question.`),
  })
  .describe("An answer");

export async function* generateAnswer(
  contextualizedQuestion: Promise<string>,
  chunks: Promise<DocumentChunkType[]>,
  citations: Promise<CitationResponse>,
  context: { institutionName: string; sessionId?: string },
  chatMessageId: string | undefined,
  modelParameters: ModelParameters,
  trace?: LangfuseTraceClient,
): AsyncGenerator<
  LLMResponse & {
    delta: string;
  },
  void,
  unknown
> {
  let answer = "";

  console.time("jumbo_chunks");
  // TODO DORON 070525 add this when the schema adds ChatMessageStatus.GENERATING_CHUNKS
  // await updateChatMessageStatus(
  //   chatMessageId,
  //   ChatMessageStatus.GENERATING_CHUNKS,
  // );
  const jumboChunks: DocumentChunkType[] = await chunks;
  console.timeEnd("jumbo_chunks");

  console.time("citations");
  await updateChatMessageStatus(
    chatMessageId,
    ChatMessageStatus.GENERATING_CITATIONS,
  );
  const { citationsWithSourceIdsAndQuotes } = await citations;
  console.timeEnd("citations");

  console.time("generateAnswer");
  await updateChatMessageStatus(
    chatMessageId,
    ChatMessageStatus.GENERATING_ANSWER,
  );

  const span = trace?.span({
    name: "5b-generate-answer",
    input: {
      contextualizedQuestion: await contextualizedQuestion,
      citations: citationsWithSourceIdsAndQuotes,
      jumboChunks: jumboChunks,
    },
  });

  try {
    const traceId = trace?.id;
    const response = await fetch(
      `${getPyrpcApiUrl()}/pyrpc/generate-answer/step_5b_generate_answer?data_mode=${getAnswerGenDataMode()}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${env.CHUNKING_API_SECRET}`,
          "x-trace-id": traceId ?? "",
        },
        body: JSON.stringify({
          contextualizedQuestion: await contextualizedQuestion,
          context,
          // TODO DORON 070525 add a parameter that tells the python to use chunks, or citations
          jumbo_chunks: JSON.stringify(jumboChunks),
          citations: JSON.stringify(
            citationsWithSourceIdsAndQuotes.map(
              ({ quote, file_name, document_id }) => ({
                quote,
                file_name,
                document_id,
              }),
            ),
          ),
          modelParameters,
        }),
      },
    );

    if (!response.ok || response.status !== 200 || !response.body) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const eventStream = response.body.pipeThrough(new TextDecoderStream());
    // TODO: fix this
    for await (const newData of eventStream as unknown as AsyncGenerator<
      string,
      void,
      unknown
    >) {
      answer += newData;
      yield {
        delta: newData,
        answer,
        citationsWithSourceIdsAndQuotes,
      };
    }

    console.timeEnd("generateAnswer");

    yield {
      delta: "",
      answer,
      citationsWithSourceIdsAndQuotes,
    };
  } catch (error) {
    span?.update({ level: "ERROR", statusMessage: (error as Error).message });
    console.error(error);
    await updateChatMessageStatus(chatMessageId, ChatMessageStatus.ERROR);
    throw error;
  } finally {
    span?.end();
  }
}

export type SingleDDQResponse = {
  questionId: string;
  answer: string;
  reason: string;
  citations: CitationResponse;
};

export async function generateDDQResponses(
  questionsInput: {
    questionId: string;
    citations: CitationResponse;
    contextualizedQuestion: Promise<string>;
    questionType: string;
    answerTemplate: string;
    customPrompt: string;
  }[],
  chunks: DocumentChunkType[],
  context: { institutionName: string },
  modelParameters: ModelParameters,
  trace?: LangfuseTraceClient,
  secret?: Record<string, string>,
): Promise<SingleDDQResponse[]> {
  const span = trace?.span({
    name: "generate-ddq-responses",
    input: {
      questions: questionsInput.length,
      chunks: chunks.length,
    },
  });

  const questionPayload = await Promise.all(
    questionsInput.map(async (q) => ({
      question_id: q.questionId,
      answer_template: q.answerTemplate,
      contextualized_question: await q.contextualizedQuestion,
      context,
      question_type: q.questionType,
      custom_prompt: q.customPrompt,
      citations: JSON.stringify(
        q.citations.citationsWithSourceIdsAndQuotes.map(
          ({ quote, file_name, document_id }) => ({
            quote,
            file_name,
            document_id,
          }),
        ),
      ),
    })),
  );

  console.time(`generateAnswer-${questionsInput.length}`);

  console.log("Calling /pyrpc/generate-answer/generate_ddq_responses");

  const MAX_RETRIES = 3;
  let retries = 0;

  while (retries < MAX_RETRIES) {
    try {
      const traceId = trace?.id;
      const response = await fetch(
        `${getPyrpcApiUrl(secret)}/pyrpc/generate-answer/generate_ddq_responses?data_mode=${getAnswerGenDataMode()}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${env.CHUNKING_API_SECRET ?? secret?.CHUNKING_API_SECRET}`,
            "x-trace-id": traceId ?? "",
          },
          body: JSON.stringify({
            question_array: JSON.stringify(questionPayload),
            jumbo_chunks: JSON.stringify(chunks),
            data_mode: AnswerDataMode.JUMBO_CHUNKS,
            context,
            modelParameters,
          }),
        },
      );

      if (!response.ok || response.status !== 200 || !response.body) {
        if (response.status === 422) {
          console.log(
            "422 error, check validation error.",
            await response.json(),
          );

          return questionsInput.map((q) => ({
            questionId: q.questionId,
            answer: "Virgil is unable to answer this question",
            reason: "Validation error",
            citations: q.citations,
          }));
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.timeEnd(`generateAnswer-${questionsInput.length}`);

      const responseJson = (await response.json()) as SingleDDQResponse[];

      console.log(
        `generateAnswer-${questionsInput.length}: responseJson`,
        JSON.stringify(responseJson, null, 2),
      );

      return responseJson.map((r) => ({
        ...r,
        citations: questionsInput.find((q) => q.questionId === r.questionId)
          ?.citations ?? {
          citationsWithSourceIdsAndQuotes: [],
        },
      }));
    } catch (error) {
      span?.update({ level: "ERROR", statusMessage: (error as Error).message });
      console.error(
        `Error calling ${getPyrpcApiUrl(secret)}/pyrpc/generate-answer/generate_ddq_response?data_mode=${getAnswerGenDataMode()}`,
        error,
      );

      retries++;
      await backoffDelay(retries);
    } finally {
      if (retries >= MAX_RETRIES) {
        span?.end();
      }
    }
  }

  span?.end();
  return [];
}
