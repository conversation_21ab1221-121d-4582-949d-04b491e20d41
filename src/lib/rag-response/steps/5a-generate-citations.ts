import { ChatPromptTemplate } from "@langchain/core/prompts";
import { jsonrepair } from "jsonrepair";
import { z } from "zod";
import {
  bestMatchingSubstring,
  bestMatchingSubstringRatio,
} from "../utils/best-matching-substring";
import { tracing } from "../../../server/api/managers/tracingManager";
import { formatDocsWithId } from "./4-format-documents";
import { newLlm } from "./llm-constructor";
import { personaPromptString } from "./prompt-persona";
import type { CitationResponse, FormattedDocuments } from "./types";
import type { LangfuseTraceClient } from "langfuse";

const citationSchema = z
  .object({
    source_id: z
      .number()
      .describe(
        "The integer ID of a SPECIFIC source which justifies the answer.",
      ),
    quote: z
      .string()
      .describe(
        "The VERBATIM quote from the specified source that justifies the answer.",
      ),
    named_entity_mentioned: z
      .boolean()
      .describe(
        "Whether the quote mentions the named entity from the question.",
      ),
  })
  .describe("A cited source from the given text");

const structuredOutputSchema = z
  .object({
    citationsWithSourceIdsAndQuotes: z
      .array(citationSchema)
      .describe(
        "Citations from the given sources that justify the answer, including the source ID and the quote.",
      ),
  })
  .describe("Relevant citations");

const toolPrompt = `
${personaPromptString}
1. You receive:
    - A user question
    - Multiple document snippets
2. Your tasks are:
    b. Use the provided document snippets for relevant information that directly answers the question.
    c. Provide the relevant quotations in valid JSON
    d. Handle ambiguous references carefully:
      - Terms like "it," "Fund," "Firm," "Company," or "Institution" MUST be clarified by the snippet itself
        or its filename.
    e. You may use general knowledge (e.g., definitions, concepts, theories) as if you are a financial expert
    or investment banker, but do not fabricate any specific details or knowledge about a particular
    "Fund," "Firm," "Company," "Institution," country, or economy if not supported by the snippet text.
    f. The quotations must be verbatim from the snippets, including any errors, typos, punctuation, etc.

The document snippets are provided as follow: 
{context}
`;

// TODO DORON 060525 move to env file, same for the python side
const DELIMITER = "__@__";

export async function generateCitations(
  { contextualizedQuestion, chunks }: FormattedDocuments,
  context: { institutionName: string; sessionId?: string },
  trace?: LangfuseTraceClient,
  maxRetries: number = 3,
  retries: number = maxRetries,
  secret?: Record<string, string>,
): Promise<CitationResponse> {
  const span = trace?.span({
    name: "5a-generate-citations",
    input: {
      contextualizedQuestion: await contextualizedQuestion,
      retriesLeft: retries,
    },
  });

  if (retries <= 0) {
    span?.update({
      output: {
        citationsWithSourceIdsAndQuotes: [],
      },
      level: "WARNING",
      statusMessage: "Max retries reached",
    });
    span?.end();
    return {
      citationsWithSourceIdsAndQuotes: [],
    };
  }

  const llm = newLlm(secret);

  const llmWithTool = llm.withStructuredOutput(structuredOutputSchema, {
    name: "cited_answer",
  });

  const prompt = ChatPromptTemplate.fromMessages([
    ["system", toolPrompt],
    ["human", "{input}"],
  ]);

  try {
    console.log("Generating citations");
    let _docs = formatDocsWithId(await chunks);
    const _items = JSON.parse(jsonrepair(_docs));
    _items.forEach((item: any) => {
      // Example transformations:
      [item.docContext, item.content] = item.content.split(DELIMITER);
      item.filename = item.filename;
    });
    // _docs = JSON.stringify(_items);
    _docs = _items
      .map((_item: any) => {
        return `${_item.docContext}: ${_item.content}`;
      })
      .join("\n\n");

    // TODO DORON 060525 (!) how is the llm prompt build and how does it ingest the _docs?
    const input = await contextualizedQuestion;

    const callback = tracing.getHandlerForTrace(span, context.sessionId);
    const response = await prompt.pipe(llmWithTool).invoke(
      {
        input,
        institution_name: context.institutionName,
        context: _docs,
      },
      { callbacks: callback ? [callback] : [] },
    );

    console.log(
      "Citations generated:",
      response.citationsWithSourceIdsAndQuotes.length,
    );

    const resolvedChunks = await chunks;

    const processedResponse = response.citationsWithSourceIdsAndQuotes
      .filter(({ source_id }) => {
        return (
          resolvedChunks[source_id] !== undefined &&
          resolvedChunks[source_id] !== null
        );
      })
      .map(async ({ source_id, quote, named_entity_mentioned }) => {
        try {
          const chunk = (await chunks)[source_id];

          console.log("chunk", chunk);

          const chunkContent: string = JSON.parse(
            jsonrepair(chunk?.pageContent ?? ""),
          ).content;
          const { minDist, bestSubstring } = bestMatchingSubstring(
            quote,
            chunkContent,
          );
          const matchingErrorRatio = bestMatchingSubstringRatio(
            minDist,
            bestSubstring.length,
          );
          let currentCandidate = {
            source_id,
            document_id: chunk?.metadata?.documentId ?? "",
            quote,
            bestSubstring,
            named_entity_mentioned,
            file_name: chunk?.metadata?.fileName ?? "",
            matchingErrorRatio,
          };
          // TODO DEBUG was 0.1 allowing all docs
          if (currentCandidate.matchingErrorRatio <= 1) {
            return currentCandidate;
          }
          for (let i = 0; i < resolvedChunks.length; i++) {
            if (i === source_id) {
              continue;
            }

            const candidateChunk = resolvedChunks[i];

            if (candidateChunk === undefined) {
              continue;
            }

            if (candidateChunk?.pageContent === "") {
              continue;
            }

            const candidateChunkContent: string = JSON.parse(
              jsonrepair(candidateChunk?.pageContent),
            ).content;
            const { minDist, bestSubstring } = bestMatchingSubstring(
              quote,
              candidateChunkContent,
            );
            const matchingErrorRatio = bestMatchingSubstringRatio(
              minDist,
              bestSubstring.length,
            );
            if (matchingErrorRatio < currentCandidate.matchingErrorRatio) {
              currentCandidate = {
                source_id,
                document_id: candidateChunk?.metadata?.documentId ?? "",
                quote,
                bestSubstring,
                named_entity_mentioned,
                file_name: candidateChunk?.metadata?.fileName ?? "",
                matchingErrorRatio,
              };
            }
            if (currentCandidate.matchingErrorRatio < 0.1) {
              return currentCandidate;
            }
          }
          return currentCandidate;
        } catch (error) {
          console.error(
            `Error processing citation for source_id ${source_id}:`,
            chunks,
            error,
          );
          return {
            source_id,
            document_id: "",
            quote,
            bestSubstring: "",
            named_entity_mentioned,
            file_name: "",
            matchingErrorRatio: Number.POSITIVE_INFINITY,
          };
        }
      })
      .filter(async (p) => {
        const { matchingErrorRatio, quote, bestSubstring } = await p;
        if (matchingErrorRatio > 0.5) {
          // console.warn(
          //   `\nBest matching substring ratio is too high: ${matchingErrorRatio}.\nQuote: ${quote}.\nbestSubstring: ${bestSubstring}\n`,
          // );
          return false;
        } else {
          return true;
        }
      })
      .map(async (p) => {
        const {
          source_id,
          bestSubstring,
          named_entity_mentioned,
          file_name,
          document_id,
        } = await p;
        return {
          source_id,
          quote: bestSubstring,
          named_entity_mentioned,
          file_name,
          document_id,
        };
      });

    const citationsWithSourceIdsAndQuotes =
      await Promise.all(processedResponse);

    span?.update({ output: citationsWithSourceIdsAndQuotes });
    // console.log("G-stage-citations-5", citationsWithSourceIdsAndQuotes);

    return {
      citationsWithSourceIdsAndQuotes,
    };
  } catch (error) {
    span?.update({ level: "ERROR", statusMessage: (error as Error).message });
    console.error("Error generating citations:", error);
    return generateCitations(
      { contextualizedQuestion, chunks },
      context,
      trace,
      maxRetries,
      retries - 1,
    );
  } finally {
    span?.end();
  }
}
