import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from "@langchain/core/prompts";
import { z } from "zod";
import { newLlm } from "./llm-constructor";
import { personaPromptString } from "./prompt-persona";
import type { UserInput } from "./types";
import { tracing } from "../../../server/api/managers/tracingManager";
import type { LangfuseTraceClient, LangfuseSpanClient } from "langfuse";

const explanationSchema = z.object({
  explanation: z
    .string()
    .describe(
      "Brief explanation of why this classification was chosen, and if work-related why this question is formulated this way.",
    ),
});

/*
// OpenAI cannot work with dsicriminated unions

const ContextualizeQSchema  = z.discriminatedUnion("type", [
  z.object({
  type: z.literal("work"),
  question: z.string().describe("The standalone question which can be understood without the chat history."),
}).merge(explanationSchema), z.object({
  type: z.enum(["casual", "unclear"]),
}).merge(explanationSchema)]);
*/

const ContextualizeQSchema = z
  .object({
    type: z
      .enum(["work", "casual", "unclear"])
      .describe("The type of user input"),
    question: z
      .string()
      .optional()
      .describe(
        "The standalone question which can be understood without the chat history.",
      ),
  })
  .merge(explanationSchema);

export type ContextualizeQSchema = z.infer<typeof ContextualizeQSchema>;

const contextualizeQSystemPrompt = `
    ${personaPromptString}
    Given a chat history and the latest user input, 
    which might reference context in the chat history,


    Your task is to classify whether the user input is one of the following:

    - "work" — for Work-related Inquiry: Messages that seek factual information, clarification, or assistance, typically related to tasks, tools, processes, or professional topics. Includes explicit (e.g., "How do I…?") and implicit inquiries (e.g., "I'd like to know more about…" or "Describe…"). his classification applies even if the request is **broad, vague, or lacks specific details** (e.g., "Tell me about insurance policies," "Database information," "How does X work?"). The key is the *intent* to get work-related information. **It absolutely applies if the request is specific but uses potentially unknown acronyms or proper names (e.g., "Details on HPC's SEC examination?", "Status of Project Alpha?"). Treat such requests as work-related if they are structured as a request for factual information on a plausible professional topic.**
    - "casual" — for Casual Statement: Conversational messages that do not seek factual answers. Includes greetings, small talk, humor, personal updates, or expressions of opinion (e.g., "What's up?", "I'm doing fine", "That's funny").
    - "unclear" — for Ambiguous/Unclear: Messages that are vague, incomplete, or not clearly classifiable as either work-related or casual (e.g., "Interesting." or "I'm not sure").

    Furthermore, if it is work-related, formulate a standalone question which can be understood 
    without the chat history. Do NOT answer the question, do not summarize the chat history,
    just reformulate the latest inquiry if needed so that another AI Agent (not you) will answer.`;

const contextualizeQPrompt = ChatPromptTemplate.fromMessages([
  ["system", contextualizeQSystemPrompt],
  new MessagesPlaceholder("chat_history"),
  ["human", "{input}"],
]);

export async function contextualizeQuestion(
  input: UserInput,
  context: { institutionName: string; sessionId?: string },
  trace?: LangfuseTraceClient,
): Promise<ContextualizeQSchema> {
  const span = trace?.span({
    name: "1-contextualize-question",
    input: input,
  });

  const llmRaw = newLlm();

  const llm = llmRaw.withStructuredOutput(ContextualizeQSchema, {
    name: "contextualized_question",
  });

  const contextualizeQChain = contextualizeQPrompt.pipe(llm);

  console.time("contextualizeQuestion");

  const callback = tracing.getHandlerForTrace(span, context.sessionId);
  try {
    const contextualizedQuestion = await contextualizeQChain.invoke(
      {
        input: input.message,
        institution_name: context.institutionName,
        chat_history: input.messageHistory,
      },
      { callbacks: callback ? [callback] : [] },
    );

    console.timeEnd("contextualizeQuestion");
    span?.update({ output: contextualizedQuestion });
    return contextualizedQuestion;
  } catch (e) {
    span?.update({ level: "ERROR", statusMessage: (e as Error).message });
    throw e;
  } finally {
    span?.end();
  }
}
