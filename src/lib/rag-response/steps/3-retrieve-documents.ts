import { Prisma } from "@prisma/client";
import { z } from "zod";
import { VoyageAIEmbedding } from "~/lib/rag-response/utils/voyageai-embedding-openai-compat";
import type { PrismaClientType } from "~/server/db";
import {
  type DocumentChunkMetadataType,
  type DocumentChunkType,
} from "./types";
import pgvector from "pgvector";
import type { LangfuseTraceClient } from "langfuse";

export const RetrieveDocumentsSchema = z.object({
  orgId: z.string(),
  openDocumentId: z.string().nullish(),
  lastChunkIds: z.array(z.string()).optional(),
  worksheet: z.string().nullish(),
  tagIds: z.array(z.string()),
  fundIds: z.array(z.string()),
  limit: z.number().default(50),
});

export type RetrieveDocumentsSchema = z.infer<typeof RetrieveDocumentsSchema>;
export type RetrieveDocumentsOptions = {
  db: PrismaClientType;
  secret?: Record<string, string>;
} & RetrieveDocumentsSchema;

export async function retrieveDocuments(
  input: string,
  options: RetrieveDocumentsOptions,
  trace?: LangfuseTraceClient,
): Promise<DocumentChunkType[]> {
  const span = trace?.span({
    name: "3-retrieve-documents",
    input: {
      input,
    },
  });

  const {
    db,
    orgId,
    openDocumentId,
    worksheet,
    tagIds,
    fundIds,
    lastChunkIds,
    limit,
    secret,
  } = options;

  // console.log("tagIds", tagIds);
  // console.log("fundIds", fundIds);
  // console.log("limit", limit);
  console.time("retrieveDocuments");

  try {
    const embeddings = new VoyageAIEmbedding(secret);

    // Get the embedding for the query
    const qEmb: number[] = await embeddings.embedQuery(input);

    const queryEmbedding = pgvector.toSql(qEmb);

    // Construct the metadata filter
    const metadataFilter = worksheet
      ? `AND metadata->>'worksheet' = '${worksheet}'`
      : "";

    const tagsFilter =
      tagIds.length > 0
        ? `AND EXISTS (
      SELECT 1 FROM "_DocumentToTag" dt
      WHERE dt."A" = d.id AND dt."B" IN (${tagIds.map((t) => `'${t.replace(/'/g, "")}'`).join(",")})
    )`
        : "";

    const fundsFilter =
      fundIds.length > 0
        ? `AND EXISTS (
          SELECT 1 FROM "_DocumentToFund" df
          WHERE df."A" = d.id AND df."B" IN (${fundIds.map((f) => `'${f.replace(/'/g, "")}'`).join(",")})
        )`
        : "";

    const lastChunkFilter = lastChunkIds?.length
      ? `AND dc.id NOT IN (${lastChunkIds.map((f) => `'${f.replace(/'/g, "")}'`).join(",")})`
      : "";

    // TODO: convert al those Prisma.raw to TypedSql
    // Custom SQL query with JOIN and vector similarity search


    let resultJumbo: any[] = [];
    let resultNormal: any[] = [];

    if (!process.env.CHUNKING_MODE) {
      process.env.CHUNKING_MODE = "JUMBO_AND_NORMAL";
    }

    if (
      process.env.CHUNKING_MODE === "JUMBO_ONLY" ||
      process.env.CHUNKING_MODE === "JUMBO_AND_NORMAL"
    ) {
      resultJumbo = await db.$queryRaw`
        SELECT dc.id,
               dc.content,
               dc.metadata,
               dc."documentId",
               dc."chunkType",
               dc.vector <=> ${queryEmbedding}::vector AS similarity, d.name AS "fileName",

               -- All related tags
          (SELECT json_agg(json_build_object('name', t.name, 'summary', t.summary))
          FROM "_DocumentToTag" dt_all
          JOIN "Tag" t ON t.id = dt_all."B"
          WHERE dt_all."A" = d.id) AS tags,

               -- All related funds
          (SELECT json_agg(json_build_object('name', f.name, 'description', f.description))
          FROM "_DocumentToFund" df_all
          JOIN "Fund" f ON f.id = df_all."B"
          WHERE df_all."A" = d.id) AS funds

        FROM "DocumentChunk" dc
          JOIN "Document" d
        ON d.id = dc."documentId"
          LEFT JOIN "_DocumentToTag" dt_filter ON dt_filter."A" = d.id
          LEFT JOIN "_DocumentToFund" df_filter ON df_filter."A" = d.id
        WHERE dc."orgId" = ${orgId}
          AND dc."documentId" != ${openDocumentId ?? ""}
          AND dc."chunkType" = 'JUMBO' 
            ${Prisma.raw(tagIds.length > 0 ? `AND dt_filter."B" IN (${tagIds.map((t) => `'${t.replace(/'/g, "")}'`).join(",")})` : "")} 
            ${Prisma.raw(fundIds.length > 0 ? `AND df_filter."B" IN (${fundIds.map((f) => `'${f.replace(/'/g, "")}'`).join(",")})` : "")} 
            ${Prisma.raw(metadataFilter)} 
            ${Prisma.raw(lastChunkFilter)}
        ORDER BY similarity
          LIMIT ${limit}
      `;
    }

    if (
      process.env.CHUNKING_MODE === "NORMAL_ONLY" ||
      process.env.CHUNKING_MODE === "JUMBO_AND_NORMAL"
    ) {
      resultNormal = await db.$queryRaw`
        SELECT dc.id,
               dc.content,
               dc.metadata,
               dc."documentId",
               dc."chunkType",
               dc.vector <=> ${queryEmbedding}::vector AS similarity, d.name AS "fileName",

               -- All related tags
          (SELECT json_agg(json_build_object('name', t.name, 'summary', t.summary))
          FROM "_DocumentToTag" dt_all
          JOIN "Tag" t ON t.id = dt_all."B"
          WHERE dt_all."A" = d.id) AS tags,

               -- All related funds
          (SELECT json_agg(json_build_object('name', f.name, 'description', f.description))
          FROM "_DocumentToFund" df_all
          JOIN "Fund" f ON f.id = df_all."B"
          WHERE df_all."A" = d.id) AS funds

        FROM "DocumentChunk" dc
          JOIN "Document" d
        ON d.id = dc."documentId"
          LEFT JOIN "_DocumentToTag" dt_filter ON dt_filter."A" = d.id
          LEFT JOIN "_DocumentToFund" df_filter ON df_filter."A" = d.id
        WHERE dc."orgId" = ${orgId}
          AND dc."documentId" != ${openDocumentId ?? ""}
          AND dc."chunkType" = 'NORMAL' 
            ${Prisma.raw(tagIds.length > 0 ? `AND dt_filter."B" IN (${tagIds.map((t) => `'${t.replace(/'/g, "")}'`).join(",")})` : "")} 
            ${Prisma.raw(fundIds.length > 0 ? `AND df_filter."B" IN (${fundIds.map((f) => `'${f.replace(/'/g, "")}'`).join(",")})` : "")} 
            ${Prisma.raw(metadataFilter)}
            ${Prisma.raw(lastChunkFilter)}
        ORDER BY similarity
          LIMIT ${limit}
      `;
    }


    const result: any[] = [...resultJumbo, ...resultNormal];

    console.timeEnd(
      `retrieveDocuments: returned ${result.length} documents [mode is: ${process.env.CHUNKING_MODE}]`,
    );

    // Convert the raw results to LangChain Document format
    const docs = result.map((row) => ({
      pageContent: row.content as string,
      metadata: {
        languages: ["eng"],
        filetype: row.metadata.filetype,
        id: row.id,
        documentId: row.documentId,
        fileName: row.fileName,
        _distance: row.similarity,
        tags: row.tags,
        funds: row.funds,
      } as DocumentChunkMetadataType,
    }));

    span?.update({
      output: {
        count: docs.length,
        documents: docs.map((d) => ({
          id: d.metadata.id,
          name: d.metadata.fileName,
          distance: d.metadata._distance,
        })),
      },
    });

    console.log(`Retrieved ${docs.length} documents`);

    // TODO DEBUG check for data
    console.log("R-stage-3", " --- DEBUG start (docs) ---");
    docs.forEach((doc: any) => {
      if (
        doc.pageContent.includes("Fund I - ") &&
        doc.pageContent.includes("Net MOIC") &&
        (doc.pageContent.includes("Q2 2023") ||
          doc.pageContent.includes("Q3 2024"))
      ) {
        console.log("R-stage-3", doc.pageContent);
      }
    });
    console.log("R-stage-3", " --- DEBUG end (docs) ---");

    return docs;
  } catch (error) {
    span?.update({ level: "ERROR", statusMessage: (error as Error).message });
    console.error("Error retrieving documents:", error);
    throw error;
  } finally {
    span?.end();
  }
}
