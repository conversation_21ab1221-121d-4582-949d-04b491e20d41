import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RAGResponseWithCitations } from "../types";
import { QuestionStatusType } from "@prisma/client";
import { GetPromptsForSheetType } from "~/server/api/routers/sheet";
import { DDQuestionWithFeedback } from "~/server/api/managers/questionManager";

export interface DDQPrompt {
  id: string;
  text: string;
  address: string;
}

interface DocumentDDQState {
  currentPageNew: number;
  currentPageAnswered: number;
  selectedQuestionText: string | null;
  selectedQuestionId: string | null;
  selectedQuestionObject: DDQuestionWithFeedback | null;
  responseLibraryResponses: Record<string, DocumentDDQResponse>; // key is question id
  generatedResponses: Record<string, DocumentDDQResponse>; // key is question id
  questionIdToText: Record<string, string>; // key is question id
  selectedTagIds: string[];
  selectedFundIds: string[];
  searchDebounced: string;
  searchInput: string;
  questionStatusFilter: QuestionStatusType;
  selectedQuestionsDic: Record<string, boolean>; // Add this line
  isAllSelected: boolean; // Add this line
  totalSelected: number; // Add this line
}

interface DocumentDDQResponse {
  responseMarkup: string;
  isLoading: boolean;
}

const initialState: DocumentDDQState = {
  currentPageNew: 1,
  currentPageAnswered: 1,
  selectedQuestionText: null,
  selectedQuestionId: null,
  selectedQuestionObject: null,
  responseLibraryResponses: {},
  generatedResponses: {},
  questionIdToText: {},
  selectedTagIds: [],
  selectedFundIds: [],
  searchDebounced: "",
  searchInput: "",
  selectedQuestionsDic: {},
  isAllSelected: false,
  totalSelected: 0,
  questionStatusFilter: QuestionStatusType.NEW,
};

const documentDDQSlice = createSlice({
  name: "documentDDQSlice",
  initialState,
  reducers: {
    setCurrentPageNew: (state, action: PayloadAction<number>) => {
      state.currentPageNew = action.payload;
    },
    setCurrentPageAnswered: (state, action: PayloadAction<number>) => {
      state.currentPageAnswered = action.payload;
    },
    setSelectedTagIds: (state, action: PayloadAction<string[]>) => {
      state.selectedTagIds = action.payload;
    },
    setSelectedFundIds: (state, action: PayloadAction<string[]>) => {
      state.selectedFundIds = action.payload;
    },
    setSearchDebounced: (state, action: PayloadAction<string>) => {
      state.searchDebounced = action.payload;
    },
    setSearchInput: (state, action: PayloadAction<string>) => {
      state.searchInput = action.payload;
    },
    setSelectedQuestionText: (state, action: PayloadAction<string>) => {
      state.selectedQuestionText = action.payload;
    },
    setSelectedQuestionId: (state, action: PayloadAction<string>) => {
      state.selectedQuestionId = action.payload;
    },
    setResponseLibraryText: (
      state,
      action: PayloadAction<{ id: string; response: DocumentDDQResponse }>,
    ) => {
      state.responseLibraryResponses[action.payload.id] =
        action.payload.response;
    },
    setGeneratedResponseText: (
      state,
      action: PayloadAction<{ id: string; response: DocumentDDQResponse }>,
    ) => {
      state.generatedResponses[action.payload.id] = action.payload.response;
    },
    setQuestionIdToText: (
      state,
      action: PayloadAction<{ id: string; text: string }>,
    ) => {
      state.questionIdToText[action.payload.id] = action.payload.text;
    },
    setQuestionIdToTextBatch: (
      state,
      action: PayloadAction<{ id: string; text: string }[]>,
    ) => {
      action.payload.forEach(({ id, text }) => {
        state.questionIdToText[id] = text;
      });
    },
    setSelectedQuestions: (
      state,
      action: PayloadAction<Record<string, boolean>>,
    ) => {
      state.selectedQuestionsDic = action.payload;
      state.totalSelected = Object.values(action.payload).filter(
        Boolean,
      ).length;
    },
    setIsAllSelected: (state, action: PayloadAction<boolean>) => {
      state.isAllSelected = action.payload;
    },
    setQuestionStatusFilter: (
      state,
      action: PayloadAction<QuestionStatusType>,
    ) => {
      state.questionStatusFilter = action.payload;
    },
    setSelectedQuestionObject: (
      state,
      action: PayloadAction<DDQuestionWithFeedback>,
    ) => {
      state.selectedQuestionObject = structuredClone(action.payload);
    },
  },
});

export const normalizePrompts = (
  prompts: GetPromptsForSheetType[],
): DDQPrompt[] => {
  return prompts.map((prompt) => ({
    id: prompt.id,
    text: prompt.prompt ?? "",
    address: prompt.address,
  }));
};

export const normalizeAnswer = (
  answer: RAGResponseWithCitations,
): RAGResponseWithCitations => {
  return {
    answer: answer.answer,
    citations: [...(answer.citations ?? [])],
  };
};

export const {
  setCurrentPageNew,
  setCurrentPageAnswered,
  setSelectedQuestionText,
  setSelectedQuestionId,
  setResponseLibraryText,
  setGeneratedResponseText,
  setQuestionIdToText,
  setQuestionIdToTextBatch,
  setSelectedTagIds,
  setSelectedFundIds,
  setSearchDebounced,
  setSearchInput,
  setSelectedQuestions,
  setIsAllSelected,
  setQuestionStatusFilter,
  setSelectedQuestionObject,
} = documentDDQSlice.actions;

export default documentDDQSlice.reducer;
