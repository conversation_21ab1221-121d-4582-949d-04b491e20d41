"use client";

import React from "react";
import { Provider } from "react-redux";
import store from "../../../src/lib/store";
import FullHeightContainer from "../components/virgil/FullHeightContainer";
import ExcelProvider from "../components/virgil/ExcelProvider";
import MSOfficeAddInContainer from "../components/virgil/OfficeAddIn/OfficeAddInContainer";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Provider store={store}>
      <FullHeightContainer>
        <MSOfficeAddInContainer>
          <ExcelProvider>{children}</ExcelProvider>
        </MSOfficeAddInContainer>
      </FullHeightContainer>
    </Provider>
  );
}
