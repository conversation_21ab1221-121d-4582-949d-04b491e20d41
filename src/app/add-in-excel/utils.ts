import { UnknownAction } from "@reduxjs/toolkit";
import { Dispatch } from "react";
import {
  setActiveSheet,
  setCellEdit,
  setCellValue,
  setDocumentUrl,
  setRangeAddress,
  setHostType,
} from "../../lib/features/documentSlice";
import { useDispatch } from "react-redux";

type CellEdit = Excel.ChangedEventDetail & {
  range: string;
};

const getDestinationRange = async (): Promise<Excel.Range | null> => {
  try {
    return await Excel.run(async (context) => {
      const range = context.workbook.getSelectedRange();
      await context.sync();

      const rangePlusOneToTheRight = range.getOffsetRange(0, 1);

      const address = rangePlusOneToTheRight.load("address");
      await context.sync();

      return rangePlusOneToTheRight;
    });
  } catch (error) {
    console.error(error);
    return null;
  }
};

async function getActiveSheet() {
  try {
    return await Excel.run(async (context) => {
      const worksheet = context.workbook.worksheets.getActiveWorksheet();
      worksheet.load("name");
      await context.sync();

      return worksheet.name;
    });
  } catch (error) {
    console.error("Error getting active sheet", error);
    return null;
  }
}

async function selectRange({ address }: { address: string }) {
  try {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getActiveWorksheet();
      const range = sheet.getRange(address);

      range.select();

      await context.sync();
    });
  } catch (error) {
    console.error(error);
  }
}

async function insertValue({
  documentId,
  text,
  rangeAddress,
}: {
  documentId: string;
  text: string;
  rangeAddress: string | null;
}) {
  console.log("insertValue", { documentId, text, rangeAddress });

  // TODO:
  // 1. Insert new record using edit.mutate() with citations in Json payload
  // 2. Insert value into cell
  // 3. Modify onChanged() callback to avoid duplidate edits

  try {
    await Excel.run(async (context) => {
      console.log("edit success");

      const range = context.workbook.getSelectedRange();
      const cellValue = range.load("values");
      await context.sync();

      console.log(cellValue.values);

      const destRange = rangeAddress
        ? context.workbook?.worksheets
            ?.getActiveWorksheet()
            ?.getRange(rangeAddress)
        : range.getOffsetRange(0, 1);

      const address = destRange?.load("address");
      await context.sync();

      console.log(
        `Inserting ${text} into ${address?.address.split("!").at(1)}`,
      );

      destRange.values = [[text]];

      // sync the context to run the previous API call, and return.
      return context.sync();
    });
  } catch (error) {
    console.error(error);
  }
}

async function registerHostType(
  hostType: Office.HostType,
  dispatch: Dispatch<UnknownAction>,
) {
  dispatch(setHostType(hostType));
}

function cellSelectionHandlerFn(
  context: Excel.RequestContext,
  dispatch: Dispatch<UnknownAction>,
) {
  return async ({ address }: { address: string }) => {
    const range = address;
    const cellValue = context?.workbook?.worksheets
      ?.getActiveWorksheet()
      ?.getRange(range)
      .load("values");
    await context.sync();
    dispatch(setCellValue((cellValue?.values?.at(0)?.at(0) as string) ?? ""));
    dispatch(setRangeAddress(range));
  };
}

async function registerCellSelectionHandler(
  hostType: Office.HostType,
  dispatch: Dispatch<UnknownAction>,
) {
  if (hostType === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      context.workbook.worksheets.onSingleClicked.add(
        cellSelectionHandlerFn(context, dispatch),
      );
      context.workbook.worksheets.onSelectionChanged.add(
        cellSelectionHandlerFn(context, dispatch),
      );

      return context.sync();
    });
  }
}

async function registerCellEditHandler(
  info: Office.HostType | null,
  dispatch: Dispatch<UnknownAction>,
) {
  if (info === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      context.workbook.worksheets.onChanged.add(async (event) => {
        console.log("onChanged event", event);

        dispatch(
          setCellEdit({
            ...event.details,
            range: event.address,
          }),
        );
      });

      return context.sync();
    });
  }
}

// Called when the document is opened and retrieves the full URL
// We can then use this URL to create a unique database record
async function registerFileOpenHandler(
  info: Office.HostType | null,
  dispatch: Dispatch<UnknownAction>,
) {
  if (info === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      dispatch(setDocumentUrl(`file://${Office.context.document.url}`));
      return context.sync();
    });
  }
}

async function registerSheetChangeHandler(
  info: Office.HostType | null,
  dispatch: Dispatch<UnknownAction>,
) {
  if (info === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      context.workbook.worksheets.onActivated.add(async (event) => {
        console.log("onActivated event", event);

        const worksheet = context.workbook.worksheets.getItem(
          event.worksheetId,
        );

        worksheet.load("name");
        await context.sync();

        dispatch(setActiveSheet(worksheet?.name ?? ""));

        console.log("worksheet", worksheet?.name);
      });

      const activeSheet = context.workbook.worksheets.getActiveWorksheet();
      activeSheet.load("name");
      await context.sync();
      dispatch(setActiveSheet(activeSheet?.name ?? ""));
      console.log("worksheet", activeSheet?.name);

      return context.sync();
    });
  }
}

export {
  getDestinationRange,
  getActiveSheet,
  selectRange,
  insertValue,
  registerCellSelectionHandler,
  registerCellEditHandler,
  registerFileOpenHandler,
  registerSheetChangeHandler,
  registerHostType,
};
