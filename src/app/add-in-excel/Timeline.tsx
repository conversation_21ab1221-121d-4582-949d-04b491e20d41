import Timeline from "@mui/lab/Timeline/Timeline";
import TimelineContent from "@mui/lab/TimelineContent";
import { timelineItemClasses } from "@mui/lab/TimelineItem";
import { Typography } from "@mui/material";

import TimelineConnector from "@mui/lab/TimelineConnector";
import TimelineDot from "@mui/lab/TimelineDot";
import TimelineItem from "@mui/lab/TimelineItem";
import TimelineSeparator from "@mui/lab/TimelineSeparator";
import { Stack } from "@mui/material";
import { Edit } from "~/lib/features/documentSlice";

export const DocumentEditTimelineItem = ({
  userName,
  timestamp,
  edit,
}: {
  userName: string;
  timestamp: string;
  edit: Edit;
}) => {
  return (
    <TimelineItem>
      <TimelineSeparator>
        <TimelineConnector />
        <TimelineDot />
        <TimelineConnector />
      </TimelineSeparator>
      <TimelineContent sx={{ width: "100%", py: "12px", px: 2 }}>
        <Stack width="100%">
          <Typography
            variant="subtitle1"
            component="span"
            style={{ width: "100%" }}
          >
            {edit.content.editType}
          </Typography>
          <Typography style={{ width: "100%" }} variant="caption">
            By {userName} {timestamp}
          </Typography>
        </Stack>
      </TimelineContent>
    </TimelineItem>
  );
};

export const DocumentEditTimeline = ({
  edits,
}: {
  edits: Edit[];
}) => {
  return (
    <Timeline
      sx={{
        [`& .${timelineItemClasses.root}:before`]: {
          flex: 0,
          padding: 0,
        },
      }}
    >
      {edits.map((edit) => (
        <DocumentEditTimelineItem
          key={edit.id}
          userName={edit.createdBy.name ?? ""}
          timestamp={edit.createdAt.toLocaleString()}
          edit={edit}
        />
      ))}
    </Timeline>
  );
};
