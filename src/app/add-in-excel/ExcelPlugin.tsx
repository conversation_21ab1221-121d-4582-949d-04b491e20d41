"use client";

import React from "react";
import { Logo } from "../../components/logo";
import Welcome from "./Welcome";
import { useSelector } from "react-redux";
import { RootState } from "~/lib/store";

export default function ExcelPlugin() {
  const { rangeAddress } = useSelector((state: RootState) => state.document);
  return (
    <>
      <Logo width={100} height={35} />
      {rangeAddress ? <Welcome /> : <></>}
    </>
  );
}
