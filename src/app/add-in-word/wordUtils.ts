import { type UnknownAction } from "@reduxjs/toolkit";
import { type Dispatch } from "react";
import { setDocumentUrl, setHostType } from "../../lib/features/documentSlice";
import { convertMarkdownToHtml } from "../components/virgil/OfficeAddIn/markdownUtils/markdownUtils";
import { FlatNode, parseHtmlToTree, flattenHtmlTree } from "./transformerUtils";
import { eventBus } from "../../lib/eventBus";
import { FullOptions, Searcher } from "fast-fuzzy";
import { debounce } from "@mui/material";

const STRING_LOOKUP_SIZE = 254; // search limit is 255 characters in MS Word
const WORD_SOFT_BREAK = String.fromCharCode(11);
let searchParagraphs: Searcher<
  { text: string; id: string },
  FullOptions<{ text: string; id: string }>
> = new Searcher<
  { text: string; id: string },
  FullOptions<{ text: string; id: string }>
>([]);
async function registerTextSelectionHandler(
  hostType: Office.HostType,
  onSelectionChange?: (data: { text: string; paragraph: string }) => void,
) {
  if (hostType === Office.HostType.Word) {
    // Add event handler for selection changes
    Office.context.document.addHandlerAsync(
      Office.EventType.DocumentSelectionChanged,
      async () => {
        // Get the current selection
        Office.context.document.getSelectedDataAsync(
          Office.CoercionType.Text,
          async (result: Office.AsyncResult<string>) => {
            if (result.status === Office.AsyncResultStatus.Succeeded) {
              const selectedText = result.value;

              await Word.run(async (context) => {
                // Get the current selection
                const range = context.document.getSelection();

                // Get the paragraph containing the selection
                const paragraph = range.paragraphs.getFirst();

                // Load the text property
                paragraph.load("text");
                await context.sync();

                // Call the callback with the selection data
                if (onSelectionChange) {
                  onSelectionChange({
                    text: selectedText,
                    paragraph: paragraph.text,
                  });
                }
              });
            }
          },
        );
      },
    );
  }
}
const debouncedBuildSearchParagraphs = debounce(buildSearchParagraphs, 1000);

async function buildSearchParagraphs() {
  await Word.run(async (context) => {
    const paragraphs = context.document.body.paragraphs;
    paragraphs.load(["text", "uniqueLocalId"]);
    await context.sync();

    const p = paragraphs.items
      .map((paragraph: Word.Paragraph) => {
        return {
          text: paragraph.text,
          id: paragraph.uniqueLocalId,
        };
      })
      .filter((p) => p.text.trim().length > 0);
    searchParagraphs = new Searcher(p, { keySelector: (obj) => obj.text });
  });
}

async function registerBodySearchHandler(hostType: Office.HostType) {
  if (hostType === Office.HostType.Word) {
    await buildSearchParagraphs();
  }
}

async function registerFileOpenHandler(
  info: Office.HostType | null,
  dispatch: Dispatch<UnknownAction>,
) {
  if (info === Office.HostType.Word) {
    await Word.run(async (context) => {
      dispatch(setDocumentUrl(`${Office.context.document.url}`));
      return context.sync();
    });
  }
}

// https://learn.microsoft.com/en-us/office/dev/add-ins/develop/dialog-best-practices
function openChat({
  domain,
  chatHeight,
  chatWidth,
}: {
  domain: string;
  chatHeight: number;
  chatWidth: number;
}) {
  try {
    // close the dialog if it is already open (before opening a new one)
    if (window.msAddInDialog) {
      window.msAddInDialog.close();
      window.msAddInDialog = undefined;
    }
  } catch (error) {
    console.error("Error closing dialog", error);
  }
  Office.context.ui.displayDialogAsync(
    `${domain}/add-in-chat`,
    {
      height: chatHeight,
      width: chatWidth,
    },
    (result) => {
      if (result.status === Office.AsyncResultStatus.Succeeded) {
        window.msAddInDialog = result.value;
      }
      if (result.status === Office.AsyncResultStatus.Failed) {
        if (result.error.code === 12007) {
          openChat({
            domain,
            chatHeight,
            chatWidth,
          }); // Recursive call.
        } else {
          console.error("Error opening dialog", result.error);
        }
      }
    },
  );
}

export function openDocumentPreview({
  domain,
  previewHeight,
  previewWidth,
  documentId,
}: {
  domain: string;
  previewHeight: number;
  previewWidth: number;
  documentId: string;
}) {
  console.log("domain", domain);
  console.log("previewHeight", previewHeight);
  console.log("previewWidth", previewWidth);
  console.log("documentId", documentId);

  try {
    // close the dialog if it is already open (before opening a new one)
    if (window.msAddInDialog) {
      window.msAddInDialog.close();
      window.msAddInDialog = undefined;
    }
  } catch (error) {
    console.error("Error closing dialog", error);
  }
  Office.context.ui.displayDialogAsync(
    `${domain}/add-in-document-preview?documentId=${documentId}`,
    {
      height: previewHeight,
      width: previewWidth,
    },
    (result) => {
      if (result.status === Office.AsyncResultStatus.Succeeded) {
        window.msAddInDialog = result.value;
      }
      if (result.status === Office.AsyncResultStatus.Failed) {
        if (result.error.code === 12007) {
          openDocumentPreview({
            domain,
            previewHeight,
            previewWidth,
            documentId,
          }); // Recursive call.
        } else {
          console.error("Error opening dialog", result.error);
        }
      }
    },
  );
}

async function highlightTextInDocument(
  text: string,
  highlightColor = "#FFFF00",
) {
  await Word.run(async (context) => {
    const searchResults = context.document.body.search(prepareSearchStr(text), {
      matchWildcards: true,
      ignoreSpace: true,
      matchCase: false,
      matchWholeWord: false,
      ignorePunct: true,
    });

    searchResults.load(["font", "range"]);
    await context.sync();

    // Highlight the found text
    searchResults.items.forEach((item) => {
      item.font.highlightColor = highlightColor;
    });

    await context.sync();
    return searchResults;
  });
}

// Take the first half of the string and the last half of the string and join them with a wildcard
export function prepareSearchStr(str: string) {
  if (str.length <= STRING_LOOKUP_SIZE) {
    return str;
  }

  const firstHalf = str
    .substring(0, Math.floor(str.length / 2))
    .substring(0, STRING_LOOKUP_SIZE / 2);
  const secondHalf = str
    .substring(str.length / 2)
    .substring(str.length / 2 - STRING_LOOKUP_SIZE / 2);

  return `${firstHalf}*${secondHalf}`;
}

function findRangeSync(
  text: string,
  context: Word.RequestContext,
  position: "Start" | "End" = "Start",
): Word.RangeCollection {
  const searchResults = context.document.body.search(prepareSearchStr(text), {
    ignoreSpace: true,
    matchWildcards: true,
    matchCase: false,
    matchWholeWord: false,
    ignorePunct: true,
  });

  searchResults.load(["range", "text", "font"]);
  return searchResults;
}

// Lookup substrings of 254 characters and join them together
async function findRange(
  text: string,
  context: Word.RequestContext,
  position: "Start" | "End" = "Start",
): Promise<Word.Range | undefined> {
  // const searchResults = findRangeSync(text, context, position);

  // use fuzzy search to find the text
  const searchResults = searchParagraphs.search(text);
  if (searchResults.length > 0 && searchResults[0]?.id) {
    const paragraphId = searchResults[0]?.id;
    const paragraph: Word.Paragraph =
      context.document.getParagraphByUniqueLocalId(paragraphId);
    paragraph.load();
    await context.sync();
    return paragraph.getRange();
  }
}

async function scrollToText(
  text: string,
  mode: "Select" | "Start" | "End" = "Select",
) {
  await Word.run(async (context) => {
    const range = await findRange(text, context, "Start");
    if (!range) {
      // throw new Error("Text not found");
      return;
    }
    range.select();
    await context.sync();
  });
}

async function scrollToTextFuzzy(text: string) {
  await Word.run(async (context) => {
    const searchResults = searchParagraphs.search(text);
    if (searchResults.length > 0 && searchResults[0]?.id) {
      const paragraphId = searchResults[0]?.id;
      const paragraph: Word.Paragraph =
        context.document.getParagraphByUniqueLocalId(paragraphId);
      paragraph.load();
      paragraph.select();
      await context.sync();
      console.log("searchParagraphs: ", searchParagraphs);
      return;
    }
  });
}

function insertWordNode(
  range: Word.Range,
  node: FlatNode,
  baseFontSize: number,
  color: string,
  bold: boolean,
  italic: boolean,
  underline: boolean,
) {
  let lineToAdd = "";
  let fontSize = baseFontSize;
  let blockRange;
  switch (node.nodeType) {
    case "h1":
      fontSize = baseFontSize + 3;
      bold = true;
      lineToAdd = `${node.text}${WORD_SOFT_BREAK}`;
      blockRange = range.insertText(lineToAdd, "End");
      break;
    case "h2":
      fontSize = baseFontSize + 2;
      bold = true;
      lineToAdd = `${node.text}${WORD_SOFT_BREAK}`;
      blockRange = range.insertText(lineToAdd, "End");
      break;
    case "h3":
      fontSize = baseFontSize + 1;
      bold = true;
      lineToAdd = `${node.text}${WORD_SOFT_BREAK}`;
      blockRange = range.insertText(lineToAdd, "End");
      break;
    case "h4":
      bold = true;
      lineToAdd = `${node.text}${WORD_SOFT_BREAK}`;
      blockRange = range.insertText(lineToAdd, "End");
      break;
    case "h5":
      bold = true;
      lineToAdd = `${node.text}${WORD_SOFT_BREAK}`;
      blockRange = range.insertText(lineToAdd, "End");
      break;
    case "h6":
      bold = true;
      lineToAdd = `${node.text}${WORD_SOFT_BREAK}`;
      blockRange = range.insertText(lineToAdd, "End");
      break;
    case "bold":
      bold = true;
      lineToAdd = `${node.text}`;
      blockRange = range.insertText(lineToAdd, "End");
      break;
    case "link":
      lineToAdd = `${node.text}`;
      blockRange = range.insertText(lineToAdd, "End");
      blockRange.font.color = "blue";
      blockRange.font.underline = "Single";
      break;
    case "newLine":
      lineToAdd = `${WORD_SOFT_BREAK}`;
      blockRange = range.insertText(lineToAdd, "End");
      break;
    case "table":
      blockRange = range.insertTable(
        node.tableData?.length ?? 0,
        node.tableData?.[0]?.length ?? 0,
        Word.InsertLocation.after,
        node.tableData,
      );
      blockRange.styleBuiltIn = "Normal";
      break;
    default:
      fontSize = baseFontSize;
      lineToAdd = `${node.text}`;
      blockRange = range.insertText(lineToAdd, "End");
  }
  blockRange.font.size = fontSize;
  blockRange.font.bold = bold;
  blockRange.font.color = color;
  blockRange.font.italic = italic;
  blockRange.font.underline = underline ? "Single" : "None";
}

function structuredInsert(
  range: Word.Range,
  nodes: FlatNode[],
  color = "green",
  fontSize = 12,
  bold = false,
  italic = false,
  underline = false,
) {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];

    // skip last new line
    if (node?.nodeType === "newLine" && i === nodes.length - 1) {
      break;
    }
    if (node) {
      insertWordNode(range, node, fontSize, color, bold, italic, underline);
    }
  }
}

// Insert the text after the found text
async function findAndInsertAfter(
  text: string,
  insertText: string,
  color = "green",
  fontSize = 12,
  bold = false,
  italic = false,
  underline = false,
  insertAt: "AfterQuestion" | "AtCursor" = "AfterQuestion",
) {
  await Word.run(async (context) => {
    const range =
      insertAt === "AfterQuestion"
        ? await findRange(text, context, "End")
        : context.document.getSelection();
    await context.sync();

    if (range) {
      if (insertAt === "AfterQuestion") {
        range.insertText(WORD_SOFT_BREAK, "End");
      }

      const html = convertMarkdownToHtml(insertText);
      const tree = parseHtmlToTree(html);
      const nodes = flattenHtmlTree(tree);
      console.log("nodes", nodes);

      structuredInsert(range, nodes, color, fontSize, bold, italic, underline);
      await context.sync();
    } else {
      throw new Error("Text not found");
    }
  });
}

// Insert the text after the found text
async function findAndInsertAfterBatch(
  pairs: { text: string; insertText: string; id: string }[],
  color = "green",
): Promise<{ success: number; failed: number; insertedQuestionIds: string[] }> {
  return await Word.run(async (context) => {
    // search in batch
    const searches = pairs.map((pair) => {
      const range = findRangeSync(pair.text, context, "End");
      return {
        searchResults: range,
        id: pair.id,
        insertText: pair.insertText,
        text: pair.text,
      };
    });

    await context.sync();

    // get ranges
    const ranges: {
      range: Word.Range;
      id: string;
      insertText: string;
      text: string;
    }[] = searches
      .map((search) => {
        const range = search.searchResults.items[0]?.getRange();
        if (range) {
          return {
            range,
            id: search.id,
            insertText: search.insertText,
            text: search.text,
          };
        }
        return undefined;
      })
      .filter((r) => !!r);

    // load font for each range
    ranges.forEach((r) => {
      r.range.load("font");
    });

    await context.sync();

    // insert text
    ranges.forEach((r) => {
      const html = convertMarkdownToHtml(r.insertText);
      const tree = parseHtmlToTree(html);
      const nodes = flattenHtmlTree(tree);
      structuredInsert(r.range, nodes, color);
    });

    // sync
    await context.sync();

    return {
      success: ranges.length,
      failed: pairs.length - ranges.length,
      insertedQuestionIds: ranges.map((r) => r.id),
    };
  });
}

async function registerHostType(
  hostType: Office.HostType,
  dispatch: Dispatch<UnknownAction>,
) {
  dispatch(setHostType(hostType));
}

async function registerOnParagraphChanged(hostType: Office.HostType) {
  if (hostType === Office.HostType.Word) {
    // Registers the onParagraphChanged event handler on the document.
    await Word.run(async (context) => {
      context.document.onParagraphChanged.add(debouncedBuildSearchParagraphs);
      await context.sync();
    });
  }
}

export function registerShortcuts(hostType: Office.HostType) {
  try {
    Office.actions.associate("InsertAnswer", () => {
      console.log("InsertAnswer callback triggered");
      eventBus.emit("insertAnswer");
    });
    console.log("Successfully registered InsertAnswer shortcut");
  } catch (error) {
    console.error("Error registering shortcuts:", error);
  }
}

export {
  registerOnParagraphChanged,
  registerFileOpenHandler,
  registerTextSelectionHandler,
  highlightTextInDocument,
  findAndInsertAfter,
  findAndInsertAfterBatch,
  scrollToText,
  openChat,
  registerHostType,
  registerBodySearchHandler,
  scrollToTextFuzzy,
};
