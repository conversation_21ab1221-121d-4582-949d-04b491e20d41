"use client";

import React from "react";
import { Provider } from "react-redux";
import store from "../../../src/lib/store";
import FullHeightContainer from "../components/virgil/FullHeightContainer";
import WordProvider from "../components/virgil/OfficeAddIn/WordProvider";
import MSOfficeAddInContainer from "../components/virgil/OfficeAddIn/OfficeAddInContainer";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Provider store={store}>
      <FullHeightContainer>
        <MSOfficeAddInContainer>
          <WordProvider>{children}</WordProvider>
        </MSOfficeAddInContainer>
      </FullHeightContainer>
    </Provider>
  );
}
