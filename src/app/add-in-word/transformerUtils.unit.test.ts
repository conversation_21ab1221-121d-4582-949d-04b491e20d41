import { parseHtmlToTree, parseHtmlToFlatNodes } from "./transformerUtils";

describe("parseHtmlToTree", () => {
  it("should ignore div and section tags", () => {
    const html = "<div><p>Hello</p></div>";
    const tree = parseHtmlToTree(html);
    expect(tree).toEqual([{ type: "p", text: "Hello" }]);
  });

  it("should not ignore text inside container", () => {
    const html = "<div><p>Hello</p> just text</div>";
    const tree = parseHtmlToTree(html);
    expect(tree).toEqual([
      {
        type: "container",
        children: [
          {
            type: "p",
            text: "Hello",
          },
          {
            type: "text",
            text: "just text",
          },
        ],
      },
    ]);
  });

  it("should handle lists", () => {
    const html = `
<div class="wrapper">
  <h1>Title</h1>
  <div class="content">
    <p>This is a <strong>bold</strong> paragraph.</p>
    <section>
      <ol>
        <li>First item<bold>bold</bold></li>
        <li>Second item
          <div class="nested">
            <ul>
              <li>Nested bullet</li>
            </ul>
          </div>
        </li>
      </ol>
    </section>
  </div>
  <p>Final paragraph</p>
</div>
    `;
    const tree = parseHtmlToTree(html);
    expect(tree).toEqual([
      {
        type: "container",
        children: [
          {
            type: "h1",
            text: "Title",
          },
          {
            type: "container",
            children: [
              {
                type: "p",
                children: [
                  {
                    type: "text",
                    text: "This is a",
                  },
                  {
                    type: "strong",
                    parentType: "p",
                    text: "bold",
                  },
                  {
                    type: "text",
                    text: "paragraph.",
                  },
                ],
              },
              {
                type: "ol",
                children: [
                  {
                    type: "li",
                    parentType: "ol",
                    text: "First item",
                  },
                  {
                    type: "li",
                    parentType: "ol",
                    children: [
                      {
                        type: "text",
                        text: "Second item",
                      },
                      {
                        type: "ul",
                        parentType: "li",
                        children: [
                          {
                            type: "li",
                            parentType: "ul",
                            text: "Nested bullet",
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            type: "p",
            text: "Final paragraph",
          },
        ],
      },
    ]);
  });
});

describe("parseHtmlToFlatNodes", () => {
  it("should handle lists", () => {
    const html = `
<div class="wrapper">
  <h1>Title</h1>
  <div class="content">
    <p>This is a <strong>bold</strong> paragraph.</p>
    <section>
      <ol>
        <li>First item<bold>bold</bold></li>
        <li>Second item
          <div class="nested">
            <ul>
              <li>Nested bullet</li>
            </ul>
          </div>
        </li>
      </ol>
    </section>
  </div>
  <p>Final paragraph</p>
</div>
    `;
    const flatNodes = parseHtmlToFlatNodes(html);
    console.log(JSON.stringify(flatNodes, null, 2));
    expect(flatNodes).toEqual([
      {
        nodeType: "h1",
        text: "Title",
      },
      {
        nodeType: "newLine",
        text: "",
      },
      {
        nodeType: "text",
        text: "This is a",
      },
      {
        nodeType: "bold",
        text: "bold",
      },
      {
        nodeType: "text",
        text: "paragraph.",
      },
      {
        nodeType: "newLine",
        text: "",
      },
      {
        nodeType: "numberedListPrefix",
        text: "1. ",
      },
      {
        nodeType: "text",
        text: "First item",
      },
      {
        nodeType: "newLine",
        text: "",
      },
      {
        nodeType: "numberedListPrefix",
        text: "2. ",
      },
      {
        nodeType: "text",
        text: "Second item",
      },
      {
        nodeType: "newLine",
        text: "",
      },
      {
        nodeType: "bulletListPrefix",
        text: "  • ",
      },
      {
        nodeType: "text",
        text: "Nested bullet",
      },
      {
        nodeType: "newLine",
        text: "",
      },
      {
        nodeType: "p",
        text: "Final paragraph",
      },
      {
        nodeType: "newLine",
        text: "",
      },
    ]);
  });
});
