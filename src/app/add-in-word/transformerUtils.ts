export interface HtmlNode {
  type: string;
  text?: string;
  children?: HtmlNode[];
  parentType?: string;
  tableData?: string[][];
  href?: string;
}

export interface FlatNode {
  nodeType: string;
  text: string;
  tableData?: string[][];
  href?: string;
}

export function parseHtmlToTree(html: string): HtmlNode[] {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");

  const ALLOWED_ELEMENTS = new Set([
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "p",
    "ul",
    "ol",
    "li",
    "strong",
    "b",
    "em",
    "i",
    "br",
    "div",
    "section",
    "table",
    "tr",
    "td",
    "th",
    "a",
  ]);

  function traverseNode(node: Node, parentType?: string): HtmlNode | null {
    // Handle text nodes
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim();
      return text ? { type: "text", text } : null;
    }

    // Handle element nodes
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;
      const tagName = element.tagName.toLowerCase();

      // Skip if not in allowed elements
      if (!ALLOWED_ELEMENTS.has(tagName)) {
        return null;
      }

      // For div and section, just process their children
      if (tagName === "div" || tagName === "section") {
        const children: HtmlNode[] = [];
        element.childNodes.forEach((childNode) => {
          const childResult = traverseNode(childNode, parentType);
          if (childResult) {
            children.push(childResult);
          }
        });
        return children.length === 0
          ? null
          : children.length === 1
            ? (children[0] ?? null)
            : { type: "container", children };
      }

      const htmlNode: HtmlNode = { type: tagName, parentType };
      const children: HtmlNode[] = [];

      // Process child nodes
      element.childNodes.forEach((childNode) => {
        const childResult = traverseNode(childNode, tagName);
        if (childResult) {
          children.push(childResult);
        }
      });

      // Handle special cases
      if (tagName === "br") {
        return { type: "br", text: "\n" };
      }

      // Handle anchor tags
      if (tagName === "a") {
        htmlNode.href = (element as HTMLAnchorElement).href;
      }

      // Add children if they exist
      if (children.length > 0) {
        htmlNode.children = children;
      }

      // For text-only nodes, collapse children into text
      if (children.length === 1 && children[0]?.type === "text") {
        htmlNode.text = children[0]?.text;
        delete htmlNode.children;
      }

      // Add table handling
      if (tagName === "table") {
        const tableData: string[][] = [];
        element.querySelectorAll("tr").forEach((row) => {
          const rowData: string[] = [];
          row.querySelectorAll("td, th").forEach((cell) => {
            rowData.push(cell.textContent?.trim() || "");
          });
          if (rowData.length > 0) {
            tableData.push(rowData);
          }
        });

        return tableData.length > 0 ? { type: "table", tableData } : null;
      }

      return htmlNode;
    }

    return null;
  }

  // Start traversal from body and filter out null results
  const nodes = Array.from(doc.body.childNodes)
    .map((node) => traverseNode(node))
    .filter((node): node is HtmlNode => node !== null);

  return nodes;
}

export function flattenHtmlTree(tree: HtmlNode[]): FlatNode[] {
  const flatNodes: FlatNode[] = [];

  function addNewLine() {
    flatNodes.push({ nodeType: "newLine", text: "" });
  }

  function processNode(node: HtmlNode, depth: number = 0, listIndex?: number) {
    const indent = "  ".repeat(depth);

    switch (node.type) {
      // Add container type handling
      case "container":
        node.children?.forEach((child) => {
          processNode(child, depth);
        });
        break;

      // Headers
      case "h1":
      case "h2":
      case "h3":
      case "h4":
      case "h5":
      case "h6":
        if (flatNodes.length > 0) addNewLine();
        flatNodes.push({ nodeType: node.type, text: node.text || "" });
        addNewLine();
        break;

      // Paragraphs
      case "p":
        if (flatNodes.length > 0) addNewLine();
        if (node.text) {
          flatNodes.push({ nodeType: "p", text: node.text });
        }
        if (node.children) {
          node.children.forEach((child) => processNode(child, depth));
        }
        addNewLine();
        break;

      // Lists
      case "ul":
      case "ol":
        if (
          flatNodes.length > 0 &&
          !flatNodes[flatNodes.length - 1]?.nodeType?.includes("List")
        ) {
          addNewLine();
        }
        node.children?.forEach((child, idx) => {
          processNode(child, depth, idx + 1);
        });
        if (!node.parentType) addNewLine();
        break;

      case "li":
        const isOrdered = node.parentType === "ol";
        flatNodes.push({
          nodeType: isOrdered ? "numberedListPrefix" : "bulletListPrefix",
          text: isOrdered ? `${indent}${listIndex}. ` : `${indent}• `,
        });

        if (node.text) {
          flatNodes.push({ nodeType: "text", text: node.text });
        }

        let hasNestedList = false;
        if (node.children) {
          node.children.forEach((child) => {
            if (child.type === "ul" || child.type === "ol") {
              hasNestedList = true;
              addNewLine(); // Add newline before nested list
              child.children?.forEach((listItem, idx) => {
                processNode(listItem, depth + 1, idx + 1);
              });
            } else {
              processNode(child, depth);
            }
          });
        }

        addNewLine();
        if (hasNestedList) {
          addNewLine(); // Add extra newline after items with nested elements
        }
        break;

      // Text formatting
      case "strong":
      case "b":
        flatNodes.push({ nodeType: "bold", text: node.text || "" });
        break;

      case "em":
      case "i":
        flatNodes.push({ nodeType: "italic", text: node.text || "" });
        break;

      case "br":
        addNewLine();
        break;

      case "text":
        if (node.text?.trim()) {
          flatNodes.push({ nodeType: "text", text: node.text });
        }
        break;

      case "table":
        if (flatNodes.length > 0) addNewLine();
        flatNodes.push({
          nodeType: "table",
          text: "", // Required by interface
          tableData: node.tableData,
        });
        addNewLine();
        break;

      // Add anchor tag handling
      case "a":
        if (node.text) {
          flatNodes.push({
            nodeType: "link",
            text: node.text,
            href: node.href,
          });
        }
        if (node.children) {
          node.children.forEach((child) => processNode(child, depth));
        }
        break;
    }
  }

  tree.forEach((node) => processNode(node));

  return flatNodes.filter((node, index, array) => {
    if (index === 0) return true;
    return !(
      node.nodeType === "newLine" && array[index - 1]?.nodeType === "newLine"
    );
  });
}

export function parseHtmlToFlatNodes(html: string): FlatNode[] {
  const tree = parseHtmlToTree(html);
  return flattenHtmlTree(tree);
}

// Test the functions
const html = `
<div class="wrapper">
  <h1>Title</h1>
  <div class="content">
    <p>This is a <strong>bold</strong> paragraph.</p>
    <section>
      <ol>
        <li>First item<bold>bold</bold></li>
        <li>Second item
          <div class="nested">
            <ul>
              <li>Nested bullet</li>
            </ul>
          </div>
        </li>
      </ol>
    </section>
  </div>
  <p>Final paragraph</p>
</div>
`;

export function test() {
  const tree = parseHtmlToTree(html);
  console.log("Tree structure:", JSON.stringify(tree, null, 2));

  const nodes = flattenHtmlTree(tree);
  console.log("Flattened nodes:", JSON.stringify(nodes, null, 2));
}
