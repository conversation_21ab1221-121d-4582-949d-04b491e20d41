"use client";

import { Box, Button, Typography } from "@mui/material";
import { useSearchParams } from "next/dist/client/components/navigation";
import { Iconify } from "~/components/iconify";
import { Logo } from "~/components/logo";

export default function Page() {
  const integration = useSearchParams().get("integration");

  const logo =
    integration === "egnyte"
      ? "/assets/images/logos/egnyte.svg"
      : "/assets/images/logos/office.svg";

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      height="100vh"
      gap={2}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        gap={2}
      >
        <Logo />
        {integration && (
          <>
            <Iconify icon="tabler:arrows-left-right" width={48} height={48} />
            <img src={logo} />
          </>
        )}
      </Box>
      <Typography variant="h4">Integration Error</Typography>
      <Typography variant="body1">
        There was an error during the integration process. Please try again.
      </Typography>
      <Button variant="contained" onClick={() => window.close()}>
        Close Window
      </Button>
    </Box>
  );
}
