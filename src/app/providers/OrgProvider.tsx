"use client";
import { useUser } from "@clerk/nextjs";
import * as Sen<PERSON> from "@sentry/nextjs";
import { createContext, useContext, useEffect, useState } from "react";
import { UserNotInOrg } from "~/sections/error/user-not-in-org";
import { api } from "~/trpc/react";
import Loading from "../loading";

type Props = {
  children: React.ReactNode;
};

type OrgContextType = {
  clerkOrgId: string | undefined;
  orgName: string | undefined;
  orgLogo: string | undefined;
};

const OrgContext = createContext<OrgContextType | undefined>(undefined);

export function useOrg() {
  const context = useContext(OrgContext);
  if (context === undefined) {
    throw new Error("useOrg must be used within an OrgProvider");
  }
  return context;
}

export function OrgProvider({ children }: Props) {
  const { isSignedIn, user, isLoaded: isUserLoaded } = useUser();
  const [orgName, setOrgName] = useState<string>("");
  const [orgLogo, setOrgLogo] = useState<string>("");

  const { data: org, isLoading: isOrgLoading } = api.organization.get.useQuery(
    {},
    {
      enabled: isSignedIn,
      retry: false,
    },
  );

  useEffect(() => {
    if (isOrgLoading) return;

    const orgClerkId = org?.clerkId;

    setOrgName(
      user?.organizationMemberships.find(
        (org) => org.organization.id === orgClerkId,
      )?.organization.name || "",
    );
    setOrgLogo(
      user?.organizationMemberships.find(
        (org) => org.organization.id === orgClerkId,
      )?.organization.imageUrl || "",
    );
  }, [isUserLoaded, isOrgLoading]);

  const { data: clerkOrgId, isLoading: isClerkOrgIdLoading } =
    api.organization.getClerkOrgId.useQuery(undefined, {
      enabled: isSignedIn,
      retry: false,
    });

  const isMember = user?.organizationMemberships.some(
    (org) => org.organization.id === clerkOrgId,
  );

  if (isOrgLoading || isClerkOrgIdLoading || !isUserLoaded) {
    return <Loading />;
  }

  if (!isMember && isSignedIn) {
    return <UserNotInOrg />;
  }

  Sentry.setUser({
    id: user?.id,
    email: user?.emailAddresses[0]?.emailAddress,
    username: user?.emailAddresses[0]?.emailAddress,
    organization: org?.name,
    organizationId: org?.id,
  });
  
  return (
    <OrgContext.Provider value={{ clerkOrgId, orgName, orgLogo }}>
      {children}
    </OrgContext.Provider>
  );
}
