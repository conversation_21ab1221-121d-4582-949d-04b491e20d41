import { z } from "zod";
import { retrieveDocuments, RetrieveDocumentsSchema } from "~/lib/rag-response/steps/3-retrieve-documents";
import { db } from "~/server/db";


export async function GET(req: Request) {
  return new Response(new Date().toISOString());
}

const RequestBody = z.object({
  query: z.string(),
  options: RetrieveDocumentsSchema
})

export async function POST(req: Request) {

  const text = await req.text();

  const jsonBody = JSON.parse(text);

  const body = await RequestBody.safeParseAsync(jsonBody)
  if (!body.data) {
    return new Response(JSON.stringify(body.error), { status: 400 })
  }
  const {query, options} = body.data;
  const a = await retrieveDocuments(query, {...options, db})
  return new Response(JSON.stringify(a), {
    status: 200,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
