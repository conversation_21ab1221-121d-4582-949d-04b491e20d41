import type { NextRequest } from "next/server";
import { z } from "zod";
import { env } from "~/env";
import { getDocumentContents } from "~/lib/fileUtils";
import { markDocumentStatusError } from "~/lib/vectorizer";
import { db as prisma } from "~/server/db";

const RequestBody = z.object({
  documentId: z.string(),
});

export async function GET(request: NextRequest) {
  const authHeader = request.headers.get("authorization");

  if (authHeader !== `Bearer ${env.CHUNKING_API_SECRET}`) {
    return new Response("Unauthorized", {
      status: 401,
    });
  }

  const org = await prisma.org.findFirst({
    where: {
      clerkId: env.CLERK_ORG_ID,
    },
  });

  if (!org) {
    return new Response("Org not found", {
      status: 404,
    });
  }

  const documentId = request.nextUrl.searchParams.get("documentId");

  if (!documentId) {
    return new Response("Document ID is required", {
      status: 400,
    });
  }

  console.log("documentId", documentId);

  const document = await prisma.document.findUnique({
    where: {
      id: documentId,
      orgId: org.id,
    },
  });

  if (!document) {
    return new Response("Document not found", {
      status: 404,
    });
  }

  try {
    const { body, contentType } = await getDocumentContents(
      document,
      prisma,
      env.AWS_S3_BUCKET_NAME,
    );

    // convert body into UInt8Array
    const uint8Array = new Uint8Array(body);
    return Response.json({ body: uint8Array, contentType });
  } catch (error) {
    console.error("Error getting document contents", error);

    await markDocumentStatusError({
      db: prisma,
      documentId: document.id,
      orgId: org.id,
    });

    return new Response("Error getting document contents", {
      status: 500,
    });
  }
}
