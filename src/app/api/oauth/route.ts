import {
  InvokeCommand,
  InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import { DocumentSource } from "@prisma/client";
import axios from "axios";
import { IndexerLambdaEvent } from "infra/lambda/indexer";
import { NextResponse } from "next/server";
import { env } from "~/env";
import { registerEgnyteWebhook } from "~/lib/integrations/egnyte/utils";
import { AzureTokenResponse } from "~/server/api/routers/azure";
import { EgnyteTokenResponse } from "~/server/api/routers/egnyte";
import { db as prisma } from "~/server/db";

const lambdaParams = {
  region: env.AWS_LAMBDA_REGION,
  credentials: {
    accessKeyId: env.AWS_LAMBDA_ACCESS_KEY,
    secretAccessKey: env.AWS_LAMBDA_SECRET_ACCESS_KEY,
  },
};

async function indexEgnyte(orgId: string) {
  try {
    // Delete all Egnyte documents for testing purposes, we can use a flag to check if we are in dev mode
    await prisma.document.deleteMany({
      where: {
        orgId: orgId,
        source: DocumentSource.EGNYTE,
      },
    });

    try {
      const lambdaClient = new LambdaClient(lambdaParams);

      const payload = JSON.stringify({
        source: DocumentSource.EGNYTE,
        orgId: orgId,
        secretId: env.AWS_LAMBDA_SECRET_ID,
      } as IndexerLambdaEvent);

      console.log("Payload", payload);
      const invokeParams: InvokeCommandInput = {
        FunctionName: `indexer-${env.LAMBDA_ENV}`,
        InvocationType: "Event",
        Payload: payload,
      };

      const command = new InvokeCommand(invokeParams);
      const response = await lambdaClient.send(command);
      console.log("Lambda invoked", response.FunctionError);
    } catch (error) {
      console.error(error);
      throw error;
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
}

async function egnyteOauth(code: string, host: string, state: string) {
  const config = {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  };

  const result = await axios.post<EgnyteTokenResponse>(
    `https://${env.EGNYTE_DOMAIN}/puboauth/token`,
    {
      client_id: env.EGNYTE_CONNECT_API_KEY,
      client_secret: env.EGNYTE_CONNECT_API_SECRET,
      // TODO: Change this to the actual redirect URL
      redirect_uri: `https://${host}/api/oauth`,
      code: code,
      grant_type: "authorization_code",
    },
    config,
  );

  console.log("result", result.data);

  const org = await prisma.org.findFirst({
    where: {
      webhookSecret: state.split("EGNYTE.")[1],
    },
  });

  if (!org) {
    return new NextResponse("", {
      status: 400,
      statusText: "Org not found",
    });
  }

  const egnyteAccessToken = await prisma.egnyteAccessToken.upsert({
    where: {
      id: org.egnyteAccessTokenId ?? "",
    },
    update: {
      accessToken: result.data.access_token,
      expiresAt: new Date(
        Date.now() + result.data.expires_in * 1000 * 60 * 60 * 24 * 30 * 12,
      ),
    },
    create: {
      accessToken: result.data.access_token,
      expiresAt: new Date(
        Date.now() + result.data.expires_in * 1000 * 60 * 60 * 24 * 30 * 12,
      ),
      org: {
        connect: {
          id: org.id,
        },
      },
    },
  });

  await indexEgnyte(org.id);

  await registerEgnyteWebhook(
    egnyteAccessToken.accessToken,
    `https://${host}/api/egnyte`,
  );

  return NextResponse.redirect(
    new URL(`https://${host}/integration-success?integration=egnyte`),
  );
}

async function azureOauth(code: string, host: string, state: string) {
  const config = {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  };

  console.log("code, host, state", code, host, state);

  const result = await axios.post<AzureTokenResponse>(
    `https://${env.AZURE_DOMAIN}/${env.AZURE_CLIENT_TENANT_ID}/oauth2/v2.0/token`,
    {
      client_id: env.AZURE_CLIENT_APP_ID,
      // redirect_uri: `https://${host}/api/oauth`,
      grant_type: "client_credentials",
      // code: code,
      scope: "https://graph.microsoft.com/.default",
      client_secret: env.AZURE_CLIENT_SECRET,
    },
    config,
  );

  console.log("Azure Token result", result.data);

  const org = await prisma.org.findFirst({
    where: {
      webhookSecret: state.split("AZURE.")[1],
    },
  });

  if (!org) {
    console.log("No org found for ", state);
    return new NextResponse("", {
      status: 400,
      statusText: "Org not found",
    });
  }

  const azureAccessToken = await prisma.azureAccessToken.upsert({
    where: {
      id: org.azureAccessTokenId ?? "",
    },
    update: {
      accessToken: result.data.access_token,
      refreshToken: "no_refresh",
      expiresAt: new Date(Date.now() + result.data.expires_in * 1000),
    },
    create: {
      accessToken: result.data.access_token,
      refreshToken: "no_refresh",
      expiresAt: new Date(Date.now() + result.data.expires_in * 1000),
      org: {
        connect: {
          id: org.id,
        },
      },
    },
  });

  return NextResponse.redirect(
    new URL(`https://${host}/integration-success?integration=azure`),
  );
}

export async function GET(req: Request) {
  console.log("GET /oauth");
  const payload = await req.text();
  const host = req.headers.get("host");

  // Get URL Parameters
  const url = new URL(req.url);
  const code = url.searchParams.get("code");
  const error = url.searchParams.get("error");
  const state = url.searchParams.get("state");

  if (error) {
    return new NextResponse("", { status: 400, statusText: error });
  }

  if (!state) {
    return new NextResponse("", { status: 400, statusText: "Invalid state" });
  }

  if (state) {
    console.log("code", code);
    console.log("state", state);

    if (state.includes("EGNYTE")) {
      try {
        return await egnyteOauth(code ?? "", host ?? "", state);
      } catch (error) {
        console.error("Error requesting Egnyte token", error);
        return NextResponse.redirect(
          new URL(`https://${host}/integration-fail?integration=egnyte`),
        );
      }
    } else if (state.includes("AZURE")) {
      try {
        return await azureOauth(code ?? "", host ?? "", state);
      } catch (error) {
        console.error("Error requesting Azure token", error);
        return NextResponse.redirect(
          new URL(`https://${host}/integration-fail?integration=azure`),
        );
      }
    }
  }

  return new NextResponse("", { status: 400, statusText: "Invalid state" });
}
