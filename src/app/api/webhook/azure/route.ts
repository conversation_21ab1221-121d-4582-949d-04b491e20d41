import {
  InvokeCommand,
  type InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import { type AzureAccessToken } from "@prisma/client";
import {
  type AzureWebhookLambdaEvent,
  processSharepointDelta,
} from "infra/lambda/azureWebhookHandler";
import { NextResponse } from "next/server";
import { env } from "~/env";
import {
  type AzureWebhookResponse,
  type DriveItem,
} from "~/lib/integrations/azure/types";
import { reauthorizeAndRenewSharepointSubscription } from "~/lib/integrations/azure/utils";
import { db } from "~/server/db";

export type AzureWebhookEvent = {
  orgId: string;
  azureAccessToken: AzureAccessToken;
  modifiedItems: DriveItem[];
  deletedItems: DriveItem[];
};

const lambdaParams = {
  region: env.AWS_LAMBDA_REGION,
  credentials: {
    accessKeyId: env.AWS_LAMBDA_ACCESS_KEY,
    secretAccessKey: env.AWS_LAMBDA_SECRET_ACCESS_KEY,
  },
};

export async function POST(req: Request) {
  console.log("Azure webhook received");

  const url = new URL(req.url);
  const validationToken = url.searchParams.get("validationToken");

  // If validationToken is present, return it
  // Required for Azure to validate the webhook
  // https://learn.microsoft.com/en-us/graph/change-notifications-delivery-webhooks?tabs=http
  if (validationToken) {
    return new NextResponse(validationToken, { status: 200 });
  }

  try {
    const payload = (await req.json()) as AzureWebhookResponse;

    if (payload.value[0]?.lifecycleEvent === "subscriptionRemoved") {
      console.log("Subscription removed", payload.value[0]);
      return new NextResponse("OK", { status: 202 });
    }

    if (payload.value[0]?.lifecycleEvent === "reauthorizationRequired") {
      console.log("Reauthorization required", payload.value[0]);
      await reauthorizeAndRenewSharepointSubscription(
        payload.value[0]?.subscriptionId ?? "",
        db,
      );

      return new NextResponse("OK", { status: 202 });
    }

    if (payload.value[0]?.lifecycleEvent === "missed") {
      console.log("Missed", payload.value[0]);
      return new NextResponse("OK", { status: 202 });
    }

    console.log("Azure webhook payload", JSON.stringify(payload, null, 2));
    if (process.env.NODE_ENV === "development") {
      console.log("Indexing documents locally");
      await processSharepointDelta(db, {
        payload: payload,
        secretId: env.AWS_LAMBDA_SECRET_ID,
      } as AzureWebhookLambdaEvent);
    } else {
      try {
        const lambdaClient = new LambdaClient(lambdaParams);

        const invokeParams: InvokeCommandInput = {
          FunctionName: `azureWebhookHandler-${env.LAMBDA_ENV}`,
          InvocationType: "Event",
          Payload: JSON.stringify({
            payload: payload,
            secretId: env.AWS_LAMBDA_SECRET_ID,
          } as AzureWebhookLambdaEvent),
        };

        const command = new InvokeCommand(invokeParams);
        const response = await lambdaClient.send(command);
        console.log("Lambda invoked", response);
      } catch (error) {
        console.error(error);
        throw error;
      }
    }

    // console.log("payload", JSON.stringify(payload, null, 2));

    // const delta = await getSharepointDelta(
    //   payload.value[0]?.subscriptionId ?? "",
    // );

    // // Reprocess all documents that were modified
    // // This needs to go into a Lambda, running locally for now
    // if (delta) {
    //   console.log("Reprocessing all documents that were modified");
    //   try {
    //     await deleteDocuments(delta);
    //   } catch (error) {
    //     console.error("Error deleting documents", error);
    //   }

    //   try {
    //     await renameDocuments(delta);
    //   } catch (error) {
    //     console.error("Error renaming documents", error);
    //   }

    //   try {
    //     console.log("Creating new documents");
    //     const newDocuments = await createDocuments(delta);
    //     await executeVectorizer(delta, newDocuments);
    //   } catch (error) {
    //     console.error("Error creating new documents", error);
    //   }
    // }
  } catch (error) {
    console.error(error);
    console.error(await req.json());
  }

  return new NextResponse("OK", { status: 200 });
}
