import { ParticipantStatus, UserR<PERSON>, UserTitle } from "@prisma/client";
import { NextResponse } from "next/server";
import { Webhook } from "svix";
import { env } from "~/env";
import { db } from "~/server/db";
import { ClerkWebhookEvent } from "~/types/clerk";

const webhookSecret: string = env.SVIX_WEBHOOK_SECRET ?? "";

const deleteUser = async (userId: string) => {
  console.log("Looking up user", userId);

  const user = await db.user.findUniqueOrThrow({
    where: {
      clerkId: userId,
    },
  });

  console.log("Deleting user", user?.id);
  await db.conversation.deleteMany({
    where: {
      participants: {
        some: {
          userId: user?.id,
        },
      },
    },
  });

  await db.userOrg.deleteMany({
    where: {
      userId: user?.id,
    },
  });

  await db.user.delete({
    where: {
      id: user?.id,
    },
  });
};

const createUser = async (msg: ClerkWebhookEvent, orgId: string) => {
  return await db.user.upsert({
    where: {
      email: msg.data.email_addresses[0]?.email_address ?? "",
    },
    update: {
      clerkId: msg.data.id,
      name: `${msg.data.first_name} ${msg.data.last_name}`,
      email: msg.data.email_addresses[0]?.email_address ?? "",
      title: UserTitle.Other,
      image: msg.data.image_url,
      role: UserRole.Member,
      bio: "",
      emailVerified: new Date(),
    },
    create: {
      clerkId: msg.data.id,
      name: `${msg.data.first_name} ${msg.data.last_name}`,
      email: msg.data.email_addresses[0]?.email_address ?? "",
      title: UserTitle.Other,
      image: msg.data.image_url,
      role: UserRole.Member,
      bio: "",
      emailVerified: new Date(),
      orgs: {
        create: {
          orgId: orgId,
        },
      },
    },
  });
};

const createConversation = async (
  msg: ClerkWebhookEvent,
  orgId: string,
  userId: string,
) => {
  const chatBot = await db.user.findFirst({
    where: {
      email: "<EMAIL>",
    },
  });

  return await db.conversation.create({
    data: {
      createdAt: new Date(),
      updatedAt: new Date(),
      orgId: orgId,
      createdById: userId,
      messages: {
        create: {
          body: "Hello, how can I help you today?",
          createdById: chatBot?.id ?? "",
          orgId: orgId,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      },
      participants: {
        create: [
          {
            userId: chatBot?.id ?? "",
            status: ParticipantStatus.ONLINE,
          },
          {
            userId: userId,
            status: ParticipantStatus.ONLINE,
          },
        ],
      },
    },
  });
};

export async function POST(req: Request) {
  console.log("Clerk webhook received");

  const svix_id = req.headers.get("svix-id") ?? "";
  const svix_timestamp = req.headers.get("svix-timestamp") ?? "";
  const svix_signature = req.headers.get("svix-signature") ?? "";

  const body = await req.text();
  const sivx = new Webhook(webhookSecret);

  try {
    const msg = sivx.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as ClerkWebhookEvent;

    console.log("Svix Verified", JSON.stringify(msg, null, 2));

    const orgId = req.headers.get("orgId");
    if (!orgId) {
      return new NextResponse("Bad Request", { status: 400 });
    }

    console.log("Searching for org", orgId);

    const orgValidated = await db.org.findUniqueOrThrow({
      where: {
        id: orgId,
      },
    });

    if (!orgValidated) {
      return new NextResponse("Bad Request", { status: 400 });
    }

    console.log("Org Validated");
    if (msg.type === "user.created") {
      console.log("Creating user and conversation");
      const user = await createUser(msg, orgId);
      await createConversation(msg, orgId, user.id);
      console.log("User created and conversation created successfully");
    }

    if (msg.type === "user.deleted") {
      console.log("Deleting user and conversation");
      await deleteUser(msg.data.id);
      console.log("User deleted and conversation deleted successfully");
    }
  } catch (error) {
    console.error(error);
    return new NextResponse("Bad Request", { status: 400 });
  }

  return new NextResponse("OK", { status: 200 });
}
