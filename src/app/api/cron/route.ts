import type { NextRequest } from "next/server";
import { env } from "~/env";
import {
  reauthorizeAndRenewSharepointSubscription,
  registerWebhook,
} from "~/lib/integrations/azure/utils";
import { db as prisma } from "~/server/db";

export async function GET(request: NextRequest) {
  const authHeader = request.headers.get("authorization");

  if (authHeader !== `Bearer ${env.CRON_SECRET}`) {
    return new Response("Unauthorized", {
      status: 401,
    });
  }

  const org = await prisma.org.findFirst({
    where: {
      clerkId: env.CLERK_ORG_ID,
    },
    include: {
      azureAccessToken: true,
      AzureDrive: {
        include: {
          subscriptions: true,
        },
      },
    },
  });

  if (!org) {
    return new Response("Org not found", {
      status: 404,
    });
  }

  const azureAccessToken = org.azureAccessToken;

  if (!azureAccessToken) {
    return new Response("Azure access token not found", {
      status: 404,
    });
  }

  const azureDrive = org.AzureDrive[0];

  if (!azureDrive) {
    return new Response("Azure drive not found", {
      status: 404,
    });
  }

  const subscription = azureDrive.subscriptions[0];

  if (!subscription) {
    console.log("Azure subscription not found, registering new one");
    await registerWebhook(
      [{ name: "", azureId: azureDrive.azureId }],
      azureDrive.id,
      org.id,
      prisma,
    );

    return Response.json({
      success: true,
      message: "Azure subscription not found, registered new one",
    });
  }

  try {
    if (subscription.expirationDateTime < new Date()) {
      await reauthorizeAndRenewSharepointSubscription(
        subscription.subscriptionId,
        prisma,
      );
    }
  } catch (error) {
    console.error(error);

    // console.error(
    //   "Error reauthorizing and renewing Sharepoint subscription",
    //   error,
    // );
    // console.log("Deleting and recreating subscription");

    // await deleteAllWebhooks(
    //   [{ name: "", azureId: "" }],
    //   azureAccessToken.accessToken,
    //   org.id,
    //   prisma,
    // );

    // await registerWebhook(
    //   [{ name: "", azureId: azureDrive.azureId }],
    //   azureDrive.azureId,
    //   org.id,
    //   prisma,
    // );
  }

  return Response.json({ success: true });
}
