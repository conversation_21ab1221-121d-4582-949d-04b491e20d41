import { DocumentChunkType } from "@prisma/client";
import type { Document } from "langchain/document";
import type { NextRequest } from "next/server";
import { z } from "zod";
import { env } from "~/env";
import { getChunksFromS3 } from "~/lib/integrations/aws/utils";
import { insertEmbeddedIntoDb } from "~/lib/vectorizer.insert-embedded";
import { db as prisma } from "~/server/db";

const Element = z.object({
  id: z.string(),
  pageContent: z.string(),
  metadata: z.record(z.string(), z.any()),
});

const RequestBody = z.object({
  documentId: z.string(),
  chunks: z.array(Element).optional(),
  chunkPath: z.string().optional(),
});

export async function POST(request: NextRequest) {
  const authHeader = request.headers.get("authorization");

  if (authHeader !== `Bearer ${env.CHUNKING_API_SECRET}`) {
    return new Response("Unauthorized", {
      status: 401,
    });
  }

  const org = await prisma.org.findFirst({
    where: {
      clerkId: env.CLERK_ORG_ID,
    },
  });

  if (!org) {
    return new Response("Org not found", {
      status: 404,
    });
  }

  const payload = await request.json();
  const reqBody = await RequestBody.safeParseAsync(payload);

  if (!reqBody.success) {
    console.error(reqBody.error);

    return new Response(JSON.stringify(reqBody.error), {
      status: 400,
    });
  }

  const { documentId, chunks, chunkPath } = reqBody.data;

  console.log(
    `Received request to store chunks: ${documentId}, ${chunks?.length} chunks`,
  );

  const documentChunks: Document[] = chunkPath
    ? await getChunksFromS3(chunkPath)
    : (chunks ?? []);

  if (chunkPath) {
    console.log(`Retrieved ${documentChunks.length} chunks from S3`);
  }

  const document = await prisma.document.findUnique({
    where: {
      id: documentId,
      orgId: org.id,
    },
  });

  if (!document) {
    return new Response("Document not found", {
      status: 404,
    });
  }

  try {
    await insertEmbeddedIntoDb(
      documentChunks,
      {
        db: prisma,
        document: {
          id: documentId,
          name: document.name,
        },
        orgId: org.id,
      },
      {},
      DocumentChunkType.JUMBO,
    );
    return Response.json({ success: true });
  } catch (error) {
    return new Response(JSON.stringify(error), {
      status: 500,
    });
  }
}
