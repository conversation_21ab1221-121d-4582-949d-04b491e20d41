import {
  InvokeCommand,
  type InvokeCommandInput,
  LambdaClient,
} from "@aws-sdk/client-lambda";
import { type LambdaEvent } from "infra/lambda/vectorizer";
import type { NextRequest } from "next/server";
import { z } from "zod";
import { env } from "~/env";
import {
  documentClassifier,
  documentFundClassifier,
  questionClassifier,
} from "~/lib/classifier";
import { getDocsFromS3 } from "~/lib/integrations/s3/utils";
import {
  markDocumentStatusError,
  markDocumentStatusReady,
  processAndUpdateDDQ,
  segmentDocumentLocal,
} from "~/lib/vectorizer";
import { db as prisma } from "~/server/db";

const lambdaParams = {
  region: env.AWS_LAMBDA_REGION,
  credentials: {
    accessKeyId: env.AWS_LAMBDA_ACCESS_KEY,
    secretAccessKey: env.AWS_LAMBDA_SECRET_ACCESS_KEY,
  },
};

const lambdaClient = new LambdaClient(lambdaParams);

export const Element = z.object({
  id: z.string(),
  pageContent: z.string(),
  metadata: z.string(),
  type: z.string(),
});

export type Element = z.infer<typeof Element>;
const RequestBody = z.object({
  documentId: z.string(),
  elementsPath: z.string(),
});

export async function POST(request: NextRequest) {
  const authHeader = request.headers.get("authorization");

  if (authHeader !== `Bearer ${env.CHUNKING_API_SECRET}`) {
    return new Response("Unauthorized", {
      status: 401,
    });
  }

  const org = await prisma.org.findFirst({
    where: {
      clerkId: env.CLERK_ORG_ID,
    },
  });

  if (!org) {
    return new Response("Org not found", {
      status: 404,
    });
  }

  const payload = await request.json();

  const reqBody = await RequestBody.safeParseAsync(payload);

  if (!reqBody.success) {
    return new Response(JSON.stringify(reqBody.error), {
      status: 400,
    });
  }

  const { documentId, elementsPath } = reqBody.data;
  const elements = await getDocsFromS3(elementsPath, env.AWS_S3_BUCKET_NAME);

  console.log(
    `Received request to process raw file: ${documentId}, ${elements.length} elements`,
  );

  const document = await prisma.document.findUnique({
    where: {
      id: documentId,
      orgId: org.id,
    },
  });

  if (!document) {
    return new Response("Document not found", {
      status: 404,
    });
  }

  try {
    const docs = elements.map((element) => ({
      pageContent: element.pageContent,
      metadata: JSON.parse(JSON.stringify(element.metadata)) as Element,
    }));

    if (process.env.NODE_ENV === "development") {
      console.log("Segmenting document");

      try {
        void segmentDocumentLocal({
          db: prisma,
          documentId: documentId,
          docs: docs,
          orgId: org.id,
        });

        void processAndUpdateDDQ({
          db: prisma,
          document: document,
          docs: docs,
          orgId: org.id,
          secret: {},
        });
      } catch (error) {
        console.error("Error segmenting document", error, docs);
        await markDocumentStatusError({
          db: prisma,
          documentId: documentId,
          orgId: org.id,
        });
      }

      console.log("Processing and updating document classifiers");

      await Promise.all([
        questionClassifier({
          db: prisma,
          orgId: org.id,
          documentId: documentId,
        }),
        documentClassifier({
          db: prisma,
          orgId: org.id,
          documentId: documentId,
        }),
        documentFundClassifier({
          db: prisma,
          orgId: org.id,
          documentId: documentId,
        }),
      ]);

      await markDocumentStatusReady({
        db: prisma,
        documentId: documentId,
        orgId: org.id,
      });
    } else {
      // Run vectorizer in Lambda in production environment
      console.log("Invoking Lambda");

      try {
        const invokeParams: InvokeCommandInput = {
          FunctionName: `vectorizer-${env.LAMBDA_ENV}`,
          InvocationType: "Event",
          Payload: JSON.stringify({
            documentId: document.id,
            orgId: org.id,
            bucket: process.env.AWS_S3_BUCKET_NAME!,
            secretId: env.AWS_LAMBDA_SECRET_ID,
            docsPath: elementsPath,
            stage: "SEGMENT_DOCUMENT",
          } as LambdaEvent),
        };

        const command = new InvokeCommand(invokeParams);
        const response = await lambdaClient.send(command);
        console.log("Lambda invoked", response);
      } catch (error) {
        console.error(error);
        throw error;
      }
    }
    return Response.json({ success: true });
  } catch (error) {
    return new Response(JSON.stringify(error), {
      status: 500,
    });
  }
}
