"use client";

import React from "react";
import { ChatView } from "../../sections/chat/view/chat-view";
import { getLocalStorageTagsIds } from "../components/virgil/OfficeAddIn/localStorageClient";
import { getLocalStorageFundIds } from "../components/virgil/OfficeAddIn/localStorageClient";

export default function Page() {
  let initialTagIds: string[] = [];
  let initialFundIds: string[] = [];
  try {
    initialTagIds = getLocalStorageTagsIds();
    initialFundIds = getLocalStorageFundIds();
  } catch (error) {
    console.error(error);
  }
  return <ChatView initialTagIds={initialTagIds} initialFundIds={initialFundIds} />
}

