"use client";

import { useUser } from "@clerk/nextjs";
import ArrowCircleRightIcon from "@mui/icons-material/ArrowCircleRight";
import SouthIcon from "@mui/icons-material/South";
import { Box, Button, Stack, Typography } from "@mui/material";
import Script from "next/script";
import { useEffect, useState } from "react";
import Loading from "~/app/dashboard/loading";
import { Logo } from "~/components/logo";
import {
  CellEdit,
  CellEditAction,
  CellEditActions,
  RAGResponseWithCitations,
} from "~/lib/types";
import { api } from "~/trpc/react";
import { DocumentEditTimeline } from "./Timeline";
import { CitationsAccordionList } from "../components/virgil/OfficeAddIn/DetailedView/CitationsAccordionList";

const getDestinationRange = async (): Promise<Excel.Range | null> => {
  try {
    return await Excel.run(async (context) => {
      const range = context.workbook.getSelectedRange();
      await context.sync();

      const rangePlusOneToTheRight = range.getOffsetRange(0, 1);

      const address = rangePlusOneToTheRight.load("address");
      await context.sync();

      return rangePlusOneToTheRight;
    });
  } catch (error) {
    console.error(error);
    return null;
  }
};

async function getActiveSheet() {
  try {
    return await Excel.run(async (context) => {
      const worksheet = context.workbook.worksheets.getActiveWorksheet();
      worksheet.load("name");
      await context.sync();

      return worksheet.name;
    });
  } catch (error) {
    console.error("Error getting active sheet", error);
    return null;
  }
}

async function selectRange({ address }: { address: string }) {
  try {
    await Excel.run(async (context) => {
      const sheet = context.workbook.worksheets.getActiveWorksheet();
      const range = sheet.getRange(address);

      range.select();

      await context.sync();
    });
  } catch (error) {
    console.error(error);
  }
}

async function insertValue({
  documentId,
  text,
  rangeAddress,
}: {
  documentId: string;
  text: string;
  rangeAddress: string | null;
}) {
  console.log("insertValue", { documentId, text, rangeAddress });

  // TODO:
  // 1. Insert new record using edit.mutate() with citations in Json payload
  // 2. Insert value into cell
  // 3. Modify onChanged() callback to avoid duplidate edits

  try {
    await Excel.run(async (context) => {
      console.log("edit success");

      const range = context.workbook.getSelectedRange();
      const cellValue = range.load("values");
      await context.sync();

      console.log(cellValue.values);

      const destRange = rangeAddress
        ? context.workbook?.worksheets
          ?.getActiveWorksheet()
          ?.getRange(rangeAddress)
        : range.getOffsetRange(0, 1);

      const address = destRange?.load("address");
      await context.sync();

      console.log(
        `Inserting ${text} into ${address?.address.split("!").at(1)}`,
      );

      destRange.values = [[text]];

      // sync the context to run the previous API call, and return.
      return context.sync();
    });
  } catch (error) {
    console.error(error);
  }
}

async function registerCellSelectionHandler(
  info: Office.HostType | null,
  setCellValue: (value: string) => void,
  setRangeAddress: (value: string) => void,
) {
  if (info === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      context.workbook.worksheets.onSingleClicked.add(async (event) => {
        console.log("event", event);

        // Get contents of clicked cell
        const range = event.address;
        const cellValue = context?.workbook?.worksheets
          ?.getActiveWorksheet()
          ?.getRange(range)
          .load("values");

        await context.sync();

        console.log("values", cellValue?.values?.at(0)?.at(0) ?? "");
        setCellValue((cellValue?.values?.at(0)?.at(0) as string) ?? "");
        setRangeAddress(range);
      });

      return context.sync();
    });
  }
}

async function registerCellEditHandler(
  info: Office.HostType | null,
  setCellEdit: (details: CellEdit) => void,
) {
  if (info === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      context.workbook.worksheets.onChanged.add(async (event) => {
        console.log("onChanged event", event);

        setCellEdit({
          ...event.details,
          range: event.address,
        });
      });

      return context.sync();
    });
  }
}

// Called when the document is opened and retrieves the full URL
// We can then use this URL to create a unique database record
async function registerFileOpenHandler(
  info: Office.HostType | null,
  setDocumentUrl: (value: string) => void,
) {
  if (info === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      console.log(Office.context.document.url);

      setDocumentUrl(`file://${Office.context.document.url}`);

      return context.sync();
    });
  }
}

async function registerSheetChangeHandler(
  info: Office.HostType | null,
  setSheet: (value: string) => void,
) {
  if (info === Office.HostType.Excel) {
    await Excel.run(async (context) => {
      context.workbook.worksheets.onActivated.add(async (event) => {
        console.log("onActivated event", event);

        const worksheet = context.workbook.worksheets.getItem(
          event.worksheetId,
        );

        worksheet.load("name");
        await context.sync();

        setSheet(worksheet?.name ?? "");

        console.log("worksheet", worksheet?.name);
      });

      return context.sync();
    });
  }
}

export default function Page() {
  const [info, setInfo] = useState<Office.HostType | null>(null);
  const [cellValue, setCellValue] = useState<string>("");
  const [activeSheet, setActiveSheet] = useState<string>("");
  const [rangeAddress, setRangeAddress] = useState<string>("");
  const [ragResponse, setRagResponse] =
    useState<RAGResponseWithCitations | null>(null);
  const [ragLoading, setRagLoading] = useState<boolean>(false);
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);
  const [documentId, setDocumentId] = useState<string | null>(null);
  const [modifiedCells, setModifiedCells] = useState<string[]>([]);
  const [cellEdit, setCellEdit] = useState<CellEdit | null>(null);
  const [isDDQ, setIsDDQ] = useState<boolean>(false);
  const [DDQStatus, setDDQStatus] = useState<string>("");

  const { user, isLoaded } = useUser();

  const documentOpen = api.document.open.useMutation();
  const rag = api.rag.generateRAGResponse.useMutation();
  const edit = api.document.edit.useMutation();

  // These two queries can be combined into one, but I'm keeping them separate for now
  const getEditsForRange = api.document.getEditsForRange.useQuery(
    {
      documentId: documentId ?? "",
      range: rangeAddress,
    },
    {
      enabled: !!documentId && !!rangeAddress,
    },
  );

  const getAllModifiedCells = api.document.getAllModifiedCells.useQuery(
    {
      documentId: documentId ?? "",
    },
    {
      enabled: !!documentId,
    },
  );

  const getDocumentAnalysis = api.document.getDocumentAnalysis.useQuery(
    {
      documentId: documentId ?? "",
    },
    {
      enabled: !!documentId,
    },
  );

  // Get a list of all modified cells, used to determine if the current cell has been modified
  useEffect(() => {
    if (getAllModifiedCells.data) {
      setModifiedCells(getAllModifiedCells.data);
    }
  }, [getAllModifiedCells.data]);

  // Generate a response when the user selects a cell
  // useEffect(() => {
  //   console.log("cellValue", cellValue);

  //   if (cellValue && !modifiedCells.includes(rangeAddress)) {
  //     setRagLoading(true);

  //     const messages =
  //       getDocumentAnalysis.data
  //         ?.map((analysis) => analysis.analysis.map((a) => a))
  //         .flatMap((a) => a)
  //         .filter((a) => a.sheet === activeSheet)
  //         .filter((a) => a.address === rangeAddress) ?? [];

  //     rag.mutate(
  //       {
  //         documentId: documentId ?? "",
  //         messages: messages,
  //       },
  //       {
  //         onSuccess: async (data) => {
  //           console.log("data", data);

  //           for await (const val of data) {
  //             if (val.response) {
  //               setRagResponse(val.response);
  //             }
  //           }

  //           setRagLoading(false);
  //         },
  //         onError: (error) => {
  //           console.error(error);

  //           setRagLoading(false);
  //         },
  //       },
  //     );
  //   } else {
  //     setRagResponse(null);
  //   }
  // }, [cellValue]);

  // Update the database on plugin open, so we can associate edits with the document
  useEffect(() => {
    if (documentUrl) {
      // Temporary hack to figure out if a document is a DDQ
      if (documentUrl.includes("[DDQ]")) {
        setIsDDQ(true);
      }

      documentOpen.mutate(
        { name: documentUrl.split("/").pop() ?? "", url: documentUrl },
        {
          onSuccess: (data) => {
            console.log("documentOpen", data);

            setDocumentId(data.id);
          },
          onError: (error) => {
            console.error(error);
          },
        },
      );

      (async () => {
        console.log("Getting active sheet");
        const activeSheet = await getActiveSheet();

        console.log("activeSheet", activeSheet);

        setActiveSheet(activeSheet ?? "");
      })();
    }
  }, [documentUrl]);

  // Get a list of edits for the current cell, used to display the edit history
  useEffect(() => {
    if (documentId && rangeAddress) {
      getEditsForRange.refetch();
    }

    console.log("getEditsForRange", getEditsForRange.data);
  }, [documentId, rangeAddress]);

  // Update the database on cell edit
  useEffect(() => {
    if (cellEdit) {
      if (cellEdit.valueBefore !== cellEdit.valueAfter) {
        console.log("cellEdit", cellEdit);

        edit.mutate(
          {
            id: documentId ?? "",
            edit: JSON.stringify({
              action: CellEditActions.REPLACE,
              range: cellEdit.range,
              valueBefore: cellEdit?.valueBefore,
              valueAfter: cellEdit?.valueAfter,
              editType: "Input Edited",
            } as CellEditAction),
          },
          {
            onSuccess: async () => {
              getEditsForRange.refetch();
              setCellEdit(null);
            },
            onError: (error) => {
              console.error("Error inserting value", error);
              setCellEdit(null);
            },
          },
        );
      }
    }
  }, [cellEdit, documentId, rangeAddress]);

  if (!isLoaded) {
    return <Loading />;
  }

  return (
    <div style={{ height: "100vh", margin: 5, padding: 5 }}>
      {/* Load the Office JS SDK */}
      <Script
        src="https://appsforoffice.microsoft.com/lib/1.1/hosted/office.js"
        onLoad={() => {
          Office.onReady((info) => {
            // Check that we loaded into Excel
            setInfo(info.host);

            // Register handlers for cell selection and file open
            registerCellSelectionHandler(
              info.host,
              setCellValue,
              setRangeAddress,
            );

            registerFileOpenHandler(info.host, setDocumentUrl);
            registerCellEditHandler(info.host, setCellEdit);
            registerSheetChangeHandler(info.host, setActiveSheet);

            // Associate the insertValue function with the "insertValue" action
            // This is used to insert the generated response into the cell
            Office.actions.associate(
              "insertValue",
              ({
                documentId,
                text,
                rangeAddress,
              }: {
                documentId: string;
                text: string;
                rangeAddress: string | null;
              }) => insertValue({ documentId, text, rangeAddress }),
            );
          });
        }}
      />
      <Logo width={100} height={35} />
      {!isDDQ && getEditsForRange.data && getEditsForRange.data.length > 0 && (
        <div style={{ width: "100%" }}>
          <DocumentEditTimeline edits={getEditsForRange.data} />
        </div>
      )}

      {/* DDQ demo mode */}
      {isDDQ && (
        <div>
          <Typography variant="h6">Hello, {user?.fullName}.</Typography>
          <Typography variant="subtitle2" style={{ marginTop: 10 }}>
            This looks like a Due Diligence Questionnaire (DDQ).
          </Typography>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: 10,
            }}
          >
            <Button
              variant="contained"
              endIcon={<ArrowCircleRightIcon />}
              style={{ width: "100%", marginTop: 10 }}
              loading={ragLoading}
              onClick={async () => {
                setRagLoading(true);
                console.log("generateResponses", getDocumentAnalysis.data);

                rag.mutate(
                  {
                    messages:
                      getDocumentAnalysis.data
                        ?.map((analysis) => analysis.analysis.map((a) => a))
                        .flatMap((a) => a)
                        .filter((a) => a.sheet === activeSheet) ?? [],
                    documentId: documentId ?? "",
                  },
                  {
                    onSuccess: async (data) => {
                      console.log("Response", data);

                      for await (const val of data) {
                        if (val.message) {
                          setDDQStatus(val.message.context);
                          await selectRange({
                            address: val.message.address,
                          });
                        }

                        // TODO: Standardize a way to return empty responses on the back end
                        if (
                          val.response &&
                          !val.response?.answer?.includes(
                            "is not explicitly stated in the provided sources",
                          ) &&
                          !val.response?.answer?.includes(
                            "No citations found",
                          ) &&
                          !val.response?.answer?.includes("Unable to")
                        ) {
                          console.log("Generated Response", val);

                          await insertValue({
                            documentId: documentId ?? "",
                            text: val.response?.answer ?? "",
                            rangeAddress: val.address,
                          });

                          getEditsForRange.refetch();
                        }
                      }

                      setRagLoading(false);
                      setDDQStatus("");
                    },
                    onError: (error) => {
                      console.error(error);

                      setRagLoading(false);
                    },
                  },
                );
              }}
            >
              <Typography variant="subtitle2">
                Generate All Responses
              </Typography>
            </Button>
          </div>
          <hr />
          <Button
            variant="outlined"
            style={{ width: "100%", marginTop: 10 }}
            endIcon={<ArrowCircleRightIcon />}
            loading={ragLoading}
            onClick={async () => {
              const messages =
                getDocumentAnalysis.data
                  ?.map((analysis) => analysis.analysis.map((a) => a))
                  .flatMap((a) => a)
                  .filter((a) => a.sheet === activeSheet)
                  .filter((a) => a.address === rangeAddress) ?? [];

              setRagLoading(true);
              rag.mutate(
                {
                  documentId: documentId ?? "",
                  messages: messages,
                },
                {
                  onSuccess: async (data) => {
                    console.log("data", data);

                    for await (const val of data) {
                      if (val.response) {
                        await insertValue({
                          documentId: documentId ?? "",
                          text: val.response?.answer ?? "",
                          rangeAddress: rangeAddress,
                        });
                      }
                    }

                    setRagLoading(false);
                  },
                  onError: (error) => {
                    console.error(error);

                    setRagLoading(false);
                  },
                },
              );
            }}
          >
            <Typography variant="subtitle2">
              Generate Response for Selected Cell
            </Typography>
          </Button>

          {ragLoading && (
            <Typography variant="subtitle2">
              Search Context: {DDQStatus}
            </Typography>
          )}
        </div>
      )}
      {!cellValue && !isDDQ && (
        <div>
          <Typography variant="h6">Hello, {user?.fullName}.</Typography>
          <Typography variant="subtitle2" style={{ marginTop: 10 }}>
            Welcome to the <strong>Virgil AI</strong> Excel Add-In! Select a
            cell to get started.
          </Typography>
        </div>
      )}
      {cellValue && !modifiedCells.includes(rangeAddress) && (
        <>
          <div style={{ marginTop: 20 }}>
            <Box
              style={{
                padding: 10,
                backgroundColor: "#f0f0f0",
                border: "1px solid black",
                borderRadius: 5,
              }}
            >
              <Typography style={{ marginBottom: 10 }} variant="subtitle2">
                Query
              </Typography>
              <hr />
              <Typography variant="body2">{cellValue}</Typography>
            </Box>
          </div>
          <div
            style={{
              marginTop: 20,
              width: "100%",
              alignContent: "center",
              alignItems: "center",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <SouthIcon />
          </div>
        </>
      )}
      {ragLoading && (
        <div style={{ marginTop: 20, marginBottom: 20 }}>
          <Loading />
        </div>
      )}
      {ragResponse && !ragLoading && (
        <div style={{ marginTop: 20, marginBottom: 20 }}>
          <Box
            style={{
              padding: 10,
              backgroundColor: "#f0f0f0",
              border: "1px solid black",
              borderRadius: 5,
            }}
          >
            <Typography style={{ marginBottom: 10 }} variant="subtitle2">
              Generated Response
            </Typography>
            <hr />
            <Typography variant="body2">{ragResponse.answer}</Typography>
          </Box>
          {ragResponse.citations && ragResponse.citations.length > 0 && (
            <>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 10,
                }}
              >
                <Button
                  variant="contained"
                  endIcon={<ArrowCircleRightIcon />}
                  style={{ width: "100%", marginTop: 10 }}
                  onClick={async () => {
                    const destinationRange = await getDestinationRange();

                    if (destinationRange) {
                      edit.mutate(
                        {
                          id: documentId ?? "",
                          edit: JSON.stringify({
                            action: CellEditActions.INSERT,
                            range: destinationRange.address.split("!").at(1),
                            valueBefore: "",
                            valueAfter: ragResponse.answer,
                            citations: ragResponse.citations,
                            editType: "Input Created",
                          }),
                        },
                        {
                          onSuccess: async () => {
                            await insertValue({
                              documentId: documentId ?? "",
                              text: ragResponse.answer ?? "",
                              rangeAddress: null,
                            });

                            getEditsForRange.refetch();
                          },
                          onError: (error) => {
                            console.error("Error inserting value", error);
                          },
                        },
                      );
                    }
                  }}
                >
                  <Typography variant="subtitle2">
                    Insert Generated Response
                  </Typography>
                </Button>
              </div>
              <Stack>
                <Typography
                  style={{ marginTop: 10, marginBottom: 10 }}
                  variant="caption"
                >
                  <strong>Source citations:</strong>
                </Typography>
                <CitationsAccordionList citations={ragResponse.citations} />
              </Stack>
            </>
          )}
        </div>
      )}
      {!ragResponse && !ragLoading && (
        <div style={{ marginTop: 20, marginBottom: 20 }}>
          {/* <Box
            style={{
              padding: 10,
              backgroundColor: "#f0f0f0",
              border: "1px solid black",
              borderRadius: 5,
            }}
          >
            <Typography variant="body2">
              Document Repository is empty. Please upload source documents to
              generate responses.
            </Typography>
          </Box> */}
        </div>
      )}
    </div>
  );
}
