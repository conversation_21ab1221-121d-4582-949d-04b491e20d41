"use client";

import { Protect, useOrganizationList } from "@clerk/clerk-react";
import { useAuth } from "@clerk/nextjs";
import { Box, Button, Stack, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles";
import { useEffect } from "react";
import { useBoolean } from "~/hooks/use-boolean";
import { DashboardContent } from "~/layouts/dashboard/main";
import { FileManagerNewFolderDialog } from "~/sections/data-room/FileManagerNewFolderDialog";
import { FiltersProvider } from "~/sections/data-room/FundsAndTagsFilter/FiltersContext";
import { IntegrationsDialog } from "~/sections/data-room/IntegrationsDialog";
import { DataRoomView } from "~/sections/data-room/view";
import { api } from "~/trpc/react";
import Loading from "../loading";

// ----------------------------------------------------------------------

const orgNameToSlug = (name: string) => {
  return name.toLowerCase().replace(/ /g, "-");
};

export default function Page() {
  const { data: org, isLoading } = api.organization.get.useQuery({});
  const theme = useTheme();
  const addNewIntegration = useBoolean();
  const upload = useBoolean();

  const { orgId } = useAuth();
  const { isLoaded, setActive, userMemberships } = useOrganizationList({
    userMemberships: {
      infinite: true,
    },
  });

  const hasActiveOrg = orgId !== null;

  useEffect(() => {
    if (
      userMemberships?.data?.[0]?.organization &&
      setActive &&
      !hasActiveOrg
    ) {
      console.log(
        "Setting active org id",
        userMemberships?.data[0]?.organization.id,
      );
      setActive({ organization: userMemberships?.data[0]?.organization.id });
    }
  }, [isLoaded, userMemberships, setActive]);

  if (isLoading) {
    return <Loading />;
  }

  if (!org) {
    return <Loading />;
  }

  return (
    <>
      <DashboardContent maxWidth={false}>
        <Grid container spacing={3}>
          <Grid size={12}>
            <Box
              sx={{
                padding: theme.spacing(3),
                borderRadius: theme.spacing(1),
                display: "flex",
                justifyContent: "space-between",
                alignItems: "flex-end",
                pl: 3,
              }}
            >
              <Typography variant="h3">Data Room</Typography>
              <Stack direction="row" spacing={2}>
                <Protect permission="org:integrations:manage" fallback={<></>}>
                  <Button
                    variant="outlined"
                    onClick={addNewIntegration.onTrue}
                    sx={{
                      width: { xs: 1, md: 250 },
                    }}
                  >
                    Manage Integrations
                  </Button>
                </Protect>
                <Button
                  color="primary"
                  variant="contained"
                  onClick={upload.onTrue}
                >
                  Upload File
                </Button>
              </Stack>
            </Box>
          </Grid>
          <FiltersProvider>
            <DataRoomView org={org} />
          </FiltersProvider>
        </Grid>
      </DashboardContent>

      <IntegrationsDialog
        open={addNewIntegration.value}
        onClose={addNewIntegration.onFalse}
        title="Manage Integrations"
        orgId={org.id}
      />

      {/* This is the upload dialog */}
      <FileManagerNewFolderDialog
        open={upload.value}
        onClose={upload.onFalse}
        orgId={org.id}
        orgSlug={orgNameToSlug(org.name)}
      />
    </>
  );
}
