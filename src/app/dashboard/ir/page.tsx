"use client";

import { Box, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles";
import { DashboardContent } from "~/layouts/dashboard/main";

// ----------------------------------------------------------------------

export default function Page() {
  const theme = useTheme();

  return (
    <DashboardContent maxWidth="xl">
      <Grid container spacing={3}>
        <Grid size={12}>
          <Box
            sx={{
              padding: theme.spacing(3),
              borderRadius: theme.spacing(1),
              display: "flex",
              justifyContent: "flex-start",
              alignItems: "flex-end",
            }}
          >
            <Typography variant="h3">Investor Relations</Typography>
          </Box>
        </Grid>
      </Grid>
    </DashboardContent>
  );
}
