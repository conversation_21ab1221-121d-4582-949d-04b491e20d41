"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { paths } from "~/routes/paths";
import Loading from "../loading";

// ----------------------------------------------------------------------

// export const metadata = { title: `Dashboard - ${CONFIG.site.name}` };

export default function Page() {
  const router = useRouter();

  useEffect(() => {
    router.push(paths.dashboard.dataRoom);
  }, []);

  return <Loading />;
}
