"use client";

import { CompanyOverview } from "~/sections/overview/portfolio/view/company-overview";
import { api } from "~/trpc/react";
import Loading from "../../loading";

type Params = Promise<{ id: string }>;

export default async function Page({ params }: { params: Params }) {
  const { id } = await params;

  const { data: org, isLoading } = api.organization.get.useQuery({});

  if (isLoading) {
    return <Loading />;
  }

  if (!org) {
    return <Loading />;
  }

  return <CompanyOverview org={org} />;
}
