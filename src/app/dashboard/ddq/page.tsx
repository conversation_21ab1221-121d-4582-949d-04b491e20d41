"use client";

import { <PERSON>, Button, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles";
import { Iconify } from "~/components/iconify";
import { useBoolean } from "~/hooks/use-boolean";
import { DashboardContent } from "~/layouts/dashboard/main";
import { DDQListView } from "~/sections/ddq/view";
import { api } from "~/trpc/react";
import Loading from "../loading";

// ----------------------------------------------------------------------

export default function Page() {
  const { data: org, isLoading } = api.organization.get.useQuery({});
  const theme = useTheme();
  const addNewDDQ = useBoolean();

  if (isLoading) {
    return <Loading />;
  }

  if (!org) {
    return <Loading />;
  }

  return (
    <DashboardContent maxWidth={false}>
      <Grid container spacing={3}>
        <Grid size={12}>
          <Box
            sx={{
              padding: theme.spacing(3),
              borderRadius: theme.spacing(1),
              display: "flex",
              justifyContent: "space-between",
              alignItems: "flex-end",
              pl: 3,
            }}
          >
            <Typography variant="h3">DDQ Manager</Typography>
            <Button
              variant="contained"
              color="primary"
              endIcon={<Iconify icon="eva:chevron-left-fill" />}
              onClick={addNewDDQ.onTrue}
              sx={{ width: { xs: 1, md: 250 } }}
            >
              Add New DDQ
            </Button>
          </Box>
        </Grid>
        <DDQListView org={org} />
      </Grid>
    </DashboardContent>
  );
}
