"use client";

import { ChatView } from "~/sections/chat/view";
import { api } from "~/trpc/react";
import Loading from "../loading";

// ----------------------------------------------------------------------

export default function Page() {
  const { data: org, isLoading } = api.organization.get.useQuery({});

  if (isLoading) {
    return <Loading />;
  }

  if (!org) {
    return <Loading />;
  }

  return <ChatView />;
}
