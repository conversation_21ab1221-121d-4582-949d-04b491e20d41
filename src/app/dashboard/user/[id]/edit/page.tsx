import { _userList } from "src/_mock/_user";
import { CONFIG } from "src/config-global";

import { UserEditView } from "src/sections/user/view";

// ----------------------------------------------------------------------

export const metadata = {
  title: `User edit | Dashboard - ${CONFIG.site.name}`,
};

type Params = Promise<{ id: string }>;

export default async function Page({ params }: { params: Params }) {
  const { id } = await params;

  const currentUser = _userList.find((user) => user.id === id);
  // @ts-ignore
  return <UserEditView user={currentUser} />;
}

// ----------------------------------------------------------------------

/**
 * [1] Default
 * Remove [1] and [2] if not using [2]
 */
const dynamic = CONFIG.isStaticExport ? "auto" : "force-dynamic";

export { dynamic };

/**
 * [2] Static exports
 * https://nextjs.org/docs/app/building-your-application/deploying/static-exports
 */
export async function generateStaticParams() {
  if (CONFIG.isStaticExport) {
    return _userList.map((user) => ({ id: user.id }));
  }
  return [];
}
