"use client";

import { Box, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles";
import { DashboardContent } from "~/layouts/dashboard";

import { api } from "~/trpc/react";



import { useChat } from '@ai-sdk/react';

function ChatBox() {
  const chatController = useChat({
    api: "/pyrpc/squared",
    streamProtocol: "data",

    experimental_prepareRequestBody: (rawBody) => {
      // Send only the content of the last message
      const { id, messages } = rawBody;
      return {
        id,
        latestMessage: messages[messages.length - 1],
      };
    },
  });

  const { messages, input, handleInputChange, handleSubmit } = chatController;

  return (
    <>
      {
        messages.map(message => (
          <div key={message.id}>
            {message.role === 'user' ? 'User: ' : 'AI: '}
            {message.parts.map((part, index) => {

              switch (part.type) {
                case 'text':
                  return <div key={index}>Text streaming {part.text}</div>;
                case 'reasoning':
                  return <><pre key={index}> Reasoning streaming
                    {part.details.map(detail =>
                      detail.type === 'text' ? detail.text : '<redacted>',
                    )}
                  </pre>
                  <div key={index}>Source streaming JSON: {JSON.stringify(part)}</div>
                  </>
                case 'tool-invocation':
                  return "should not be the case"
                case "source":
                  return <div key={index}>Source straming JSON: {JSON.stringify(part.source)}</div>;
              }
            })}
          </div>
        ))
      }



      <form onSubmit={handleSubmit}>
        <input name="prompt" value={input} onChange={handleInputChange} />
        <button type="submit">Submit</button>
      </form>
    </>
  );
}

// ----------------------------------------------------------------------

export default function Page() {
  const pyrpc = api.pyrpc.time.useQuery();
  const theme = useTheme();

  return (
    <>
      <DashboardContent maxWidth={false}>
        <Grid container spacing={3}>
          <Grid size={12}>
            <Box
              sx={{
                padding: theme.spacing(3),
                borderRadius: theme.spacing(1),
                display: "flex",
                justifyContent: "space-between",
                alignItems: "flex-end",
                pl: 3,
              }}
            >
              <Typography variant="h3">Development</Typography>
            </Box>
          </Grid>
          <Box sx={{ p: 3 }}>
            <Typography variant="body1">PyRPC Data: {pyrpc.data}</Typography>
          </Box>
          <Box sx={{ p: 3 }}>
            <ChatBox />
          </Box>
        </Grid>
      </DashboardContent>
    </>
  );
}
