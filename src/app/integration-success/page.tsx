"use client";

import { Box, Button, Typography } from "@mui/material";
import { useSearchParams } from "next/dist/client/components/navigation";
import { Iconify } from "~/components/iconify";
import { Logo } from "~/components/logo";

export default function Page() {
  const integration = useSearchParams().get("integration");

  const logo =
    integration === "egnyte"
      ? "/assets/images/logos/egnyte.svg"
      : integration === "azure"
        ? "/assets/images/logos/sharepoint.png"
        : "/assets/images/logos/office.svg";

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      height="100vh"
      gap={2}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        justifyContent="center"
        gap={2}
      >
        <Logo />
        {integration && (
          <>
            <Iconify icon="tabler:arrows-left-right" width={48} height={48} />
            <img src={logo} height={100} />
          </>
        )}
      </Box>
      <Typography variant="h4">Integration Successful</Typography>
      <Typography variant="body1">
        You can now close this window and return to the application.
      </Typography>
      <Button variant="contained" onClick={() => window.close()}>
        Close Window
      </Button>
    </Box>
  );
}
