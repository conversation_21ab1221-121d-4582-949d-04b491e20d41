"use client";

import { SignIn } from "@clerk/nextjs";

export function SplitSignInView({
  forceRedirectUrl = "/dashboard"
}: {
  forceRedirectUrl?: string | null
}) {
  return (
    <SignIn
      path="/sign-in"
      forceRedirectUrl={forceRedirectUrl}
      appearance={{
        elements: {
          logoBox: {
            height: '50px'
          },
          footerAction: { display: "none" },
          socialButtons: {
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
          },
          socialButtonsBlockButton: {
            width: '100%',
            boxSizing: 'border-box',
          },
          cardBox: {
            boxShadow: 'none',
            border: 'none',
          },
          formButtonPrimary: {
            backgroundColor: '#5E8779',
            color: '#fff',
            border: 'none',
            padding: '10px 20px',
            fontSize: '16px',
            fontWeight: 'bold',
            boxShadow: 'none!important',
            '&:hover': {
              backgroundColor: '#5E8779',
              opacity: 0.8,
            },
          },
          formFieldLabel: {
            display: 'none',
          },
        },
      }}
    />
  );
}
