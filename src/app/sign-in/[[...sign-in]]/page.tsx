"use client";
import { AuthCenteredLayout } from "~/layouts/auth-centered";
import { SplitSignInView } from "./split-sign-in-view";
import { useSearchParams } from "next/navigation";

export default function Page() {
  const searchParams = useSearchParams();

  const redirectUrl = searchParams.get('redirect_url')
  return (
    <>
      <AuthCenteredLayout>
        <SplitSignInView forceRedirectUrl={redirectUrl} />
      </AuthCenteredLayout>
    </>
  );
}
