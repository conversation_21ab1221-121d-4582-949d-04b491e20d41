"use client";

import React from "react";
import { ViewSharepointDocument } from "~/sections/data-room/FileExplorer";
import { useSearchParams } from "next/navigation";

export default function Page() {
  // Get documentId from URL
  const documentId = useSearchParams().get("documentId");
  console.log("documentId", documentId);

  if (!documentId) {
    return <div>No document ID provided</div>;
  }

  return <ViewSharepointDocument documentId={documentId} />;
}
