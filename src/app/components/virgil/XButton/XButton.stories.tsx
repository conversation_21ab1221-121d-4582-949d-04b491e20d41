import React from "react";
import { Meta, StoryFn } from "@storybook/react";
import XButton from "./XButton";

export default {
    title: "Components/XButton",
    component: XButton,
    parameters: {
        layout: "centered", // Centers the component in the Storybook canvas
    },
} as Meta;

const Template: StoryFn<typeof XButton> = (args) => <XButton {...args} />;

export const Default = Template.bind({});
Default.args = {
    style: 'light',
    children: 'Test Label'
};

export const Dark = Template.bind({});
Dark.args = {
    style: 'dark',
    children: 'Test Label Dark'
};

