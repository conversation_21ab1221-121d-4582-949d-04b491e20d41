import React, { FC, PropsWithChildren } from "react";
import { Box, Button, ButtonProps, IconButton } from "@mui/material";
type Props = ButtonProps & {
    style: 'dark' | 'light';
    fullWidth?: boolean;
}
const XButton: FC<Props> = (props) => {
    const { style, fullWidth, ...rest } = props;
    return (
        <Button sx={{ paddingTop: 2.5, paddingBottom: 2.5, paddingLeft: 1, paddingRight: 1, width: fullWidth ? '100%' : 'auto' }} variant={style === 'dark' ? 'contained' : 'outlined'} size="small" {...rest} >
            {props.children}
        </Button>
    )
}

export default XButton;