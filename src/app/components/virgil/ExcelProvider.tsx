import { PropsWithChildren } from "react";
import Script from "next/script";
import { insertValue, registerCellEditHandler, registerCellSelectionHandler, registerFileOpenHandler, registerHostType, registerSheetChangeHandler } from "~/app/add-in-excel/utils";
import { useDispatch } from "react-redux";
import React from "react";
import { useDocumentInfo } from "./hooks/useDocumentInfo";
import { Box } from "@mui/material";

const ExcelProvider: React.FC<PropsWithChildren> = ({ children }) => {
    const dispatch = useDispatch();
    useDocumentInfo();
    return (
        <Box sx={{ height: "100vh", width: "100%", margin: 0, padding: 0 }}>
            <Script
                src="https://appsforoffice.microsoft.com/lib/1.1/hosted/office.js"
                onLoad={() => {
                    Office.onReady((info: {
                        host: Office.HostType;
                        platform: Office.PlatformType;
                    }) => {
                        registerHostType(info.host, dispatch);
                        registerCellSelectionHandler(info.host, dispatch);
                        registerFileOpenHandler(info.host, dispatch);
                        registerCellEditHandler(info.host, dispatch);
                        registerSheetChangeHandler(info.host, dispatch);

                        Office.actions.associate(
                            "insertValue",
                            ({
                                documentId,
                                text,
                                rangeAddress,
                            }: {
                                documentId: string;
                                text: string;
                                rangeAddress: string | null;
                            }) => insertValue({ documentId, text, rangeAddress }),
                        );
                    });
                }}
            />
            {children}
        </Box>
    )
}

export default ExcelProvider;
