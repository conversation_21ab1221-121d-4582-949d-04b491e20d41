import React from "react";
import { Meta, StoryFn } from "@storybook/react";
import DocumentAccordion from "./DocumentAccordion";

export default {
    title: "Components/DocumentAccordion",
    component: DocumentAccordion,
    parameters: {
        layout: "centered",
    },
} as Meta;

const Template: StoryFn<typeof DocumentAccordion> = (args) => (
    <div style={{ maxWidth: 600, margin: "0 auto", padding: 16 }}>
        <DocumentAccordion {...args} />
    </div>
);

export const Default = Template.bind({});
Default.args = {
    fileName: "PartnershipAgreement.docx",
    content:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    sourceLink: "#",
    pageNumber: 1,
};

export const LongContent = Template.bind({});
LongContent.args = {
    fileName: "InvestmentStrategy.docx",
    content:
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, libero vel consequat dictum, odio lorem suscipit purus, vel aliquam felis risus vel lorem. Nam euismod est at est blandit, vel volutpat justo interdum. Nullam nec fermentum nisi, a sodales massa. Aenean suscipit auctor lorem. Vestibulum suscipit, metus ac dignissim bibendum, mauris nisl feugiat lorem, quis auctor velit eros et purus. Ut viverra ligula ac mollis venenatis. Donec non justo at justo blandit sollicitudin. Curabitur auctor, libero vel tincidunt hendrerit, nisi massa efficitur arcu, ut scelerisque lorem magna quis nisi. Mauris efficitur nulla in urna feugiat, sit amet tristique nisi convallis.",
    sourceLink: "#",
    pageNumber: 1,
};

export const NoContent = Template.bind({});
NoContent.args = {
    fileName: "EmptyDocument.docx",
    content: "No preview available for this document.",
    sourceLink: "#",
};
