import { Chip } from "@mui/material"

export const GenericLabel = ({ label, color = "#207EEC", icon, fontSize = 10 }: { label: string, color?: string, icon?: React.ReactElement<unknown>, fontSize?: number }) => {
    return (
        <Chip
            sx={{ backgroundColor: `${color}1F`, color, fontSize, pl: 0.5, pr: 0.5 }}
            size="small"
            variant="soft"
            label={label}
            icon={icon}
        />
    )
}
