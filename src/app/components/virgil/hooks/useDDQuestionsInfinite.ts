import { DocumentStatus, QuestionStatusType } from "@prisma/client";
import { useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import { selectSearchDebounced } from "~/lib/features/documentDDQSelector";
import {
  DDQuestionWithFeedback,
  DDQuestionWithIndexAndFeedback,
} from "~/server/api/managers/questionManager";
import { selectDocumentMetadataDocumentStatus } from "~/lib/features/documentMetadataSelectors";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import { api } from "~/trpc/react";
import { useVirtualizer } from "@tanstack/react-virtual";

export const useDDQuestionsInfinite = (
  questionStatusFilter: QuestionStatusType,
) => {
  const documentId = useSelector(selectDocumentId);
  const searchString = useSelector(selectSearchDebounced);
  const documentStatus = useSelector(selectDocumentMetadataDocumentStatus);

  const defaultQueryInput = {
    documentId,
    limit: 100,
  };

  const querySettings = {
    enabled: !!documentId && documentStatus === DocumentStatus.READY,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  };

  const {
    data,
    isLoading,
    isPending,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = api.question.getAllQuestionsInfinite.useInfiniteQuery(defaultQueryInput, {
    ...querySettings,
    getNextPageParam: (lastPage) => lastPage.nextCursor,
  });

  useEffect(() => {
    if (hasNextPage && !isFetchingNextPage) {
      void fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const allQuestions: DDQuestionWithIndexAndFeedback[] = useMemo(
    () =>
      (data?.pages.flatMap((page) => page.items) ?? []).map((q, index) => ({
        ...q,
        index,
      })),
    [data?.pages],
  );
  const questionsByStatus: DDQuestionWithIndexAndFeedback[] = useMemo(() => {
    return allQuestions.filter(
      (question) =>
        question.status === questionStatusFilter &&
        question.text.toLowerCase().includes(searchString?.toLowerCase() ?? ""),
    );
  }, [allQuestions, questionStatusFilter, searchString]);

  return {
    questions: questionsByStatus,
    isLoading: isLoading || isPending,
    isFetchingNextPage,
    hasNextPage,
  };
};
