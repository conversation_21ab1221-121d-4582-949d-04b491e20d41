import { useCallback, useEffect, useState } from "react";
import { DDQuestionWithFeedback } from "~/server/api/managers/questionManager";

export const useDDQSelect = ({
  questions,
}: {
  questions: DDQuestionWithFeedback[];
}) => {
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [totalSelectedPerPage, setTotalSelectedPerPage] = useState(0);
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    if (questions.every((q) => selectedIds.includes(q.id))) {
      setSelectedIds((prev) =>
        prev.filter((id) => !questions.map((q) => q.id).includes(id)),
      );
    } else {
      setSelectedIds((prev) => [...prev, ...questions.map((q) => q.id)]);
    }
  };
  const handleSelectSingle = useCallback(
    (id: string) => {
      setSelectedIds((prev) =>
        prev.includes(id)
          ? prev.filter((prevId) => prevId !== id)
          : [...prev, id],
      );
    },
    [setSelectedIds],
  );

  // Reset when questions change (page change)
  useEffect(() => {
    setTotalSelectedPerPage(
      questions.filter((q) => selectedIds.includes(q.id)).length,
    );
    setIsAllSelected(questions.every((q) => selectedIds.includes(q.id)));
  }, [questions, selectedIds]);

  return {
    selectedIds,
    setSelectedIds,
    isAllSelected,
    handleSelectAll,
    handleSelectSingle,
    totalSelectedPerPage,
  };
};
