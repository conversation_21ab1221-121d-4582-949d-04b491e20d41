import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { api } from "~/trpc/react";
import { setDocumentMetadata } from "~/lib/features/documentMetadataSlice";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import { selectDocumentUrl } from "~/lib/features/documentSelectors";
import { setDocumentId } from "~/lib/features/documentSlice";
import { useUser } from "@clerk/nextjs";
import { setAddInNavigationPath } from "~/lib/features/addInUISlice";
import { DocumentStatus as DocumentStatusEnum } from "@prisma/client";

export const useDocumentInfo = () => {
  const dispatch = useDispatch();
  const documentId = useSelector(selectDocumentId);
  const documentUrl = useSelector(selectDocumentUrl);
  const userData = useUser();

  const fileName = documentUrl?.split("/").pop() ?? "";
  const isDemo =
    userData.isLoaded && userData.user?.publicMetadata?.demoMode === "true";

  const {
    data: documentInfoByUrl,
    isSuccess: isSuccessByUrl,
    isError: isErrorByUrl,
  } = api.document.getDocumentByUrl.useQuery(
    { url: documentUrl ?? "" },
    {
      enabled: !!documentUrl && !documentId && !isDemo,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      retry: false,
    },
  );

  const {
    data: documentInfoByFileName,
    isSuccess: isSuccessByFileName,
    isError: isErrorByFileName,
  } = api.document.getDocumentByFilename.useQuery(
    { filename: fileName },
    {
      enabled: !!documentUrl && !documentId && isDemo,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      retry: false,
    },
  );

  useEffect(() => {
    if (documentInfoByUrl && isSuccessByUrl) {
      dispatch(setDocumentId(documentInfoByUrl.id));
      dispatch(
        setDocumentMetadata({
          createdByName: documentInfoByUrl.createdBy.name ?? "",
          summary: documentInfoByUrl.summary ?? "",
          createdAt: documentInfoByUrl.createdAt.toLocaleDateString(),
          dueDate: documentInfoByUrl.dueDate?.toISOString() ?? "",
          title: documentInfoByUrl.title ?? "",
          documentStatus: documentInfoByUrl.status,
        }),
      );
      if (documentInfoByUrl.status !== DocumentStatusEnum.READY) {
        dispatch(setAddInNavigationPath("document-status"));
      } else {
        dispatch(setAddInNavigationPath("ddq"));
      }
    } else {
      dispatch(setAddInNavigationPath("document-status"));
    }
  }, [documentInfoByUrl, isSuccessByUrl, isErrorByUrl]);

  console.log("isDemo", isDemo);

  // Demo mode
  useEffect(() => {
    if (documentInfoByFileName && isSuccessByFileName) {
      dispatch(setDocumentId(documentInfoByFileName.id));
      dispatch(
        setDocumentMetadata({
          createdByName: "Created by Demo User",
          summary: documentInfoByFileName.summary ?? "",
          createdAt: documentInfoByFileName.createdAt.toLocaleDateString(),
          dueDate: documentInfoByFileName.dueDate?.toISOString() ?? "",
          title: documentInfoByFileName?.title ?? "",
          documentStatus: documentInfoByFileName.status,
        }),
      );
      if (documentInfoByFileName.status !== DocumentStatusEnum.READY) {
        dispatch(setAddInNavigationPath("document-status"));
      } else {
        dispatch(setAddInNavigationPath("ddq"));
      }
    } else {
      dispatch(setAddInNavigationPath("document-status"));
    }
  }, [documentInfoByFileName, isSuccessByFileName, isErrorByFileName]);
};
