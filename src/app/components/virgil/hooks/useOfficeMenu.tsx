import React, { useC<PERSON>back, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Box, IconButton, Drawer, Button, Typography, useTheme, Avatar } from "@mui/material";
import MoreVertIcon from '@mui/icons-material/MoreVert';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import { SignOutButton } from "~/layouts/components/SignOutButton";
import { selectAddInNavigationPath, selectBackRoute } from "~/lib/features/addInUISelectors";
import { setAddInNavigationPath, AddInNavigationPath, navigateBack } from "~/lib/features/addInUISlice";
import { CONFIG } from "~/config-global";
import { useOrg } from "~/app/providers/OrgProvider";
import { navSectionClasses } from "~/components/nav-section";

const MENU_ITEMS: { path: AddInNavigationPath, label: string }[] = [
    { path: 'document-status', label: 'Document Status' },
    { path: 'ddq', label: 'DDQ' },
    { path: 'document-details', label: 'Document Details' },
    { path: 'settings', label: 'Settings' },
    { path: 'collaboration', label: 'Collaboration' },
    { path: 'alerts', label: 'Alerts' },
    { path: 'document-feedback-status', label: 'Feedback' },
];

export const useOfficeNavigation = () => {
    const dispatch = useDispatch();
    const { orgName, orgLogo } = useOrg();
    const theme = useTheme();
    const addInNavigationPath = useSelector(selectAddInNavigationPath);
    const backRoute = useSelector(selectBackRoute);
    const navigateTo = (path: AddInNavigationPath) => {
        closeDrawer();
        dispatch(setAddInNavigationPath(path));
    };

    const [isDrawerOpen, setIsDrawerOpen] = useState(false);

    const openDrawer = () => {
        setIsDrawerOpen(true);
    };

    const closeDrawer = () => {
        setIsDrawerOpen(false);
    };

    const navigateBackClick = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        dispatch(navigateBack());
    }, [dispatch]);

    const navigateToIndexDDQClick = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        dispatch(setAddInNavigationPath('ddq'));
    }, [dispatch]);

    const backButton = useMemo(() => {
        if (backRoute) {
            return <IconButton onClick={navigateBackClick}><ChevronLeftIcon /></IconButton>;
        }
        return null;
    }, [backRoute, navigateBackClick]);

    const menuToggleButton = useMemo(() => {
        if (addInNavigationPath === 'initializing') {
            return null;
        }
        if (addInNavigationPath === 'ddq') {
            return <>
                <Box
                    alt="logo"
                    component="img"
                    src={`${CONFIG.site.basePath}/logo/virgil-square.png`}
                    width={25}
                    height={25}
                />

                <IconButton size="small" onClick={openDrawer}>
                    <MoreVertIcon />
                </IconButton>
            </>
        }
        return <IconButton onClick={navigateToIndexDDQClick}>
            <ChevronLeftIcon />
        </IconButton>
    }, [openDrawer, navigateToIndexDDQClick]);


    const Menu = (<Drawer
        anchor="left"
        open={isDrawerOpen}
        onClose={closeDrawer}
        sx={{ '& .MuiDrawer-paper': { width: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between', height: '100%', paddingBottom: '20px', zIndex: 1000 } }}
    >
        <Box sx={{ flexGrow: 1, padding: '10px' }}>
            <IconButton onClick={closeDrawer}>
                <ChevronLeftIcon />
            </IconButton>
            <Box sx={{ marginTop: '20px' }}>
                {MENU_ITEMS.map(({ path, label }) => (
                    <Button
                        key={path}
                        fullWidth
                        sx={{
                            justifyContent: 'start',
                            backgroundColor: addInNavigationPath === path ? '#e0e0e0' : 'transparent',
                            '&:hover': {
                                backgroundColor: '#e0e0e0'
                            }
                        }}
                        onClick={() => navigateTo(path as AddInNavigationPath)}
                    >
                        {label}
                    </Button>
                ))}
            </Box>
        </Box>
        <Box sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: "10px",
            backgroundColor: '#e0e0e0',
            padding: '10px',
            borderTop: '1px solid #888'
        }}>
            <Avatar
                src={orgLogo}
                sx={{
                    width: 28,
                    height: 28,
                    border: 1,
                    borderRadius: 50,
                    borderColor: "var(--layout-nav-border-color)",
                }}
            />
            <Typography variant="body2" sx={{ fontWeight: theme.typography.fontWeightSemiBold }}>{orgName}</Typography>
        </Box>
        <Box sx={{ p: 1 }}>
            <SignOutButton
                size="medium"
                variant="text"
                sx={{ display: 'block', textAlign: 'left' }}
            />
        </Box>
    </Drawer>)

    return {
        Menu,
        openDrawer,
        closeDrawer,
        backButton,
        menuToggleButton,
    };
};