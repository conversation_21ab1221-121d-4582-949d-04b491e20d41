import { GetDocumentType } from "~/server/api/routers/question";
import { api } from "~/trpc/react";

export const useGetExistingResponseById = (responseId: string | null) => {
  const { data, isLoading } = api.response.getResponseById.useQuery(
    { id: responseId ?? "" },
    {
      enabled: !!responseId,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: true,
      retry: true,
    },
  );

  return {
    data,
    isLoading,
    responseText:
      (data?.responseContents[0]?.content as { text: string })?.text ?? null,
    responseContentId: data?.responseContents[0]?.id ?? null,
    responseCanonicalId: data?.id ?? null,
    responseDocuments:
      (data?.documents.map(
        (document) => document.document,
      ) as GetDocumentType[]) ?? [],
  };
};

export const useGetResponseByIdQueryKey = (responseId: string | null) => {
  return ["response", responseId];
};
