import { useSelector } from "react-redux";
import { selectSearchDebounced } from "~/lib/features/documentDDQSelector";
import { normalizePrompts } from "~/lib/features/documentDDQSlice";
import { selectActiveSheetName, selectDocumentId } from "~/lib/features/documentSelectors";
import { api } from "~/trpc/react";

export const useDDQExcelPropts = () => {

  const documentId = useSelector(selectDocumentId);
  const activeSheetName = useSelector(selectActiveSheetName);
  const searchString = useSelector(selectSearchDebounced);

  const { data, isLoading, isFetching } = api.sheet.getAllPromptsForSheet.useQuery({
    documentId: documentId ?? "",
    sheetName: activeSheetName ?? "",
    search: searchString ?? "",
  }, {
    enabled: !!documentId && !!activeSheetName,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5,
  });

  return {
    prompts: normalizePrompts(data ?? []),
    isLoading: isLoading || isFetching,
  };
};
