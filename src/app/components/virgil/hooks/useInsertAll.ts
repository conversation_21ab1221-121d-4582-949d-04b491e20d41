import { useCallback, useState } from "react";
import { useSelector } from "react-redux";
import { findAndInsertAfterBatch } from "~/app/add-in-word/wordUtils";
import { toast } from "~/components/snackbar";
import {
  selectGeneratedResponses,
  selectQuestionIdToText,
  selectResponseLibraryResponses,
} from "~/lib/features/documentDDQSelector";
import { useUserMSAddInSettings } from "./useUserMSAddInSettings";

export const useInsertAll = ({
  selectedIds,
  onSuccess,
  onError,
}: {
  selectedIds: string[];
  onSuccess: (count: number) => void;
  onError: (errorCount: number) => void;
}) => {
  const responseLibraryResponsesDic = useSelector(
    selectResponseLibraryResponses,
  );
  const generatedResponsesDic = useSelector(selectGeneratedResponses);
  const questionIdToTextDic = useSelector(selectQuestionIdToText);
  const { data: msAddInSettings, isLoading: userDataLoading } = useUserMSAddInSettings();

  const handleInsertAll = useCallback(
    async (e: React.MouseEvent<HTMLLIElement>) => {
      e.stopPropagation();
      const selectedQuestionResponseArray = selectedIds
        .map((id) => {
          const responseText =
            responseLibraryResponsesDic[id]?.responseMarkup ||
            generatedResponsesDic[id]?.responseMarkup ||
            "";
          const questionText = questionIdToTextDic[id];
          return {
            responseText,
            questionText,
            id,
          };
        })
        .filter(
          (qAndA) =>
            qAndA.responseText &&
            qAndA.responseText.length > 0 &&
            qAndA.responseText !== "Unable to generate response.",
        );

      try {
        const inputQandA = selectedQuestionResponseArray.map((qAndA) => ({
          text: qAndA.questionText ?? "",
          insertText: qAndA.responseText ?? "",
          id: qAndA.id,
        }));

        const response = await findAndInsertAfterBatch(inputQandA, msAddInSettings?.insertTextColor);
        if (response.failed > 0) {
          onError(response.failed);
        }
        if (response.success > 0) {
          onSuccess(response.success);
          return response;
        }
      } catch (error) {
        console.error(`Error inserting responses:`, error);
        toast.error(`Error inserting responses`);
        onError(selectedIds.length);
      }
    },
    [
      selectedIds,
      responseLibraryResponsesDic,
      generatedResponsesDic,
      questionIdToTextDic,
    ],
  );

  return handleInsertAll;
};
