import { useQueryClient } from "@tanstack/react-query";
import { useCallback } from "react";
import { useSelector } from "react-redux";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import { api } from "~/trpc/react";
import { useGetSingleAnswerQueryKey } from "./useGetSingleAnswer";

const useCreateResponse = ({
  questionId,
  questionText,
  ragResponseText,
  onSuccess,
  onError,
}: {
  questionId: string;
  questionText: string;
  ragResponseText: string;
  onSuccess: () => void;
  onError: () => void;
}) => {
  const queryClient = useQueryClient();
  const documentId = useSelector(selectDocumentId);
  const createResponse = api.response.createResponse.useMutation({
    meta: {
      skipInvalidateQueries: true,
    },
  });
  const singleAnswerQueryKey = useGetSingleAnswerQueryKey(questionText);

  const createResponseMutateAsync = useCallback(async () => {
    try {
      await createResponse.mutateAsync({
        questionId,
        documentId,
        content: ragResponseText,
      });

      if (createResponse.isSuccess) {
        onSuccess();
        // invalidate the response library response query for that question
        await queryClient.invalidateQueries({
          queryKey: singleAnswerQueryKey,
        });
      }
    } catch (error) {
      onError();
    }
  }, [
    createResponse,
    onSuccess,
    onError,
    questionId,
    documentId,
    singleAnswerQueryKey,
  ]);

  return { createResponseMutateAsync };
};

export default useCreateResponse;
