import * as React from 'react';
import Button from '@mui/material/Button';
import ButtonGroup from '@mui/material/ButtonGroup';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grow from '@mui/material/Grow';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import { ButtonProps } from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Box from '@mui/material/Box';
import { useSelector } from 'react-redux';
import { selectDocumentId } from '~/lib/features/documentSelectors';
import { api } from '~/trpc/react';
import SplitButtonWithPrompt from '../OfficeAddIn/SplitButtonWithPrompt';
import { toast } from '~/components/snackbar';



interface RegenerateBtnProps {
    questionId: string
}

export default function useRegenerateBtn({
    questionId,
}: RegenerateBtnProps) {
    const documentId = useSelector(selectDocumentId)
    const [regeneratedResponseText, setRegeneratedResponseText] = React.useState("")
    const { mutate: regenerate, isPending } = api.rag.generateDDQResponseWithUserPrompt.useMutation({
        onSuccess: (data) => {
            toast.success("Regenerated")
            setRegeneratedResponseText(data[0]?.answer ?? "")
        },
        onError: (error) => {
            toast.error("Error regenerating")
        }
    })

    const regenerateBtn = <SplitButtonWithPrompt
        variant="outlined"
        color="secondary"
        size="small"
        disabled={isPending}
        buttonLabel="Regenerate"
        inputConfig={{
            placeholder: "Enter prompt",
            buttonLabel: "Regenerate",
            onSubmit: (value) => {
                regenerate({
                    questions: [{
                        questionId,
                        customPrompt: value
                    }],
                    documentId,
                })
            }
        }}
    />

    return {
        regenerateBtn,
        regeneratedResponseText,
        isPending,
    }
}