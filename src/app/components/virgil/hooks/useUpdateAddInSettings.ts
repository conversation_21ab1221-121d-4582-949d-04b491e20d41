import { api } from "~/trpc/react";

/**
 * A custom hook that fetches user data using Clerk authentication.
 * 
 * @returns An object containing:
 * - isLoading: Boolean indicating if the user data is still being fetched
 * - data: User data including organizations they belong to, or undefined if not loaded
 */

export const useUpdateAddInSettings = () => {
  const { mutateAsync } = api.user.updateAddInSettings.useMutation();

  return { mutateAsync };
};
