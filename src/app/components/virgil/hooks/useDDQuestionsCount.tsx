import { DocumentStatus, QuestionCategory } from "@prisma/client";
import { useSelector } from "react-redux";
import { selectDocumentMetadataDocumentStatus } from "~/lib/features/documentMetadataSelectors";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import { api } from "~/trpc/react";

export const useDDQuestionsCount = () => {

  const documentId = useSelector(selectDocumentId);
  const documentStatus = useSelector(selectDocumentMetadataDocumentStatus);

  const defaultQueryInput = {
    category: [
      QuestionCategory.OTHER,
      QuestionCategory.DATA_ASSURANCE,
      QuestionCategory.STANDARDS_AND_FRAMEWORKS,
      QuestionCategory.INVESTMENT_PROCESS,
    ],
    documentId: documentId ?? "",
  };

  const querySettings = {
    enabled: !!documentId && documentStatus === DocumentStatus.READY,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5,
  };

  const { data } = api.question.getQuestionCountForDocument.useQuery(
    {
      ...defaultQueryInput,
    },
    querySettings,
  );

  return {
    newQuestions: data?.newQuestions,
    answeredQuestions: data?.answeredQuestions,
    totalQuestions: (data?.newQuestions ?? 0) + (data?.answeredQuestions ?? 0),
  };
};
