import { useMemo, useState } from "react";
import SplitButton from "../SplitButton";
import { QuestionStatusType } from "@prisma/client";
import { useUpdateQuestionStatusBatch } from "./useUpdateQuestionStatusBatch";
import { useInsertAll } from "./useInsertAll";
import { toast } from "~/components/snackbar";

type UseSelectActionProps = {
  selectedIds: string[];
  setSelectedIds: (ids: string[]) => void;
  tabType: QuestionStatusType;
}

export const useSelectAction = ({ selectedIds, setSelectedIds, tabType }: UseSelectActionProps) => {
  const [loading, setLoading] = useState(false);
  const handleInsertAll = useInsertAll({
    selectedIds, onSuccess: (count: number) => {
      toast.success(`${count} responses inserted`);
    }, onError: (errorCount: number) => {
      toast.error(`Error inserting ${errorCount} responses`);
    }
  });
  const batchUpdateQuestionStatus = useUpdateQuestionStatusBatch({
    onMutate: () => {
      setLoading(true);
    },
    onSuccess: (count: number) => {
      toast.success(`${JSON.stringify(count)} questions updated`);
      setLoading(false);
      setSelectedIds([]);
    },
    onError: (errorCount: number) => {
      toast.error(`Error updating ${errorCount} questions`);
      setLoading(false);
    }
  });

  const SplitButtonMemo = useMemo(() => {
    return <SplitButton
      mainLabel="Bulk Actions"
      disabled={loading}
      options={tabType === QuestionStatusType.NEW ? [
        // {
        //   label: `Insert (${selectedIds.length})`, onClick: async (e: React.MouseEvent<HTMLLIElement, MouseEvent>) => {
        //     e.preventDefault();
        //     const response = await handleInsertAll(e);

        //     await batchUpdateQuestionStatus.mutateAsync({
        //       ids: response?.insertedQuestionIds || [],
        //       status: QuestionStatusType.ANSWERED
        //     })
        //   }
        // },
        {
          label: `Mark as answered (${selectedIds.length})`, onClick: async (e) => {
            e.preventDefault();
            await batchUpdateQuestionStatus.mutateAsync({
              ids: selectedIds,
              status: QuestionStatusType.ANSWERED
            })
          }
        },
      ] : [
        {
          label: `Mark as new (${selectedIds.length})`, onClick: async (e) => {
            e.preventDefault();
            await batchUpdateQuestionStatus.mutateAsync({
              ids: selectedIds,
              status: QuestionStatusType.NEW
            })
          }
        },
      ]}
    />
  }, [loading, selectedIds, tabType]);

  return {
    SplitButtonMemo
  };
};
