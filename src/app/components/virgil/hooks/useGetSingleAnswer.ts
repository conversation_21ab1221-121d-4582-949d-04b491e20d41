import { useSelector } from "react-redux";
import { api } from "~/trpc/react";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import {
  selectSelectedTagIds,
  selectSelectedFundIds,
} from "~/lib/features/documentDDQSelector";

import {
  GetDocumentType,
  QuestionWithSearchType,
} from "~/server/api/routers/question";
import { getQueryKey } from "@trpc/react-query";

export const useGetSingleAnswerQueryKey = (questionText: string) => {
  const selectedFundIds = useSelector(selectSelectedFundIds);
  const selectedTagIds = useSelector(selectSelectedTagIds);
  return getQueryKey(
    api.question.similaritySearch,
    {
      query: questionText,
      fundIds: selectedFundIds,
      tagIds: selectedTagIds,
    },
    "query",
  );
};

export const useGetSingleAnswer = (
  questionText: string | null,
  enabled = true,
) => {
  const selectedFundIds = useSelector(selectSelectedFundIds);
  const selectedTagIds = useSelector(selectSelectedTagIds);
  const documentId = useSelector(selectDocumentId);
  const { data, isLoading, isSuccess } = api.question.similaritySearch.useQuery(
    {
      query: questionText ?? "",
      fundIds: selectedFundIds,
      tagIds: selectedTagIds,
    },
    {
      enabled:
        !!documentId && !!questionText && !!questionText?.trim() && enabled,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60,
    },
  );

  try {
    // find first answer
    const similarResponseData = data?.find((answer) =>
      answer?.response?.responseContents.find(
        (rc) => (rc.content as { text: string }).text !== "",
      ),
    )?.response as QuestionWithSearchType["response"];

    const similarResponse = similarResponseData?.responseContents?.[0];
    // faltten documents
    const documents = similarResponseData?.documents.map(
      (doc) => doc.document,
    ) as GetDocumentType[];

    const responseText =
      (similarResponse?.content as { text: string })?.text ?? null;

    return {
      data: similarResponseData,
      responseText,
      isLoading,
      isSuccess,
      responseContentId: similarResponse?.id,
      responseCanonicalId: similarResponseData?.id,
      documents,
      responseStatus: similarResponseData?.status,
    };
  } catch (error) {
    console.error(
      "Error getting single answer for question:",
      questionText,
      error,
    );
    return {
      data,
      responseText: null,
      isLoading,
      isSuccess,
      responseContentId: null,
      responseCanonicalId: null,
      documents: [],
      responseStatus: null,
    };
  }
};
