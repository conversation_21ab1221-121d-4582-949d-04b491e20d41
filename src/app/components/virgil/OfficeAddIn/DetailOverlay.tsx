import React from 'react';
import { Box, Paper, IconButton } from '@mui/material';
import LeftArrowIcon from '@mui/icons-material/KeyboardArrowLeft';
import { useSelector, useDispatch } from 'react-redux';
import { selectAddInNavigationPath } from '~/lib/features/addInUISelectors';
import { setAddInNavigationPath } from '~/lib/features/addInUISlice';
import DocumentDetails from './DocumentDetails';
import AddInSettings from './AddInSettings/AddInSettings';
import DDQListPaginatedContainer from './IndexView/DDQListPaginatedContainer';
import DetailedAnswerContainer from './DetailedView/DetailedAnswerContainer';
import DocumentStatusPage from './DocumentStatusPage';
import DocumentFeedbackStatusPage from './DocumentFeedbackStatusPage';

const DetailOverlay: React.FC = () => {
    const addInNavigationPath = useSelector(selectAddInNavigationPath);
    const dispatch = useDispatch();

    const handleClose = () => {
        dispatch(setAddInNavigationPath('ddq'));
    };

    const renderContent = () => {
        switch (addInNavigationPath) {
            case 'document-details':
                return <DocumentDetails />;
            case 'settings':
                return <AddInSettings />;
            case 'collaboration':
                return <>Collaboration</>;
            case 'alerts':
                return <>Alerts</>;
            case 'detailed-answer':
                return <DetailedAnswerContainer />;
            case 'document-status':
                return <DocumentStatusPage />;
            case 'document-feedback-status':
                return <DocumentFeedbackStatusPage />;
            default:
                return null;
        }
    };

    if (!addInNavigationPath || addInNavigationPath === 'ddq') {
        return null;
    }

    return (
        <Box
            sx={{
                position: 'fixed',
                top: '57px',
                left: 0,
                right: 0,
                bottom: 0,
                zIndex: 1000,
                backgroundColor: "white",
                p: 0,
                m: 0
            }}
        >
            {renderContent()}
        </Box>
    );
};

export default DetailOverlay;
