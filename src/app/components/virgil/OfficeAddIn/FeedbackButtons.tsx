import { api } from "~/trpc/react";
import { toast } from "~/components/snackbar";
import { useCallback, useEffect, useMemo, useState } from "react";
import { ResponseFeedbackType } from "@prisma/client";
import { Box, Button, CircularProgress, debounce, Typography } from "@mui/material";
import { Iconify } from "~/components/iconify";
import { useSelector } from "react-redux";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import { AggregatedFeedback } from "~/server/api/managers/feedbackManager";
import { useQueryClient } from "@tanstack/react-query";
import { getQueryKey } from "@trpc/react-query";

const getFeedbackChange = (feedback: AggregatedFeedback | null, type: ResponseFeedbackType): { goodChange: number, badChange: number } => {
  let goodChange = 0;
  let badChange = 0;
  if (!feedback?.self) {
    if (type === ResponseFeedbackType.GOOD) {
      goodChange = 1;
    } else if (type === ResponseFeedbackType.BAD) {
      badChange = 1;
    }
  }
  if (feedback?.self !== type) {
    if (type === ResponseFeedbackType.GOOD) {
      goodChange = 1;
      badChange = -1;
    } else if (type === ResponseFeedbackType.BAD) {
      badChange = 1;
      goodChange = -1;
    }
  }
  return { goodChange, badChange };
}

export const FeedbackButtons = ({
  questionId,
  responseId,
  responseText,
  reason,
  goodCountProp,
  badCountProp,
  selfFeedbackProp,
  size = "small",
}: {
  questionId: string;
  responseId?: string;
  responseText?: string;
  reason?: string;
  goodCountProp: number;
  badCountProp: number;
  selfFeedbackProp: ResponseFeedbackType | null;
  size?: "small" | "medium" | "large";
}) => {
  const documentId = useSelector(selectDocumentId);
  const queryClient = useQueryClient();
  const [goodCount, setGoodCount] = useState<number>(goodCountProp ?? 0);
  const [badCount, setBadCount] = useState<number>(badCountProp ?? 0);
  const [selfFeedback, setSelfFeedback] = useState<ResponseFeedbackType | null>(selfFeedbackProp);

  const { mutate } = api.feedback.createorUpdate.useMutation({
    onMutate: async (data: {
      questionId: string;
      type: ResponseFeedbackType;
    }) => {
      const previousFeedback = {
        good: goodCount,
        bad: badCount,
        self: selfFeedback,
      }
      const infiniteQueries = queryClient.getQueryCache().findAll({
        queryKey: getQueryKey(
          api.question.getAllQuestionsInfinite,
          undefined,
          "any",
        ),
      });

      await Promise.all(infiniteQueries.map((query) =>
        query.cancel(),
      ));

      const previousQueries = [] as { queryKey: any; state: any }[];

      // Update cache for all matching queries
      infiniteQueries.forEach((query) => {
        const previousData = queryClient.getQueryData(query.queryKey) as { pages: { items: any[] }[] } | undefined;
        if (!previousData) {
          return;
        }

        previousQueries.push({
          queryKey: query.queryKey,
          state: queryClient.getQueryData(query.queryKey),
        });

        const newData = {
          ...previousData,
          pages: previousData.pages.map((page) => {
            return {
              ...page,
              items: page.items.map((item) => {
                if (item.id === data.questionId) {
                  const { goodChange, badChange } = getFeedbackChange(item.feedback, data.type);

                  return {
                    ...item,
                    feedback: {
                      good: Math.max(0, (item.feedback?.good ?? 0) + goodChange),
                      bad: Math.max(0, (item.feedback?.bad ?? 0) + badChange),
                      self: data.type,
                    },
                  };
                }
                return item;
              }),
            };
          }),
        };
        queryClient.setQueryData(query.queryKey, newData);
      });

      return { previousQueries, previousFeedback };
    },
    onSuccess: () => {
    },
    onError: (error, variables, context) => {
      toast.error(`Error creating feedback: ${JSON.stringify(error)}`); // @ts-ignore
      context?.previousQueries?.forEach((query) => {
        if (query) {
          queryClient.setQueryData(query.queryKey, query.state.data);
        }
      });
      setGoodCount(context?.previousFeedback?.good ?? 0);
      setBadCount(context?.previousFeedback?.bad ?? 0);
      setSelfFeedback(context?.previousFeedback?.self ?? null);
    },
    onSettled: () => {
    },
    meta: {
      skipInvalidateQueries: true,
    },
  });

  const createFeedback =
    (type: ResponseFeedbackType) => {
      if (selfFeedback === type) {
        return;
      }
      const { goodChange, badChange } = getFeedbackChange({
        good: goodCount,
        bad: badCount,
        self: selfFeedback,
      }, type);

      setGoodCount((prev) => Math.max(0, prev + goodChange));
      setBadCount((prev) => Math.max(0, prev + badChange));
      setSelfFeedback(type);

      mutate({
        documentId,
        questionId,
        type,
        responseText: responseText ?? "",
        reason: reason ?? "",
      });
    };

  const debouncedCreateFeedback = debounce(createFeedback, 300);

  const createGoodFeedback = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    e.preventDefault();
    void debouncedCreateFeedback(ResponseFeedbackType.GOOD);
  };

  const createBadFeedback = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    e.preventDefault();
    void debouncedCreateFeedback(ResponseFeedbackType.BAD);
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Button
        variant={selfFeedback === ResponseFeedbackType.GOOD ? "contained" : "outlined"}
        color="primary"
        size={size}
        // disabled={aggregatedFeedback.isPending}
        title="This response was helpful"
        onClick={createGoodFeedback}>
        &nbsp;
        <Iconify
          icon="tabler:thumb-up-filled"
          width={16}
        />
        &nbsp;
        {goodCount > 0 && (
          <Typography variant="caption" sx={{ ml: 0.5 }}>
            {goodCount}
          </Typography>
        )}
      </Button>
      <Button
        variant={selfFeedback === ResponseFeedbackType.BAD ? "contained" : "outlined"}
        color="primary"
        size={size}
        // disabled={aggregatedFeedback.isPending}
        title="This response was not helpful"
        onClick={createBadFeedback}>
        &nbsp;
        <Iconify
          icon="tabler:thumb-down-filled"
          width={16}
        />
        &nbsp;
        {badCount > 0 && (
          <Typography variant="caption" sx={{ ml: 0.5 }}>
            {badCount}
          </Typography>
        )}
      </Button>
    </Box>
  );
};
