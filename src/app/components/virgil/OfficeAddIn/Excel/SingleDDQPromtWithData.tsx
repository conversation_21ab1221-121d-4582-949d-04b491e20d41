import * as Sentry from "@sentry/react";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    Box,
    Button,
    Checkbox,
    Collapse,
    Stack,
    Typography,
    useTheme,
} from "@mui/material";
import { useRagResponse } from "../../hooks/useRagResponse";
import { CircleLoading } from "../CircleLoading";
import { setGeneratedResponseText, setQuestionIdToText, setSelectedQuestionId, setSelectedQuestionText } from "~/lib/features/documentDDQSlice";
import { Markdown } from "../Markdown";
import { convertMarkdownToPreview } from "../markdownUtils/markdownUtils";
import { AIGeneratedResponseIconWithBackground } from "../../Icons/AIGeneratedResponseIcon";
import { AIGeneratedResponseLabel } from "../../Labels/AIGeneratedResponseLabel";
import { useUserMSAddInSettings } from "../../hooks/useUserMSAddInSettings";
import { insertValue, selectRange } from "~/app/add-in-excel/utils";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import { Label } from "~/components/label/label";
import { setDisplayDetailedAnswer } from "~/lib/features/addInUISlice";
import { setCellRange } from "~/lib/features/documentSlice";

type LoadingTextProps = {
    text: string;
}
const LoadingText: React.FC<LoadingTextProps> = ({ text }: LoadingTextProps) => {
    return (
        <Typography
            variant="body2"
            color="text.secondary">{text}</Typography>
    )
}

type SingleDDQPromtWithDataProps = {
    questionText: string;
    questionId: string;
    expanded: boolean;
    address: string;
    handleToggle: (id: string) => void;
};

const SingleDDQPromtWithData = memo(({
    questionText,
    questionId,
    expanded,
    handleToggle,
    address,
}: SingleDDQPromtWithDataProps) => {
    const theme = useTheme();
    const dispatch = useDispatch();
    const documentId = useSelector(selectDocumentId);
    const [hideState, setHideState] = useState(false);
    const [loading, setLoading] = useState(false);
    const { data: msAddInSettings } = useUserMSAddInSettings();

    const {
        responseText: ragResponseText,
        isLoading: isRagResponseLoading,
        refetch: refreshRagResponse
    } = useRagResponse(questionText, true)

    const handleToggleCallback = useCallback(async (e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        await selectRange({ address });
        handleToggle(questionId);
    }, [questionText, expanded, questionId, handleToggle]);
    const handleRefresh = useCallback(async (e: React.MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation();
        await refreshRagResponse();
    }, [refreshRagResponse]);
    const handleInsert = useCallback(async (e: React.MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation();
        setLoading(true);
        await insertValue({
            documentId,
            text: ragResponseText ?? "",
            rangeAddress: address
        });
        Sentry.startSpan(
            {
                name: "Excel Plugin Response",
                op: "plugin.excel.response",
            },
            async (span) => {
                span.setAttribute("action", "insert");
                span.setAttribute("question.id", questionId ?? "");
            },
        )
        setLoading(false);

    }, [setLoading, insertValue, documentId, ragResponseText, address]);

    const ragResponseTextMemoized = useMemo(
        () => {
            if (expanded) {
                return (ragResponseText ? <Markdown onClick={(e) => e.stopPropagation()} markdown={ragResponseText} className="add-in" /> : null)
            }

            return (
                ragResponseText ? (
                    <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitLineClamp: 1,
                            WebkitBoxOrient: "vertical",
                        }}
                    >
                        {convertMarkdownToPreview(ragResponseText ?? "")}
                    </Typography>) : null
            )
        },
        [ragResponseText, expanded, convertMarkdownToPreview],
    );

    const SourceResponseIcon = useMemo(() => {
        if (isRagResponseLoading) {
            return <CircleLoading size={15} />
        } else {
            return <AIGeneratedResponseIconWithBackground />
        }
    }, [isRagResponseLoading])

    // populate store with rag response
    useEffect(() => {
        dispatch(setGeneratedResponseText({
            id: questionId, response: {
                responseMarkup: ragResponseText ?? "",
                isLoading: isRagResponseLoading,
            }
        }));
    }, [isRagResponseLoading, ragResponseText]);

    const handleView = useCallback(() => {
        dispatch(setDisplayDetailedAnswer());
        dispatch(setSelectedQuestionText(questionText));
        dispatch(setSelectedQuestionId(questionId));
        dispatch(setCellRange(address));
    }, [questionText, questionId, dispatch, setDisplayDetailedAnswer, setSelectedQuestionText, setSelectedQuestionId]);

    useEffect(() => {
        dispatch(setQuestionIdToText({
            id: questionId,
            text: questionText,
        }));
    }, [questionText]);


    useEffect(() => {
        // testing unmounts
        return () => {
            console.log("unmounting SingleIndexQuestion");
        };
    }, []);

    return (
        <Box
            sx={{
                width: "100%",
                padding: 1,
                paddingLeft: !expanded ? 4.5 : undefined,
                borderBottom: "1px solid #ddd",
                cursor: "pointer",
                borderLeft: "5px solid transparent",
                position: "relative",
                "&:hover": {
                    backgroundColor: theme.palette.action.hover,
                    borderLeft: `5px solid ${theme.palette.primary.main}`,
                },
                transition: "transform 0.5s ease-out, height 0.5s ease-out, opacity 0.5s ease-out",
                transform: hideState ? "translateX(100%)" : "translateX(0)",
                opacity: hideState ? 0 : 1,
                height: hideState ? 0 : undefined,
                marginBottom: hideState ? 0 : undefined,
            }}
            onClick={handleToggleCallback}
            id={`ddq-prompt-${questionId}`}
        >
            {!expanded && <Checkbox
                // checked={isChecked}
                // onChange={(e) => {
                //     e.stopPropagation();
                //     setIsChecked(questionId);
                // }}
                onClick={(e) => e.stopPropagation()}
                size="small"
                sx={{ pl: 0.5, pr: 0, position: "absolute", left: 3, top: 0 }}
            />}
            <Typography
                variant="h6"
                sx={{
                    overflow: expanded ? "visible" : "hidden",
                    textOverflow: expanded ? undefined : "ellipsis",
                    display: expanded ? "block" : "-webkit-box",
                    WebkitLineClamp: expanded ? undefined : 2,
                    WebkitBoxOrient: "vertical",
                    paddingRight: expanded ? undefined : 4,
                }}
            >
                {questionText}
            </Typography>
            {!expanded && <Box sx={{ position: "absolute", right: 12, top: 8 }}>{SourceResponseIcon}</Box>}
            <Box sx={{
                maxHeight: "300px",
                overflowY: "auto",
                border: expanded ? "1px solid #ddd" : undefined,
                backgroundColor: expanded ? "#F4F4F6" : undefined,
                mt: expanded ? 2 : undefined,
                p: expanded ? 1 : undefined,
                pr: expanded ? undefined : 5,
                borderRadius: 1,
            }}>
                {expanded && (
                    <Stack direction="row" alignItems="left" spacing={1}>
                        <AIGeneratedResponseLabel />
                        <Label color="secondary">{address}</Label>
                    </Stack>
                )}
                {!expanded && (<Label color="secondary">{address}</Label>)}
                {isRagResponseLoading ? <LoadingText text="Generating answer..." /> : ragResponseTextMemoized}
            </Box>
            <Collapse in={expanded}>
                <Box sx={{ mt: 2 }}>
                    {!isRagResponseLoading && (
                        <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                            <Button
                                variant="contained"
                                color="primary"
                                onClick={(e) => handleInsert(e)}
                                size="small"
                                disabled={loading}
                                loading={loading}
                            >
                                Insert
                            </Button>
                            <Button
                                variant="contained"
                                color="primary"
                                onClick={(e) => handleRefresh(e)}
                                size="small"
                                disabled={isRagResponseLoading}
                                loading={isRagResponseLoading}
                            >
                                Refresh response
                            </Button>
                            <Button
                                variant="outlined"
                                color="secondary"
                                size="small"
                                onClick={handleView}
                                disabled={loading}
                            >
                                View
                            </Button>
                            <Button
                                variant="outlined"
                                color="secondary"
                                size="small"
                                disabled={loading}
                                onClick={handleToggleCallback}
                            >
                                Close
                            </Button>
                        </Box>
                    )}
                </Box>
            </Collapse>
        </Box>
    );
});

export default SingleDDQPromtWithData;
