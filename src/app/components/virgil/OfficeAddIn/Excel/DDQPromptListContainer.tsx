import React from "react";
import DDQPromptList from "./DDQPromptList";
import { useDDQExcelPropts } from "../../hooks/useDDQExcelPropts";
import { ActionsBar } from "../IndexView/ActionsBar";

const DDQPromptListContainer: React.FC = () => {

    const { prompts, isLoading } = useDDQExcelPropts();

    return (
        <>
            <ActionsBar />
            <DDQPromptList
                prompts={prompts}
                noPromptsText="No due diligence prompts found, please re-process document in the Data Room"
                isLoading={isLoading}
            />
        </>
    );
};

export default DDQPromptListContainer;
