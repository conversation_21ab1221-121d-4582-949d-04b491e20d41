import {
  <PERSON>,
  Button,
  Stack,
  Typography,
} from "@mui/material";
import { AIGeneratedResponseLabel } from "../../Labels/AIGeneratedResponseLabel";
import { CircleLoading } from "../CircleLoading";
import { Markdown } from "../Markdown";
import { Label } from "~/components/label";
const MarkdownWithLoading = ({
  markdown,
  fallbackText,
  isLoading,
}: {
  markdown: string;
  fallbackText: string;
  isLoading: boolean;
}) => {
  if (isLoading) {
    return (
      <Typography variant="body2" color="text.secondary">
        {fallbackText}
        <CircleLoading size={16} />
      </Typography>
    );
  }

  return <Markdown markdown={markdown} className="add-in" />;
};

type EditorProps = {
  questionText: string;
  address: string;
  handleInsert: (e: React.MouseEvent<HTMLButtonElement>) => void;
  // rag response
  ragResponseText: string;
  isRagResponseLoading: boolean;
};
const PromptEditor = ({
  questionText,
  address,
  handleInsert,
  ragResponseText,
  isRagResponseLoading,
}: EditorProps) => {

  return (
    <>
      <Box
        sx={{ height: "calc(100vh - 55px - 50px - 70px)", overflow: "auto" }}
      >
        <Box sx={{ p: 1 }}>
          <Typography variant="h6">{questionText}</Typography>
        </Box>
        <Box sx={{ p: 1 }}>
          <Box
            sx={{
              border: "1px solid #ddd",
              backgroundColor: "#F4F4F6",
              borderRadius: 1,
              p: 1,
            }}
          >
            <Stack direction="row" alignItems="left" spacing={1}>
              <AIGeneratedResponseLabel />
              <Label color="secondary">{address}</Label>
            </Stack>
            <MarkdownWithLoading
              markdown={ragResponseText ?? ""}
              fallbackText="Generating with LLM..."
              isLoading={isRagResponseLoading}
            />
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          mt: 2,
          position: "sticky",
          bottom: 0,
          bgcolor: "background.paper",
          p: 2,
          borderTop: 1,
          borderColor: "divider",
          height: "70px",
          display: "flex",
          gap: 1,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Button
          variant="contained"
          color="primary"
          fullWidth
          onClick={handleInsert}
        >
          Insert Response
        </Button>
      </Box>
    </>
  );
};

export default PromptEditor;
