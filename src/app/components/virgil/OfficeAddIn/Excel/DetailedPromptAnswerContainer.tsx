import * as Sentry from "@sentry/react";
import { useCallback, useState } from "react";
import { useSelector } from "react-redux";
import {
  selectSelectedQuestionId,
  selectSelectedQuestionText,
} from "~/lib/features/documentDDQSelector";
import { useRagResponse } from "../../hooks/useRagResponse";
import { useUserMSAddInSettings } from "../../hooks/useUserMSAddInSettings";
import { selectActiveCellRange, selectDocumentId } from "~/lib/features/documentSelectors";
import DetailedAnswerTwoTab from "../DetailedView/DetailedAnswerTwoTab";
import DetailsTabContent from "../DetailedView/DetailsTabContent";
import { insertValue } from "~/app/add-in-excel/utils";
import PromptEditor from "./PromptEditor";
import { Box } from "@mui/material";

const DetailedAnswerContainer: React.FC = () => {
  const questionText = useSelector(selectSelectedQuestionText);
  const questionId = useSelector(selectSelectedQuestionId);
  const documentId = useSelector(selectDocumentId);
  const address = useSelector(selectActiveCellRange);
  const [loading, setLoading] = useState(false);
  const {
    responseText: ragResponseText,
    data: ragResponseData,
    isLoading: isRagResponseLoading,
    citations: ragCitations,
  } = useRagResponse(
    questionText ?? "",
    true
  );

  const { data: msAddInSettings, isLoading: userDataLoading } =
    useUserMSAddInSettings();

  const handleInsert = useCallback(async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setLoading(true);
    await insertValue({
      documentId,
      text: ragResponseText ?? "",
      rangeAddress: address
    });
    Sentry.startSpan(
      {
        name: "Excel Plugin Response",
        op: "plugin.excel.response",
      },
      async (span) => {
        span.setAttribute("action", "insert");
        span.setAttribute("question.id", questionId ?? "");
      },
    )
    setLoading(false);

  }, [setLoading, insertValue, documentId, ragResponseText, address]);

  if (!questionText || !questionId || userDataLoading) {
    return null;
  }

  return (
    <Box sx={{ height: "calc(100vh - 70px)", overflow: "auto" }}>
      <DetailedAnswerTwoTab
        tab1Label="Editor"
        tab1Component={
          <PromptEditor
            questionText={questionText}
            address={address}
            handleInsert={handleInsert}
            ragResponseText={ragResponseText ?? ""}
            isRagResponseLoading={isRagResponseLoading}
          />
        }
        tab2Label="Citations"
        tab2Component={
          <DetailsTabContent
            ragCitations={ragCitations ?? []}
            similarResponseDocuments={[]}
            similarResponseCanonicalId={""}
          />
        }
      />
    </Box>
  );
};

export default DetailedAnswerContainer;
