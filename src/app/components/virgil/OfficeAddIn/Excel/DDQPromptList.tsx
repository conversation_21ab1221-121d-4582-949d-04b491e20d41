import React, { FC, useCallback, useEffect, useState } from "react";
import { Box } from "@mui/material";
import SingleDDQPromtWithData from "./SingleDDQPromtWithData";
import { DDQPrompt } from "~/lib/features/documentDDQSlice";
import InfoBox from "../IndexView/InfoBox";
import { useSelector } from "react-redux";
import { selectActiveCellRange } from "~/lib/features/documentSelectors";

export type DDQPromptListProps = {
    prompts: DDQPrompt[];
    noPromptsText: string;
    isLoading: boolean;
}

const DDQPromptList: FC<DDQPromptListProps> = ({ prompts, noPromptsText, isLoading }: DDQPromptListProps) => {
    const [expandedId, setExpandedId] = useState<string>("");
    const handleToggle = useCallback((id: string) => {
        setExpandedId(prev => prev === id ? "" : id);
    }, [setExpandedId]);
    const activeCellRangeAddress = useSelector(selectActiveCellRange);
    useEffect(() => {
        if (activeCellRangeAddress && prompts.length > 0) {
            const matchingPrompt = prompts.find(p => p.address === activeCellRangeAddress);
            if (matchingPrompt && expandedId !== matchingPrompt.id) {
                setExpandedId(matchingPrompt.id);
                const element = document.getElementById(`ddq-prompt-${matchingPrompt.id}`);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        }
    }, [activeCellRangeAddress, prompts]);

    let warningText = '';

    if (isLoading) {
        warningText = 'Loading prompts...';
    } else {
        if (prompts.length === 0) {
            warningText = noPromptsText;
        }
    }

    if (warningText) {
        return (
            <Box sx={{ overflow: "auto", height: 'calc(100vh - 55px - 50px - 48px)' }}>
                <InfoBox text={warningText} />
            </Box>
        )
    }

    return (
        <>

            <Box sx={{ overflow: "auto", height: "calc(100vh - 55px - 60px)", position: 'absolute', top: "115px", left: 0 }}>
                {prompts.map((prompt: DDQPrompt) => (
                    <SingleDDQPromtWithData
                        key={prompt.id}
                        questionText={prompt.text}
                        questionId={prompt.id}
                        expanded={expandedId === prompt.id}
                        handleToggle={handleToggle}
                        address={prompt.address}
                    />
                ))}
            </Box>
        </>
    )
}

export default DDQPromptList;