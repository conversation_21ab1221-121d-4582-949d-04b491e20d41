import { Box, Button, Typography, useTheme } from "@mui/material";
import { useState } from "react";
import { Iconify } from "~/components/iconify";

type ReasonProps = {
    reason: string;
}

const Reason = ({ reason }: ReasonProps) => {
    const [expanded, setExpanded] = useState(false);

    const handleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setExpanded(!expanded);
    };
    const theme = useTheme();

    return (
        <Box sx={{ fontSize: theme.vars.font.body2 }}>
            <Button
                onClick={handleClick}
                variant="text"
                size="small"
                sx={{ textTransform: 'none', p: 1, pl: 0, minWidth: 0, cursor: 'pointer', color: theme.palette.text.secondary }}
            >
                {expanded ? (
                    <Iconify
                        icon="mdi:chevron-down"
                        color={theme.palette.text.secondary}
                    />
                ) : (
                    <Iconify
                        icon="mdi:chevron-right"
                        color={theme.palette.text.secondary}
                    />
                )}
                Reason
            </Button>

            {expanded && (
                <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                        fontStyle: "italic",
                        mt: 1
                    }}
                >
                    {reason}
                </Typography>
            )}
        </Box>
    );
};

export default Reason;
