import React, { PropsWithChildren } from "react";
import { Box } from "@mui/material";
import { useOfficeNavigation } from "../hooks/useOfficeMenu";
import DocumentStatus from "./DocumentStatus";
import GlobalLoading from "./GlobalLoading";
import LaunchChat from "./LaunchChat";

const OfficeAddInContainer: React.FC<PropsWithChildren> = ({ children }) => {
    // fake navigation b/c MS add in doesn't support window.history.pushState which is used in next.js
    const { Menu, menuToggleButton, backButton } = useOfficeNavigation();
    return (
        <Box sx={{ position: 'relative', height: '100vh' }}>
            <Box sx={{
                position: 'sticky',
                top: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                flexDirection: 'row',
                backgroundColor: '#fff',
                padding: '10px',
                paddingRight: '50px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                zIndex: 1000,
                height: '55px'
            }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {backButton ?? menuToggleButton}
                    <DocumentStatus />
                    <GlobalLoading />
                </Box>
                <LaunchChat />
            </Box>
            {/* make it a child responsibility to handle scrolling, this helps with the sticky header and spacing */}
            <Box sx={{ display: 'flex', height: 'calc(100vh - 55px)', overflow: 'hidden' }}>
                {children}
            </Box>
            {Menu}
        </Box>
    );
};

export default OfficeAddInContainer;
