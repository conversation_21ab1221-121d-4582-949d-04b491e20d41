import React from "react";
import { Box, Checkbox, Typography } from "@mui/material";

type QuestionActionsProps = {
    totalSelected: number;
    totalSelectedPerPage: number;
    totalCountPerPage: number;
    isAllSelected: boolean;
    handleSelectAll: (e: React.ChangeEvent<HTMLInputElement>) => void;
    actionButton: React.ReactNode;
};

const QuestionActions: React.FC<QuestionActionsProps> = ({
    totalSelectedPerPage,
    totalCountPerPage,
    isAllSelected,
    handleSelectAll,
    totalSelected,
    actionButton,
}) => {

    return (
        <>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Checkbox
                        sx={{ pl: 0.5, pr: 0 }}
                        checked={isAllSelected}
                        onChange={handleSelectAll}
                        size="small"
                    />
                    <Typography variant="h6">{totalSelectedPerPage} of {totalCountPerPage} selected:</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'right', gap: 1 }}>
                    {totalSelected > 0 && actionButton}
                </Box>
            </Box>
        </>
    )
}

export default QuestionActions;