import React from "react";
import { QuestionStatusType } from "@prisma/client";
import { StatusFilterTabs } from "./StatusFilterTabs";
import { ActionsBar } from "./ActionsBar";
import { selectQuestionStatusFilter } from "~/lib/features/documentDDQSelector";
import { useSelector } from "react-redux";
import DDQListAnswered from "./DDQListAnswered";
import DDQListPaginatedNew from "./DDQListNew";

const DDQListPaginatedContainer: React.FC = () => {
  const questionStatusFilter = useSelector(selectQuestionStatusFilter);
  return (
    <>
      <StatusFilterTabs />
      <ActionsBar />
      {questionStatusFilter === QuestionStatusType.NEW && <DDQListPaginatedNew />}
      {questionStatusFilter === QuestionStatusType.ANSWERED && <DDQListAnswered />}
    </>
  );
};

export default DDQListPaginatedContainer;
