import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    Box,
    Button,
    Checkbox,
    Collapse,
    Stack,
    Typography,
    useTheme,
} from "@mui/material";
import {
    findAndInsertAfter,
    scrollToTextFuzzy
} from "~/app/add-in-word/wordUtils";
import {
    setDisplayDetailedAnswer,
} from "~/lib/features/addInUISlice";
import { setQuestionIdToText, setResponseLibraryText, setSelectedQuestionId, setSelectedQuestionObject, setSelectedQuestionText } from "~/lib/features/documentDDQSlice";
import { Markdown } from "../Markdown";
import { convertMarkdownToPreview } from "../markdownUtils/markdownUtils";
import { toast } from "~/components/snackbar";
import { AnswerGenerationType, QuestionStatusType } from "@prisma/client";
import { useUpdateQuestionStatus } from "../../hooks/useUpdateQuestionStatus";
import { AIGeneratedResponseIconWithBackground } from "../../Icons/AIGeneratedResponseIcon";
import { LibraryResponseIconWithBackground } from "../../Icons/LibraryResponseIcon";
import { SourcedResponseLabel } from "../../Labels/SorcedResponseLabel";
import { AIGeneratedResponseLabel } from "../../Labels/AIGeneratedResponseLabel";
import ResponseStatusLabel from "../../ResponseStatusLabel/ResponseStatusLabel";
import { useUserMSAddInSettings } from "../../hooks/useUserMSAddInSettings";
import Reason from "../Reason";
import { useGetExistingResponseById } from "../../hooks/useGetExistingResponseById";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import { FeedbackButtons } from "../FeedbackButtons";
import SplitButton from "../SplitButton";
import { DDQuestionWithFeedback } from "~/server/api/managers/questionManager";
import SplitButtonWithPrompt from "../SplitButtonWithPrompt";
import useRegenerateBtn from "../../hooks/useRegenerateBtn";

type LoadingTextProps = {
    text: string;
}
const LoadingText: React.FC<LoadingTextProps> = ({ text }: LoadingTextProps) => {
    return (
        <Typography
            variant="body2"
            color="text.secondary">{text}</Typography>
    )
}

type SingleDDQWithDataType = {
    expanded: boolean;
    handleToggle: (id: string) => void;
    isChecked: boolean;
    setIsChecked: (questionId: string) => void;
    question: DDQuestionWithFeedback;
    index: number;
    measureElement: (node: any) => void;
};

const SingleDDQWithData = memo(({
    expanded,
    handleToggle,
    setIsChecked,
    isChecked,
    question,
    index,
    measureElement,
}: SingleDDQWithDataType) => {
    const theme = useTheme();
    const ref = useRef<HTMLDivElement>(null);
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const { data: msAddInSettings, isLoading: userDataLoading } = useUserMSAddInSettings();
    const updateQuestionStatusMutation = useUpdateQuestionStatus({
        onSuccess: () => {
            setLoading(false);
        },
        onError: (error: any) => {
            setLoading(false);
            toast.error(`Error updating question status: ${error}`);
        },
    });

    // At the moment, fetching the answer is done in the individual component.
    const { text: questionText, id: questionId, response } = question;
    const answerGenerationType = response?.responseContents?.[0]?.answerGenerationType
    const existingResponseId = (response?.responseContents?.[0]?.content as { existingResponses: { responseId: string }[] }).existingResponses?.[0]?.responseId;
    const existingResponseReason = (response?.responseContents?.[0]?.content as { existingResponses: { reason: string }[] }).existingResponses?.[0]?.reason;
    const responseReason = (response?.responseContents?.[0]?.content as { reason: string }).reason ?? existingResponseReason ?? "";
    const responseStatus = response?.status;
    const responseTextGenerated = (response?.responseContents?.[0]?.content as { text: string }).text;

    const { regenerateBtn, regeneratedResponseText, isPending: isGenerating } = useRegenerateBtn({
        questionId,
    });

    const { data: responseData, isLoading: responseLoading, responseText: responseTextExisting } = useGetExistingResponseById(existingResponseId ?? null);

    const responseText = useMemo(() => {
        if (isGenerating) {
            return "Generating..."
        }
        if (regeneratedResponseText) {
            return regeneratedResponseText;
        }
        if (answerGenerationType === AnswerGenerationType.EXTRACTED) {
            return responseTextExisting ?? "";
        }
        return responseTextGenerated ?? "";
    }, [responseTextGenerated, responseTextExisting, answerGenerationType, regeneratedResponseText, isGenerating]);

    const handleToggleCallback = useCallback(async (e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        handleToggle(questionId);
        await scrollToTextFuzzy(questionText);
    }, [questionText, expanded, questionId, handleToggle]);

    const handleInsert = useCallback((insertype: "AfterQuestion" | "AtCursor") => async (e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        if (loading) return;
        try {
            setLoading(true);
            await findAndInsertAfter(
                questionText,
                responseText,
                msAddInSettings?.insertTextColor ?? 'green',
                msAddInSettings?.fontSize,
                msAddInSettings?.bold,
                msAddInSettings?.italic,
                msAddInSettings?.underline,
                insertype
            );
            // await scrollToTextFuzzy(questionText);
            toast.success("Response inserted");
            await updateQuestionStatusMutation.mutateAsync({
                id: questionId,
                newStatus: QuestionStatusType.ANSWERED,
                oldStatus: question.status,
            });
            setLoading(false);
        } catch (error) {
            setLoading(false);
            toast.error(`Error inserting response: ${error}`);
        }

    }, [questionText, msAddInSettings?.insertTextColor, questionId, responseText]);

    const handleView = useCallback((e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        e.preventDefault();
        dispatch(setDisplayDetailedAnswer());
        dispatch(setSelectedQuestionObject(question));
    }, [questionText, questionId, dispatch, setDisplayDetailedAnswer, setSelectedQuestionText, setSelectedQuestionId]);

    const SourceResponseLabel = useMemo(() => {
        if (answerGenerationType === AnswerGenerationType.EXTRACTED) {
            return <SourcedResponseLabel />
        }
        if (answerGenerationType === AnswerGenerationType.GENERATED) {
            return <AIGeneratedResponseLabel />
        }

        return null;
    }, [answerGenerationType])

    const SourceResponseIcon = useMemo(() => {
        if (answerGenerationType === AnswerGenerationType.EXTRACTED) {
            return <LibraryResponseIconWithBackground />
        }
        if (answerGenerationType === AnswerGenerationType.GENERATED) {
            return <AIGeneratedResponseIconWithBackground />
        }

        return null;
    }, [answerGenerationType])

    return (
        <Box
            data-index={index}
            ref={ref}
            key={questionId}
            id={`ddq-${questionId}`}
            sx={{
                width: "100%",
                padding: 1,
                paddingLeft: !expanded ? 4.5 : undefined,
                borderBottom: "1px solid #ddd",
                minHeight: "57px",
                cursor: "pointer",
                borderLeft: "5px solid transparent",
                position: "relative",
                "&:hover": {
                    backgroundColor: theme.palette.action.hover,
                    borderLeft: `5px solid ${theme.palette.primary.main}`,
                },
            }}
            onClick={handleToggleCallback}
        >
            {!expanded && <Checkbox
                checked={isChecked}
                onChange={(e) => {
                    e.stopPropagation();
                    setIsChecked(questionId);
                }}
                onClick={(e) => e.stopPropagation()}
                size="small"
                sx={{ pl: 0.5, pr: 0, position: "absolute", left: 3, top: 0 }}
            />}
            <Typography
                variant="h6"
                sx={{
                    overflow: expanded ? "visible" : "hidden",
                    textOverflow: expanded ? undefined : "ellipsis",
                    display: expanded ? "block" : "-webkit-box",
                    WebkitLineClamp: expanded ? undefined : 1,
                    WebkitBoxOrient: "vertical",
                    paddingRight: expanded ? undefined : 4,
                }}
            >
                {questionText}
            </Typography>
            {!expanded && <Box sx={{ position: "absolute", right: 12, top: 8 }}>{SourceResponseIcon}</Box>}
            <Box sx={{
                maxHeight: "300px",
                overflowY: "auto",
                border: expanded ? "1px solid #ddd" : undefined,
                backgroundColor: expanded ? "#F4F4F6" : undefined,
                mt: expanded ? 2 : undefined,
                p: expanded ? 1 : undefined,
                pr: expanded ? undefined : 5,
                borderRadius: 1,
            }}>
                {expanded && (
                    <>
                        <Stack direction="row" alignItems="left" spacing={1}>
                            {SourceResponseLabel}
                            {responseStatus && (<ResponseStatusLabel responseStatus={responseStatus} />)}
                        </Stack>
                        <Markdown markdown={responseText} className="add-in" />
                    </>
                )}
                {!expanded && (
                    <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            display: "-webkit-box",
                            WebkitLineClamp: 1,
                            WebkitBoxOrient: "vertical",
                        }}
                    >
                        {convertMarkdownToPreview(responseText)}
                    </Typography>
                )}
            </Box>
            <Collapse in={expanded} timeout={50} addEndListener={(node, done) => {
                measureElement(ref.current);
                done();
            }}>
                <Box sx={{ mt: 2 }}>
                    {responseReason && <Reason reason={responseReason} />}
                    <Box sx={{ display: "flex", gap: 1, mt: 1 }}>
                        <SplitButton
                            options={
                                [
                                    { label: "Insert", clickAction: handleInsert("AtCursor") },
                                    { label: "Insert after", clickAction: handleInsert("AfterQuestion") },
                                ]}
                            variant="contained"
                            color="primary"
                            size="small"
                            disabled={loading}
                        />
                        {regenerateBtn}
                        <Button
                            variant="outlined"
                            color="secondary"
                            size="small"
                            onClick={handleView}
                            disabled={loading}
                        >
                            View
                        </Button>
                        <FeedbackButtons
                            questionId={questionId}
                            responseId={response?.id}
                            responseText={responseText}
                            reason={responseReason}
                            goodCountProp={question.feedback?.good ?? 0}
                            badCountProp={question.feedback?.bad ?? 0}
                            selfFeedbackProp={question.feedback?.self ?? null}
                        />
                    </Box>
                </Box>
            </Collapse >
        </Box >
    );
});

export default SingleDDQWithData;
