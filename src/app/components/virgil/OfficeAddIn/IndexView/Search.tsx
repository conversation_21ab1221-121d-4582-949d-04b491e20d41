import { debounce } from "@mui/material";

import { TextField } from "@mui/material"
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setSearchDebounced, setSearchInput } from "~/lib/features/documentDDQSlice";
import { selectSearchInput } from "~/lib/features/stateSelecrors";

const DEBOUNCE_TIME = 300;
export const Search: React.FC = () => {
    const dispatch = useDispatch();
    const searchInput = useSelector(selectSearchInput);
    const debouncedSearch = debounce((value: string) => {
        dispatch(setSearchDebounced(value.toLowerCase().trim()));
    }, DEBOUNCE_TIME);

    useEffect(() => {
        return () => {
            debouncedSearch.clear();
        };
    }, []);

    const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        dispatch(setSearchInput(event.target.value));
        debouncedSearch(event.target.value);
    };

    return (
        <TextField
            value={searchInput}
            onChange={handleSearchChange}
            placeholder="Search"
            size="small"
            sx={{ width: "100%", backgroundColor: "white", fontSize: "10px" }}
            slotProps={{
                input: {
                    sx: {
                        fontSize: "8px",
                        padding: 0
                    },
                },
            }}
        />
    );
}
