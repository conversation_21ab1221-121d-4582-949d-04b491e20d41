import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Box } from "@mui/material";
import SingleDDQWithData from "./SingleDDQWithData";
import { DDQuestionWithFeedback } from "~/server/api/managers/questionManager";
import QuestionActions from "./QuestionActions";
import InfoBox from "./InfoBox";
import { useDDQSelect } from "../../hooks/useDDQSelect";
import { QuestionStatusType } from "@prisma/client";
import { useSelectAction } from "../../hooks/useSelectAction";
import { eventBus } from "~/lib/eventBus";
import { Searcher } from "fast-fuzzy";
import { useDDQuestionsInfinite } from "../../hooks/useDDQuestionsInfinite";
import { useVirtualizer } from '@tanstack/react-virtual';

export type DDQListPaginatedProps = {
    noQuestionsText: string;
    tabType: QuestionStatusType;
}

const DDQListInfiniteScroll: React.FC<DDQListPaginatedProps> = ({ noQuestionsText, tabType }) => {
    const [expandedId, setExpandedId] = useState<string>("");
    const parentRef = React.useRef<HTMLDivElement>(null);
    const previousSelectedId = useRef<string>("");

    const { questions, isLoading } = useDDQuestionsInfinite(tabType);

    const virtualizer = useVirtualizer({
        count: questions.length,
        getScrollElement: () => parentRef.current,
        estimateSize: () => 57,
        overscan: 5,
        getItemKey: (index) => (questions[index] as { id: string }).id,
    });
    const items = virtualizer.getVirtualItems();

    const handleToggle = useCallback((id: string) => {
        // this is to prevent race condition when user clicks on the question, word paragraph is selected
        // and the plugin scrolls to the same question  
        previousSelectedId.current = id === expandedId ? "" : id;
        setExpandedId(prev => prev === id ? "" : id);
        const element = document.getElementById(`ddq-${id ?? ''}`) as Element;
        if (element) {
            virtualizer.measureElement(element);
        }
    }, [virtualizer, setExpandedId]);

    const { selectedIds, isAllSelected, handleSelectAll, handleSelectSingle, totalSelectedPerPage, setSelectedIds } = useDDQSelect({ questions });

    const { SplitButtonMemo } = useSelectAction({ selectedIds, setSelectedIds, tabType });
    const scrollToQuestion = useCallback((index: number) => {
        virtualizer.scrollToIndex(index, { align: 'start', behavior: 'smooth' });
    }, [virtualizer]);

    const questionsSearcherMemo = useMemo(() => {
        console.log("questionsSearcherMemo", questions);
        return new Searcher(questions, { keySelector: (obj) => obj.text });
    }, [questions]);

    const wordSelectionChanged = useCallback(({
        paragraph,
    }: {
        paragraph: string;
    }) => {
        if (paragraph) {
            const matchingQuestion = questionsSearcherMemo.search(paragraph)?.[0];
            if (matchingQuestion && expandedId !== matchingQuestion.id && previousSelectedId.current !== matchingQuestion.id) {
                setExpandedId(matchingQuestion.id);
                setTimeout(() => {
                    virtualizer.measure()
                    scrollToQuestion(questions.findIndex(q => q.id === matchingQuestion.id));
                }, 200);
            }
        }
    }, [questionsSearcherMemo, expandedId, scrollToQuestion]);

    useEffect(() => {
        eventBus.on('wordSelectionChanged', wordSelectionChanged);
        return () => {
            eventBus.off('wordSelectionChanged', wordSelectionChanged);
        };
    }, [wordSelectionChanged]);

    if (isLoading || !questions) {
        return (
            <Box sx={{ overflow: "auto", height: 'calc(100vh - 55px - 50px - 40px)' }}>
                <InfoBox text="Loading questions..." />
            </Box>
        );
    }

    if (questions.length === 0) {
        return (
            <Box sx={{ overflow: "auto", height: 'calc(100vh - 55px - 50px - 40px)' }}>
                <InfoBox text={noQuestionsText} />
            </Box>
        );
    }

    return (
        <>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 1, height: '40px' }}>
                <QuestionActions
                    totalSelectedPerPage={totalSelectedPerPage}
                    totalCountPerPage={questions.length}
                    totalSelected={selectedIds.length}
                    isAllSelected={isAllSelected}
                    handleSelectAll={handleSelectAll}
                    actionButton={SplitButtonMemo}
                />
            </Box>
            <Box
                ref={parentRef}
                sx={{
                    overflow: "auto",
                    height: `calc(100vh - 55px - 50px - 40px - 60px)`,
                    position: 'absolute',
                    top: "205px",
                    left: 0,
                    right: 0,
                    contain: 'strict'
                }}
            >
                <div
                    style={{
                        height: `${virtualizer.getTotalSize()}px`,
                        width: '100%',
                        position: 'relative',
                    }}
                >
                    <div
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            transform: `translateY(${items[0]?.start ?? 0}px)`,
                        }}
                    >
                        {items.map((virtualRow) => (
                            <SingleDDQWithData
                                measureElement={virtualizer.measureElement}
                                index={virtualRow.index}
                                key={questions[virtualRow.index]?.id ?? ''}
                                expanded={expandedId === questions[virtualRow.index]?.id}
                                handleToggle={handleToggle}
                                isChecked={selectedIds.includes(questions[virtualRow.index]?.id ?? '')}
                                setIsChecked={handleSelectSingle}
                                question={questions[virtualRow.index] as DDQuestionWithFeedback}
                            />
                        ))}
                    </div>
                </div>
            </Box>
        </>
    );
};

export default DDQListInfiniteScroll;