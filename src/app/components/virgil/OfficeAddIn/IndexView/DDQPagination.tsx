import { Box, useTheme } from "@mui/material";
import Pagination from '@mui/material/Pagination';

const DDQPagination = ({
    totalPages,
    page,
    handlePageChange,
}: {
    totalPages: number;
    page: number;
    handlePageChange: (event: React.ChangeEvent<unknown>, value: number) => void;
}) => {
    const theme = useTheme();
    return (
        <Box
            sx={{
                borderTop: '1px solid',
                p: 2,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width: '100%',
                height: '50px',
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                backgroundColor: "#fff"
            }}
        >
            <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                size="small"
                showFirstButton={false}
                showLastButton={false}
                siblingCount={1}
            />
        </Box>
    )
}

export default DDQPagination;