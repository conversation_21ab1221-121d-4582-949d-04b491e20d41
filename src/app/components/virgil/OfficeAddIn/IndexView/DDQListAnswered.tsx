import React from "react";
import { QuestionStatusType } from "@prisma/client";
import DDQListInfiniteScroll from "./DDQListInfiniteScroll";

const DDQListAnswered: React.FC = () => {
  return (
    <>
      <DDQListInfiniteScroll
        tabType={QuestionStatusType.ANSWERED}
        noQuestionsText="No answered due diligence questions found."
      />
    </>
  );
};

export default DDQListAnswered;
