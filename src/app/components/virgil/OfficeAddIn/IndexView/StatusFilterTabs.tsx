import React from "react";
import { Box, Tab, Tabs } from "@mui/material";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { QuestionStatusType } from "@prisma/client";
import { selectQuestionStatusFilter } from "~/lib/features/documentDDQSelector";
import { setQuestionStatusFilter } from "~/lib/features/documentDDQSlice";
import { useDDQuestionsCount } from "../../hooks/useDDQuestionsCount";

export const StatusFilterTabs: React.FC = () => {
    const questionStatusFilter = useSelector(selectQuestionStatusFilter);
    const { newQuestions, answeredQuestions } = useDDQuestionsCount();
    const dispatch = useDispatch();

    const handleTabChange = (event: React.SyntheticEvent, newValue: QuestionStatusType) => {
        dispatch(setQuestionStatusFilter(newValue));
    };

    return (
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
                value={questionStatusFilter}
                onChange={handleTabChange}
                variant="fullWidth"
                aria-label="detailed answer tabs"
                sx={{
                    height: '40px',
                    '& .MuiTabs-indicator': {
                        backgroundColor: 'primary.main'
                    },
                    '& .Mui-selected.MuiTab-root': {
                        color: 'primary.main'
                    },
                }}
            >
                <Tab label={`New ${typeof newQuestions === "number" ? `(${newQuestions})` : ''}`} value={QuestionStatusType.NEW} sx={{ height: '40px' }} />
                <Tab label={`Answered ${typeof answeredQuestions === "number" ? `(${answeredQuestions})` : ''}`} value={QuestionStatusType.ANSWERED} sx={{ height: '40px' }} />
            </Tabs>
        </Box>
    )
}