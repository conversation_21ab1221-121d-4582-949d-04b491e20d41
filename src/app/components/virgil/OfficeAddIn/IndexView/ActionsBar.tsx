import { Box } from "@mui/material";

import { Search } from "./Search";
// import { HeaderFilters } from "../HeaderFilters";

export const ActionsBar: React.FC = () => {
    return (
        <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            gap: 1,
            alignItems: 'center',
            width: '100%',
            p: 2,
            borderBottom: '1px solid #e0e0e0',
            borderTop: '1px solid #e0e0e0',
            backgroundColor: '#fff',
            height: '60px',
            position: 'sticky',
            top: 0,
            zIndex: 1000
        }}>
            <Search />
            {/* <Box sx={{ marginLeft: 'auto' }}>
                <HeaderFilters />
            </Box> */}
        </Box>
    )
}