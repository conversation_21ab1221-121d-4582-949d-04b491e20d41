import React from "react";
import { QuestionStatusType } from "@prisma/client";
import DDQListInfiniteScroll from "./DDQListInfiniteScroll";

const DDQListNew: React.FC = () => {
  return (
    <>
      <DDQListInfiniteScroll
        tabType={QuestionStatusType.NEW}
        noQuestionsText="No due diligence questions found, please re-process document in the Data Room"
      />
    </>
  );
};

export default DDQListNew;
