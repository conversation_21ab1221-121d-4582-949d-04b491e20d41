import React, { useState } from 'react';
import { useDispatch, useSelector } from "react-redux";
import Select from 'react-select';
import { setSelectedTagIds } from '~/lib/features/documentSlice';
import { RootState } from '~/lib/store';
import { api } from "~/trpc/react";

const Tags: React.FC = () => {
    const { data: tags, isLoading, error } = api.tag.getAllTags.useQuery();
    const options = tags?.map((tag) => ({ value: tag.id, label: tag.name })) || [];
    const { selectedTagIds } = useSelector((state: RootState) => state.document);
    const dispatch = useDispatch();


    if (isLoading) return <div>Loading...</div>;
    if (error) return <div>Error loading tags</div>;

    const handleChange = (newValue: any) => {
        const selectedTagIds = newValue?.map((v: { value: string }) => v.value) || [];
        dispatch(setSelectedTagIds(selectedTagIds));
    };

    return (
        <div>
            <Select
                value={options.filter((option: { value: string }) => selectedTagIds.includes(option.value))}
                isMulti
                isClearable={false}
                name="colors"
                className="basic-multi-select"
                classNamePrefix="select"
                onChange={handleChange}
                options={options}
            />
        </div>
    );
};

export default Tags;
