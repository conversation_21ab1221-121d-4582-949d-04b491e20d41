import { GetDocumentType } from "~/server/api/routers/question";
import { CitationsAccordionList } from "./CitationsAccordionList";
import { Box, Divider, Stack } from "@mui/material";
import { SourceDocuments } from './SourceDocuments';
import { Citation } from '~/lib/types';
import { ResponseEditsTimelineContainer } from "~/sections/response/ResponseEditsTimelineContainer";

type DetailsTabContentProps = {
    similarResponseDocuments: GetDocumentType[];
    similarResponseCanonicalId: string;
}
export default function DetailsTabContentExtracted({
    similarResponseDocuments,
    similarResponseCanonicalId
}: DetailsTabContentProps) {
    return (
        <Box sx={{ overflow: "auto", height: "calc(100vh - 55px - 50px)", p: 2 }}>
            <Stack direction="column" spacing={2}>
                <SourceDocuments documents={similarResponseDocuments} />
                <Divider sx={{
                    my: 2
                }} />
                <ResponseEditsTimelineContainer responseId={similarResponseCanonicalId} />
            </Stack>
        </Box>
    )
}