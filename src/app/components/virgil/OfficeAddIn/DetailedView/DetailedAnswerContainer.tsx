import * as Sentry from "@sentry/react";
import { useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { findAndInsertAfter } from "~/app/add-in-word/wordUtils";
import { toast } from "~/components/snackbar";
import { setAddInNavigationPath, setIsGlobalLoading } from "~/lib/features/addInUISlice";
import {
  selectSelectedQuestionObject,
} from "~/lib/features/documentDDQSelector";
import { useUserMSAddInSettings } from "../../hooks/useUserMSAddInSettings";
import { useUpdateQuestionStatus } from "../../hooks/useUpdateQuestionStatus";
import { AnswerGenerationType, QuestionStatusType } from "@prisma/client";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import DetailedAnswerTwoTab from "./DetailedAnswerTwoTab";
import DetailsTabContent from "./DetailsTabContent";
import Editor from "./Editor";
import { GetDocumentType } from "~/server/api/routers/question";
import { Citation } from "~/lib/types";
import DetailsTabContentGenerated from "./DetailsTabContentGenerated";
import DetailsTabContentExtracted from "./DetailsTabContentExtracted";
import { useGetExistingResponseById } from "../../hooks/useGetExistingResponseById";

const DetailedAnswerContainer: React.FC = () => {
  const dispatch = useDispatch();

  const documentId = useSelector(selectDocumentId);
  const selectedQuestionObject = useSelector(selectSelectedQuestionObject);
  const similarResponseDocuments = selectedQuestionObject?.response?.documents.map((document) => document.document) as GetDocumentType[];
  const citations = selectedQuestionObject?.response?.responseContents?.map((rs) => (rs.content as { citations: Citation[] })?.citations).flat() ?? [];
  const responseType = selectedQuestionObject?.response?.responseContents?.[0]?.answerGenerationType;
  const updateQuestionStatusMutation = useUpdateQuestionStatus({
    onSuccess: () => {
      toast.success("Question status updated");
    },
    onError: (error: any) => {
      toast.error(`Error updating question status: ${error}`);
    },
  });

  const { data: msAddInSettings, isLoading: userDataLoading } =
    useUserMSAddInSettings();
  const responseIdExtracted = (selectedQuestionObject?.response?.responseContents?.[0]?.content as { existingResponses: { responseId: string }[] }).existingResponses?.[0]?.responseId;
  const { data: responseData, isLoading: responseLoading, responseText: responseTextExisting, responseContentId: responseContentIdExisting, responseDocuments: responseDocumentsExisting } = useGetExistingResponseById(responseIdExtracted ?? null);

  const responseText = useMemo(() => {
    if (responseType === AnswerGenerationType.EXTRACTED) {
      return responseTextExisting;
    }
    return (selectedQuestionObject?.response?.responseContents?.[0]?.content as { text: string })?.text ?? "";
  }, [responseTextExisting, responseType]);

  const responseContentId = useMemo(() => {
    if (responseType === AnswerGenerationType.GENERATED) {
      return selectedQuestionObject?.response?.responseContents?.[0]?.id ?? "";
    }
    return responseContentIdExisting;
  }, [responseType, responseContentIdExisting, selectedQuestionObject?.response?.responseContents?.[0]?.id]);

  const handleInsert = useCallback(
    (insertype: "AfterQuestion" | "AtCursor") => async (e: React.MouseEvent<HTMLElement>) => {
      e.stopPropagation();
      try {
        dispatch(setIsGlobalLoading(true));
        // insert response
        await findAndInsertAfter(
          selectedQuestionObject?.text ?? "",
          responseText,
          msAddInSettings?.insertTextColor ?? "green",
          msAddInSettings?.fontSize,
          msAddInSettings?.bold,
          msAddInSettings?.italic,
          msAddInSettings?.underline,
          insertype
        );
        toast.success("Response inserted");

        // update question status
        await updateQuestionStatusMutation.mutateAsync({
          id: selectedQuestionObject?.id ?? "",
          newStatus: QuestionStatusType.ANSWERED,
          oldStatus: selectedQuestionObject?.response?.status as QuestionStatusType || QuestionStatusType.NEW,
        });

        dispatch(setIsGlobalLoading(false));

        Sentry.startSpan(
          {
            name: "Plugin Response",
            op: "plugin.response",
          },
          async (span) => {
            span.setAttribute("action", "insert");
            span.setAttribute("question.id", selectedQuestionObject?.id ?? "");
          },
        )
        dispatch(setAddInNavigationPath("ddq"));
      } catch (error) {
        dispatch(setIsGlobalLoading(false));
        toast.error(`Error inserting response: ${error}`);
        Sentry.startSpan(
          {
            name: "Plugin Response Error",
            op: "plugin.response.error",
          },
          async (span) => {
            span.setAttribute("action", "insert");
            span.setAttribute("question.id", selectedQuestionObject?.id ?? "");
            span.setAttribute("error", `${error}`);
          },
        )
      }
    },
    [
      selectedQuestionObject?.text,
      selectedQuestionObject?.id,
      msAddInSettings?.insertTextColor,
      dispatch,
      setIsGlobalLoading,
      documentId,
      responseText,
    ],
  );

  if (!selectedQuestionObject || userDataLoading) {
    return null;
  }

  return (
    <DetailedAnswerTwoTab
      tab1Label="Editor"
      tab1Component={
        <Editor
          questionText={selectedQuestionObject?.text ?? ""}
          handleInsert={handleInsert}
          questionId={selectedQuestionObject?.id ?? ""}
          responseIdCanonical={selectedQuestionObject?.response?.id ?? ""}
          similarResponseId={responseContentId ?? ""}
          similarResponseText={responseText}
          similarResponseStatus={selectedQuestionObject?.response?.status ?? null}
          isSimilaritySearchLoading={false}
          ragResponseText={""}
          isRagResponseLoading={false}
          answerGenerationType={selectedQuestionObject?.response?.responseContents?.[0]?.answerGenerationType ?? AnswerGenerationType.EXTRACTED}
          reason={(selectedQuestionObject?.response?.responseContents?.[0]?.content as { reason: string })?.reason}
          feedback={selectedQuestionObject?.feedback ?? null}
        />
      }
      tab2Label="Details"
      tab2Component={
        responseType === AnswerGenerationType.GENERATED ?
          <DetailsTabContentGenerated
            ragCitations={citations}
            responseIdCanonical={selectedQuestionObject?.response?.id ?? ""}
          />
          :
          <DetailsTabContentExtracted
            similarResponseDocuments={responseDocumentsExisting as any ?? []}
            similarResponseCanonicalId={selectedQuestionObject?.response?.id ?? ""}
          />
      }
    />
  );
};

export default DetailedAnswerContainer;
