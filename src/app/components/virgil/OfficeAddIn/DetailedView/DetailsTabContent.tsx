import { GetDocumentType } from "~/server/api/routers/question";
import { CitationsAccordionList } from "./CitationsAccordionList";
import { Box, Divider, Stack } from "@mui/material";
import { SourceDocuments } from './SourceDocuments';
import { Citation } from '~/lib/types';
import { ResponseEditsTimelineContainer } from "~/sections/response/ResponseEditsTimelineContainer";

type DetailsTabContentProps = {
    ragCitations: Citation[];
    similarResponseDocuments: GetDocumentType[];
    similarResponseCanonicalId: string | null;
}
export default function DetailsTabContent({
    ragCitations,
    similarResponseDocuments,
    similarResponseCanonicalId
}: DetailsTabContentProps) {
    return (
        <Box sx={{ overflow: "auto", height: "calc(100vh - 55px - 50px)", p: 2 }}>
            <Stack direction="column" spacing={2}>
                {/* Rag Respnse elements */}
                {ragCitations.length > 0 && (
                    <>
                        <CitationsAccordionList citations={ragCitations ?? []} />
                        <Divider sx={{
                            my: 2
                        }} />
                    </>
                )}
                {/* Similar Response elements */}
                {similarResponseDocuments.length > 0 && (
                    <>
                        <SourceDocuments documents={similarResponseDocuments} />
                        <Divider sx={{
                            my: 2
                        }} />
                    </>
                )}
                {similarResponseCanonicalId &&
                    (<ResponseEditsTimelineContainer responseId={similarResponseCanonicalId} />)
                }
            </Stack>
        </Box>
    )
}