import { useState } from 'react';
import { Box, Tab, Tabs } from '@mui/material';

interface TabPanelProps {
  children?: React.ReactNode;
  value: string;
  index: string;
}

const TabPanel = ({ children, value, index }: TabPanelProps) => {
  return (
    <Box role="tabpanel" hidden={value !== index}>
      {value === index && children}
    </Box>
  );
};

export type DetailedAnswerProps = {
  tab1Label?: string;
  tab1Component: React.ReactNode;
  tab2Label?: string;
  tab2Component: React.ReactNode;
}
const DetailedAnswerTwoTab: React.FC<DetailedAnswerProps> = ({
  tab1Component,
  tab2Component,
  tab1Label = "Editor",
  tab2Label = "Details",
}) => {
  const [activeTab, setActiveTab] = useState<"tab1" | "tab2">('tab1');

  const handleTabChange = (_event: React.SyntheticEvent, newValue: "tab1" | "tab2") => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', height: '48px' }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          aria-label="detailed answer tabs"
          sx={{
            height: '48px',
            '& .MuiTabs-indicator': {
              backgroundColor: 'primary.main'
            },
            '& .Mui-selected.MuiTab-root': {
              color: 'primary.main'
            },
          }}
        >
          <Tab label={tab1Label} value="tab1" sx={{ height: '48px' }} />
          <Tab label={tab2Label} value="tab2" sx={{ height: '48px' }} />
        </Tabs>
      </Box>
      <TabPanel value={activeTab} index="tab1">
        {tab1Component}
      </TabPanel>
      <TabPanel value={activeTab} index="tab2">
        {tab2Component}
      </TabPanel>
    </Box>
  );
};

export default DetailedAnswerTwoTab;
