import { Box, Button, Typography } from "@mui/material";
import { Citation } from "~/lib/types";
import DocumentAccordion from "../../DocumentAccordion/DocumentAccordion";
import { useState } from "react";

const MAX_CITATIONS_IN_PREVIEW = 3;

export const CitationsAccordionList = ({
  citations,
}: {
  citations: Citation[];
}) => {
  const [viewAll, setViewAll] = useState(false);
  const displayedCitations = viewAll
    ? citations
    : citations.slice(0, MAX_CITATIONS_IN_PREVIEW);

  console.log("citations", citations);

  return (
    <Box
      sx={{
        p: 1,
        overflow: "auto",
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 1,
        }}
      >
        <Typography variant="h4" sx={{ mb: 1 }}>
          Citations ({citations.length})
        </Typography>
        {citations.length > MAX_CITATIONS_IN_PREVIEW && (
          <Button
            sx={{ color: "primary.main" }}
            variant="text"
            onClick={() => setViewAll((viewAll) => !viewAll)}
          >
            {viewAll ? "View Less" : "View All"}
          </Button>
        )}
      </Box>
      {displayedCitations.map((citation, idx) => (
        <DocumentAccordion
          key={idx}
          fileName={citation?.fileName?.split("/").at(-1) ?? ""}
          content={citation?.quote ?? ""}
          sourceLink={""}
          pageNumber={citation?.metadata?.page_number ?? 0}
          documentId={citation?.documentId ?? ""}
        />
      ))}
    </Box>
  );
};
