import { CitationsAccordionList } from "./CitationsAccordionList";
import { Box, Divider, Stack } from "@mui/material";
import { Citation } from '~/lib/types';
import { ResponseEditsTimelineContainer } from "~/sections/response/ResponseEditsTimelineContainer";

type DetailsTabContentProps = {
    ragCitations: Citation[];
    responseIdCanonical: string;
}
export default function DetailsTabContentGenerated({
    ragCitations,
    responseIdCanonical
}: DetailsTabContentProps) {
    return (
        <Box sx={{ overflow: "auto", height: "calc(100vh - 55px - 50px)", p: 2 }}>
            <Stack direction="column" spacing={2}>
                <CitationsAccordionList citations={ragCitations ?? []} />
                <Divider sx={{
                    my: 2
                }} />
                <ResponseEditsTimelineContainer responseId={responseIdCanonical} />
            </Stack>
        </Box>
    )
}