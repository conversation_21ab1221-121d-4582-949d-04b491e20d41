import React, { useMemo } from "react";
import { useSelector } from "react-redux";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import { useIsFetching } from "@tanstack/react-query";
import { getQueryKey } from "@trpc/react-query";
import { api } from "~/trpc/react";
import { selectDocumentMetadataDocumentStatus } from "~/lib/features/stateSelecrors";
import { DocumentStatus as DocumentStatusEnum } from "@prisma/client";
import { onlineManager } from "@tanstack/react-query";

export default function useDocumentStatus() {
  const documentId = useSelector(selectDocumentId);
  const documentStatus = useSelector(selectDocumentMetadataDocumentStatus);

  const isFetchingDocumentByUrl = useIsFetching({
    queryKey: getQueryKey(api.document.getDocumentByUrl, undefined, "query"),
  });
  const isFetchingDocumentByFileName = useIsFetching({
    queryKey: getQueryKey(
      api.document.getDocumentByFilename,
      undefined,
      "query",
    ),
  });
  const isFetchingQuestions = useIsFetching({
    queryKey: getQueryKey(api.question.getAllQuestions, undefined, "query"),
  });
  const isFetchingSimilarity = useIsFetching({
    queryKey: getQueryKey(api.question.similaritySearch, undefined, "query"),
  });
  const isFetchingRagResponse = useIsFetching({
    queryKey: getQueryKey(api.rag.generateRAGResponseQuery, undefined, "query"),
  });
  const isFetchingDDQPrompts = useIsFetching({
    queryKey: getQueryKey(api.sheet.getAllPromptsForSheet, undefined, "query"),
  });

  const color = useMemo(() => {
    const isDocumentDefined = Boolean(documentId);
    if (
      !isDocumentDefined ||
      documentStatus !== DocumentStatusEnum.READY ||
      !onlineManager.isOnline()
    ) {
      return "red";
    }
    return "green";
  }, [documentStatus, documentStatus, onlineManager.isOnline()]);

  const fullTextStatus = useMemo(() => {
    if (!onlineManager.isOnline()) {
      return "No internet connection";
    }

    const isDocumentDefined = Boolean(documentId);
    if (!isDocumentDefined) {
      return "Document is not found in Data Room";
    }

    if (documentStatus !== DocumentStatusEnum.READY) {
      return "Document is not ready";
    }

    return "Document is ready";
  }, [documentStatus, documentId, onlineManager.isOnline()]);

  const shortStatusText = useMemo(() => {
    if (!onlineManager.isOnline()) {
      return "Offline";
    }

    if (isFetchingDocumentByUrl > 0 || isFetchingDocumentByFileName > 0) {
      return "Loading document...";
    }
    if (isFetchingQuestions > 0) {
      return "Loading questions...";
    }
    if (isFetchingSimilarity > 0) {
      return "Generating answers...";
    }
    if (isFetchingRagResponse > 0) {
      return "Generating answers...";
    }
    if (isFetchingDDQPrompts > 0) {
      return "Loading prompts...";
    }

    if (
      isFetchingRagResponse === 0 &&
      isFetchingQuestions === 0 &&
      isFetchingSimilarity === 0 &&
      isFetchingDocumentByUrl === 0 &&
      isFetchingDocumentByFileName === 0
    ) {
      const isDocumentDefined = Boolean(documentId);
      if (!isDocumentDefined) {
        return "Document is not found";
      }
      if (documentStatus !== DocumentStatusEnum.READY) {
        return "Document is not ready";
      }
    }
  }, [
    isFetchingDocumentByUrl,
    isFetchingDocumentByFileName,
    isFetchingSimilarity,
    isFetchingRagResponse,
    isFetchingQuestions,
    documentStatus,
    documentId,
    onlineManager.isOnline(),
  ]);

  return {
    color,
    fullTextStatus,
    shortStatusText,
    documentFound: Boolean(documentId),
    documentProcessed: documentStatus === DocumentStatusEnum.READY,
    isFetchingDocument:
      isFetchingDocumentByUrl > 0 || isFetchingDocumentByFileName > 0,
    documentStatus,
    documentId,
  };
}
