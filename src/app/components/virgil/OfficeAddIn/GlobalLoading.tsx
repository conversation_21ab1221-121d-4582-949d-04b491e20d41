import React from 'react';
import { Box, CircularProgress, IconButton } from '@mui/material';
import { useSelector } from 'react-redux';
import { selectIsGlobalLoading } from '~/lib/features/addInUISelectors';

export default function GlobalLoading() {
    const isGlobalLoading = useSelector(selectIsGlobalLoading);
    return (
        <Box sx={{ marginLeft: 'auto' }}>
            {isGlobalLoading && (
                <IconButton size="small" disabled>
                    <CircularProgress size={20} />
                </IconButton>
            )}
        </Box>
    );
}
