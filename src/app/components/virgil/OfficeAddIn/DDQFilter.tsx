"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import Button from "@mui/material/Button";
import Stack from "@mui/material/Stack";

import { type UseSetStateReturn } from "src/hooks/use-set-state";

import { Iconify } from "src/components/iconify";

import {
    Chip,
    Fade,
    Paper,
    Popper,
    Typography,
    ClickAwayListener,
} from "@mui/material";
import { type Fund, type Tag } from "@prisma/client";
import PopupState, { bindPopper, bindToggle } from "material-ui-popup-state";
import Select, { type MultiValue } from "react-select";
import { selectSelectedFundIds, selectSelectedTagIds } from "~/lib/features/documentDDQSelector";
import { useDispatch, useSelector } from "react-redux";
import { setSelectedFundIds } from "~/lib/features/documentDDQSlice";
import { setSelectedTagIds } from "~/lib/features/documentDDQSlice";
import { setLocalStorageFundIds, setLocalStorageTagsIds } from "./localStorageClient";

type DDQFilterProps = {
    tags: Tag[] | undefined;
    funds: Fund[] | undefined;
}

export const DDQFilter = ({
    tags,
    funds,
}: DDQFilterProps) => {
    const dispatch = useDispatch();
    const selectedTagIds = useSelector(selectSelectedTagIds);
    const selectedFundIds = useSelector(selectSelectedFundIds);

    const tagOptions = useMemo(() =>
        tags?.map((tag) => ({ value: tag.id, label: tag.name })) ?? [],
        [tags]
    );
    const fundOptions = useMemo(() =>
        funds?.map((fund) => ({ value: fund.id, label: fund.name })) ?? [],
        [funds]
    );

    const handleTagsChange = useCallback((
        newValue: MultiValue<{ value: string; label: string }>,
    ) => {
        const newSelectedTagIds =
            newValue?.map((v: { value: string }) => v.value) || [];
        dispatch(setSelectedTagIds(newSelectedTagIds));
    }, [dispatch, setSelectedTagIds, selectedTagIds]);

    const handleFundsChange = useCallback((
        newValue: MultiValue<{ value: string; label: string }>,
    ) => {
        const newSelectedFundIds =
            newValue?.map((v: { value: string }) => v.value) || [];
        dispatch(setSelectedFundIds(newSelectedFundIds));
    }, [dispatch, setSelectedFundIds, selectedFundIds]);

    const handleClear = useCallback(() => {
        dispatch(setSelectedTagIds([]));
        dispatch(setSelectedFundIds([]));
    }, [dispatch, setSelectedTagIds, setSelectedFundIds]);

    // persist selectedTagIds and selectedFundIds to localStorage
    useEffect(() => {
        setLocalStorageTagsIds(selectedTagIds);
        setLocalStorageFundIds(selectedFundIds);
    }, [selectedTagIds, selectedFundIds]);

    return (
        <PopupState variant="popper" popupId="feedback-popup-popper">
            {(popupState) => (
                <ClickAwayListener onClickAway={(event: MouseEvent | TouchEvent) => {
                    popupState.close();
                }}>
                    <div>
                        <Button
                            variant="text"
                            size="medium"
                            sx={{
                                minWidth: 0,
                            }}
                            {...bindToggle(popupState)}
                        >
                            <Iconify icon={"tabler:adjustments-horizontal"} width={20} style={{ marginLeft: 0 }} />
                            {[...selectedTagIds, ...selectedFundIds].length > 0 && (
                                <Chip
                                    label={`${selectedTagIds.length} tags, ${selectedFundIds.length} funds`}
                                    size="small"
                                    sx={{ ml: 1 }}
                                />
                            )}
                        </Button>
                        <Popper
                            {...bindPopper(popupState)}
                            transition
                            placement="bottom"
                            modifiers={[
                                {
                                    name: "offset",
                                    options: {
                                        offset: [0, 10],
                                    },
                                },
                            ]}
                            style={{ zIndex: 1300 }}
                        >
                            {({ TransitionProps }) => (
                                <Fade {...TransitionProps} timeout={350}>
                                    <div>
                                        <Paper
                                            sx={{
                                                width: "400px",
                                                maxWidth: "90vw",
                                                gap: 1,
                                                boxShadow: "0 0 5px rgba(0, 0, 0, 0.2)",
                                                padding: 1,
                                            }}
                                        >
                                            <Stack direction="column" justifyContent="space-between">
                                                <Typography variant="body2" sx={{ fontWeight: "bold" }}>
                                                    Tags
                                                </Typography>
                                                <Select
                                                    value={tagOptions.filter((option) =>
                                                        selectedTagIds.includes(option.value),
                                                    )}
                                                    isMulti
                                                    isClearable={false}
                                                    name="colors"
                                                    className="basic-multi-select"
                                                    classNamePrefix="select"
                                                    placeholder="Select tags"
                                                    onChange={handleTagsChange}
                                                    options={tagOptions}
                                                    styles={{
                                                        container: (base) => ({
                                                            ...base,
                                                            padding: 2,
                                                            borderRadius: 2,
                                                            marginTop: 2,
                                                        }),
                                                    }}
                                                />

                                                <Typography
                                                    variant="body2"
                                                    sx={{ fontWeight: "bold", mt: 2 }}
                                                >
                                                    Funds
                                                </Typography>
                                                <Select
                                                    value={fundOptions.filter((option) =>
                                                        selectedFundIds.includes(option.value),
                                                    )}
                                                    isMulti
                                                    isClearable={false}
                                                    name="colors"
                                                    className="basic-multi-select"
                                                    classNamePrefix="select"
                                                    placeholder="Select funds"
                                                    onChange={handleFundsChange}
                                                    options={fundOptions}
                                                    styles={{
                                                        container: (base) => ({
                                                            ...base,
                                                            padding: 2,
                                                            borderRadius: 2,
                                                            marginTop: 2,
                                                        }),
                                                    }}
                                                />
                                                <Stack
                                                    direction="row"
                                                    spacing={4}
                                                    sx={{ mt: 2, width: "100%" }}
                                                >
                                                    <Button
                                                        variant="outlined"
                                                        onClick={handleClear}
                                                        fullWidth
                                                    >
                                                        Clear
                                                    </Button>
                                                    <Button
                                                        variant="contained"
                                                        color="primary"
                                                        onClick={popupState.close}
                                                        fullWidth
                                                    >
                                                        Apply
                                                    </Button>
                                                </Stack>
                                            </Stack>
                                        </Paper>
                                    </div>
                                </Fade>
                            )}
                        </Popper>
                    </div>
                </ClickAwayListener>
            )}
        </PopupState>
    );
};