import { PropsWithChildren, useRef, useCallback, createContext, useContext, useState, RefObject } from "react";
import Script from "next/script";
import React from "react";
import { useDispatch } from "react-redux";
import { register<PERSON>ody<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, registerFileOpenHandler, registerHostType, registerOnParagraphChanged, registerTextSelectionHandler } from "~/app/add-in-word/wordUtils";
import { useDocumentInfo } from "../hooks/useDocumentInfo";
import { Box } from "@mui/material";
import { setWordSelection, setCurrentParagraph } from "~/lib/features/documentSlice";
import { eventBus } from "~/lib/eventBus";
import { registerShortcuts } from "~/app/add-in-word/wordUtils";

interface WordContextType {
    isOfficeReady: boolean;
    hostType: Office.HostType | null;
    platform: Office.PlatformType | null;
    selectedParagraphRef: RefObject<string> | null;
}

const WordContext = createContext<WordContextType>({
    isOfficeReady: false,
    hostType: null,
    platform: null,
    selectedParagraphRef: null,
});

export const useWord = () => {
    const context = useContext(WordContext);
    if (!context) {
        throw new Error('useWord must be used within a WordProvider');
    }
    return context;
};

const WordProvider: React.FC<PropsWithChildren> = ({ children }) => {
    const dispatch = useDispatch();
    useDocumentInfo();
    const [isOfficeReady, setIsOfficeReady] = useState(false);
    const [hostType, setHostType] = useState<Office.HostType | null>(null);
    const [platform, setPlatform] = useState<Office.PlatformType | null>(null);
    const selectedParagraphRef = useRef<string>("");
    const selectedTextRef = useRef<string>("");

    const handleOfficeReady = useCallback((info: {
        host: Office.HostType;
        platform: Office.PlatformType;
    }) => {
        setIsOfficeReady(true);
        setHostType(info.host);
        setPlatform(info.platform);

        registerFileOpenHandler(info.host, dispatch);
        registerHostType(info.host, dispatch);
        registerOnParagraphChanged(info.host);
        registerTextSelectionHandler(info.host, (data) => {
            eventBus.emit('wordSelectionChanged', {
                paragraph: data.paragraph,
                text: data.text,
            });
            selectedParagraphRef.current = data.paragraph;
            selectedTextRef.current = data.text;
        });
        registerShortcuts(info.host);
        registerBodySearchHandler(info.host);
    }, [dispatch]);

    return (
        <WordContext.Provider value={{ isOfficeReady, hostType, platform, selectedParagraphRef }}>
            <Box sx={{ height: "100vh", width: "100%", margin: 0, padding: 0 }}>
                <Script
                    src="https://appsforoffice.microsoft.com/lib/1.1/hosted/office.js"
                    onLoad={() => {
                        Office.onReady(handleOfficeReady);
                    }}
                />
                {children}
            </Box>
        </WordContext.Provider>
    );
};

export default WordProvider;
