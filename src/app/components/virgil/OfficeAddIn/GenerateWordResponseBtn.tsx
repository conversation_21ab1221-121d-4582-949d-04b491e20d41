import { api } from "~/trpc/react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "~/lib/store";
import { setRagLoading, setRagResponse, setRagResponseWord } from "~/lib/features/documentSlice";
import XLoadingButton from "../XLoadingButton/XLoadingButton";

const GenerateWordResponseBtn = () => {
    const { ragLoading, documentId, currentInput } = useSelector((state: RootState) => state.document);
    const dispatch = useDispatch();
    const rag = api.rag.generateRAGResponse.useMutation();
    const onClickFn = async () => {
        const messages = [{
            ragPrompt: currentInput.value ?? "",
            address: "",
            sheet: "",
            context: "test context"
        }];

        dispatch(setRagLoading(true));
        rag.mutate(
            {
                documentId: documentId ?? "",
                messages: messages,
            },
            {
                onSuccess: async (data: any) => {
                    for await (const val of data) {
                        if (val.response) {
                            dispatch(setRagResponseWord(val.response))
                        }
                    }
                    dispatch(setRagLoading(false));
                },
                onError: (error: any) => {
                    console.error(error);

                    dispatch(setRagLoading(false));
                },
            },
        );
    }
    return (<XLoadingButton
        variant="contained"
        fullWidth={true}
        loading={ragLoading}
        onClick={onClickFn}
        style="dark"
    >
        Generate Response
    </XLoadingButton>)
}

export default GenerateWordResponseBtn;