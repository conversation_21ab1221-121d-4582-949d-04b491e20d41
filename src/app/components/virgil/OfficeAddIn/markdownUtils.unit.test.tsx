import { convertMarkdownToHtml } from "./markdownUtils/markdownUtils";
import { render, screen } from "@testing-library/react";

describe("convertMarkdownToHtml", () => {
  it("should convert basic markdown to HTML", async () => {
    const markdown = "# Hello this is **bold** and *italic* text.";
    const result = convertMarkdownToHtml(markdown);
    render(<div dangerouslySetInnerHTML={{ __html: result }} />);
    expect(screen.getByRole("heading", { level: 1 })).toHaveTextContent(
      "Hello this is bold and italic text.",
    );
  });
  it("should convert numbered list to HTML", async () => {
    const markdown = "1. First item\n2. Second item\n3. Third item";
    const result = convertMarkdownToHtml(markdown);
    render(<div dangerouslySetInnerHTML={{ __html: result }} />);
    const list = screen.getByRole("list");
    expect(list.tagName).toBe("OL");
    const items = screen.getAllByRole("listitem");
    expect(items).toHaveLength(3);
    expect(items[0]).toHaveTextContent("First item");
    expect(items[1]).toHaveTextContent("Second item");
    expect(items[2]).toHaveTextContent("Third item");
  });

  it("should convert bullet list to HTML", async () => {
    const markdown = "- First item\n- Second item\n- Third item";
    const result = convertMarkdownToHtml(markdown);
    render(<div dangerouslySetInnerHTML={{ __html: result }} />);
    const list = screen.getByRole("list");
    expect(list.tagName).toBe("UL");
    const items = screen.getAllByRole("listitem");
    expect(items).toHaveLength(3);
    expect(items[0]).toHaveTextContent("First item");
    expect(items[1]).toHaveTextContent("Second item");
    expect(items[2]).toHaveTextContent("Third item");
  });

  it("should convert nested lists to HTML", async () => {
    const markdown = "1. First item\n   - Nested item\n2. Second item\n   - Nested item";
    const result = convertMarkdownToHtml(markdown);
    render(<div dangerouslySetInnerHTML={{ __html: result }} />);
    const lists = screen.getAllByRole("list");
    console.log(lists);
    expect(lists).toHaveLength(3);
    expect(lists[0]).toBeInstanceOf(HTMLOListElement);
    expect(lists[1]).toBeInstanceOf(HTMLUListElement);
    expect(lists[2]).toBeInstanceOf(HTMLUListElement);
  });

  it("should handle multiple newlines", async () => {
    const markdown = "1. First item\n\n2. Second item\n\n3. Third item";
    const result = convertMarkdownToHtml(markdown);
    render(<div dangerouslySetInnerHTML={{ __html: result }} />);
    const list = screen.getByRole("list");
    expect(list.tagName).toBe("OL");
    const items = screen.getAllByRole("listitem");
    expect(items).toHaveLength(3);
    expect(items[0]).toHaveTextContent("First item");
    expect(items[1]).toHaveTextContent("Second item");
    expect(items[2]).toHaveTextContent("Third item");
  });

});