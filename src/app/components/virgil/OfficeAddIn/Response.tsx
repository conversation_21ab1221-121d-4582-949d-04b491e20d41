import React from "react";
import { Box, IconButton } from "@mui/material";
import RefreshIcon from "@mui/icons-material/Refresh";
import ExpandableTextArea from "../ExpandableTextArea/ExpandableTextArea";
import XButton from "../XButton/XButton";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

interface ResponseProps {
    response: string;
    // onSubmit: () => void;
    onInsert: () => void;
}

const Response: React.FC<ResponseProps> = ({ response, onInsert }) => {
    return (
        <>
            <div className="markdown-body">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {response}
                </ReactMarkdown>
            </div>
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    marginTop: 2,
                }}
            >
                <Box sx={{ display: "flex", gap: 1 }}>
                    {/* <XButton onClick={onSubmit} style="dark" fullWidth>
                        Submit for Review
                    </XButton> */}
                    <XButton onClick={onInsert} style="light">
                        Insert
                    </XButton>
                </Box>
                <Box sx={{ flexGrow: 1 }} />
                <IconButton size="small">
                    <RefreshIcon />
                </IconButton>
            </Box>
        </>
    );
};

export default Response;
