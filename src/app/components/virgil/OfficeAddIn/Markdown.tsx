import { Table, Typography } from "@mui/material"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"

export const Markdown = ({ markdown, className, onClick }: { markdown: string, className?: string, onClick?: (e: React.MouseEvent<HTMLElement>) => void }) => {
    return (
        <div onClick={onClick} className={`markdown-body${className ? ` ${className}` : ""}`}>
            <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                    table: ({ node, ...props }) => (
                        <div className="table-wrapper">
                            <table {...props} />
                        </div>
                    ),
                    h1: ({ node, ...props }) => (
                        <Typography variant="h1" {...props} />
                    ),
                    h2: ({ node, ...props }) => (
                        <Typography variant="h2" {...props} />
                    ),
                    h3: ({ node, ...props }) => (
                        <Typography variant="h3" {...props} />
                    ),
                    h4: ({ node, ...props }) => (
                        <Typography variant="h4" {...props} />
                    ),
                    h5: ({ node, ...props }) => (
                        <Typography variant="h5" {...props} />
                    ),
                    h6: ({ node, ...props }) => (
                        <Typography variant="h6" {...props} />
                    ),
                    p: ({ node, ...props }) => (
                        <Typography variant="body2">{props.children}</Typography>
                    ),
                }}
            >
                {markdown}
            </ReactMarkdown>
        </div>
    )
}
