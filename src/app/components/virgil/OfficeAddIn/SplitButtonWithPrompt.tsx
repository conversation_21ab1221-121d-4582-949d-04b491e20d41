import * as React from 'react';
import Button from '@mui/material/Button';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grow from '@mui/material/Grow';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import { ButtonProps } from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Box from '@mui/material/Box';

interface InputConfig {
    placeholder?: string;
    buttonLabel: string;
    onSubmit: (value: string) => void;
}

interface SplitButtonProps extends Omit<ButtonProps, 'onClick'> {
    inputConfig?: InputConfig;
    buttonLabel: string;
}

export default function SplitButtonWithPrompt({ inputConfig, buttonLabel, ...buttonProps }: SplitButtonProps) {
    const [open, setOpen] = React.useState(false);
    const [inputValue, setInputValue] = React.useState('');
    const anchorRef = React.useRef<HTMLDivElement>(null);
    const { fullWidth, ...restBtnProps } = buttonProps;

    const handleToggle = (e: React.MouseEvent<HTMLElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setOpen((prevOpen) => !prevOpen);
    };

    const handleClose = (event: Event) => {
        if (
            anchorRef.current &&
            anchorRef.current.contains(event.target as HTMLElement)
        ) {
            return;
        }
        setOpen(false);
    };

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        event.stopPropagation();
        setInputValue(event.target.value);
    };

    const handleInputSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (inputConfig && inputValue.trim()) {
            inputConfig.onSubmit(inputValue.trim());
            setInputValue('');
            setOpen(false);
        }
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
        event.stopPropagation();
        if (event.key === 'Enter') {
            event.preventDefault();
            handleInputSubmit(event as unknown as React.MouseEvent<HTMLButtonElement>);
        }
    };

    const handleTextFieldClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
    };

    return (
        <Box ref={anchorRef}>
            <Button
                aria-controls={open ? 'split-button-menu' : undefined}
                aria-expanded={open ? 'true' : undefined}
                aria-label={buttonLabel}
                aria-haspopup="menu"
                onClick={handleToggle}
                {...restBtnProps}
            >
                {buttonLabel}
            </Button>
            {(inputConfig) && (
                <Popper
                    sx={{
                        zIndex: 999999,
                        backgroundColor: "white",
                        border: "1px solid #e0e0e0",
                        position: 'relative'
                    }}
                    open={open}
                    anchorEl={anchorRef.current}
                    role={undefined}
                    transition
                    placement="bottom-end"
                    modifiers={[
                        {
                            name: 'preventOverflow',
                            enabled: true,
                            options: {
                                altAxis: true,
                                altBoundary: true,
                                tether: true,
                                rootBoundary: 'viewport',
                                padding: 8
                            }
                        }
                    ]}
                >
                    {({ TransitionProps, placement }) => (
                        <Grow
                            {...TransitionProps}
                            style={{
                                transformOrigin:
                                    placement === 'bottom' ? 'center top' : 'center bottom',
                            }}
                        >
                            <Paper sx={{
                                gap: 1,
                                boxShadow: "0 0 5px rgba(0, 0, 0, 0.2)",
                                padding: 0.5,
                            }}>
                                <ClickAwayListener onClickAway={handleClose}>
                                    <Box
                                        sx={{ p: 1, display: 'flex', gap: 1 }}
                                    >
                                        <TextField
                                            size="small"
                                            placeholder={inputConfig.placeholder}
                                            value={inputValue}
                                            onChange={handleInputChange}
                                            onKeyDown={handleKeyDown}
                                            onClick={handleTextFieldClick}
                                            autoFocus
                                            inputProps={{
                                                onKeyDown: handleKeyDown,
                                                onClick: handleTextFieldClick
                                            }}
                                        />
                                        <Button
                                            size="medium"
                                            variant="contained"
                                            onClick={handleInputSubmit}
                                            disabled={!inputValue.trim()}
                                        >
                                            {inputConfig.buttonLabel}
                                        </Button>
                                    </Box>
                                </ClickAwayListener>
                            </Paper>
                        </Grow>
                    )}
                </Popper>
            )}
        </Box>
    );
}