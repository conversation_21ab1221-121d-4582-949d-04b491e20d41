import { Box, Typography } from "@mui/material";
import { useSelector } from "react-redux";
import {
  selectDocumentMetadataDocumentStatus,
  selectDocumentMetadataDueDate,
  selectDocumentMetadataSummary,
  selectDocumentMetadataTitle,
} from "~/lib/features/documentMetadataSelectors";

const DocumentDetails: React.FC = () => {
  const documentTitle = useSelector(selectDocumentMetadataTitle);
  const documentSummary = useSelector(selectDocumentMetadataSummary);
  const documentDueDate = useSelector(selectDocumentMetadataDueDate);
  const documentStatus = useSelector(selectDocumentMetadataDocumentStatus);
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6">{documentTitle}</Typography>
      <Typography variant="h6">Summary:</Typography>
      <Typography variant="body2">{documentSummary}</Typography>
      <Typography variant="h6">Due Date:</Typography>
      <Typography variant="body2">{documentDueDate}</Typography>
      <Typography variant="h6">Status:</Typography>
      <Typography variant="body2">{documentStatus}</Typography>
    </Box>
  );
};

export default DocumentDetails;
