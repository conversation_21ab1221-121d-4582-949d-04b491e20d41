import React from 'react';
import { But<PERSON> } from '@mui/material';
import { openChat } from '~/app/add-in-word/wordUtils';
import { ChatIcon } from '../Icons/ChatIcon';
import { useBaseUrl } from '../hooks/useBaseUrl';
import { useUserMSAddInSettings } from '../hooks/useUserMSAddInSettings';

export default function LaunchChat() {
    const orgDomain = useBaseUrl()
    const { data: msAddInSettings, isLoading: userDataLoading } = useUserMSAddInSettings();

    if (!orgDomain || userDataLoading) {
        return null;
    }

    const { chatHeight, chatWidth } = msAddInSettings;

    return (
        <Button onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            openChat({
                domain: orgDomain,
                chatHeight,
                chatWidth,
            });
        }}>
            <ChatIcon width={20} />
        </Button>

    );
}
