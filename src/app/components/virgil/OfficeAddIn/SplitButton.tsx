import * as React from 'react';
import Button from '@mui/material/Button';
import ButtonGroup from '@mui/material/ButtonGroup';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grow from '@mui/material/Grow';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import { ButtonProps } from '@mui/material/Button';

interface SplitButtonOption {
    label: string;
    clickAction: (e: React.MouseEvent<HTMLElement>) => void;
}

interface SplitButtonProps extends Omit<ButtonProps, 'onClick'> {
    options: SplitButtonOption[];
}

export default function SplitButton({ options, ...buttonProps }: SplitButtonProps) {
    const [open, setOpen] = React.useState(false);
    const anchorRef = React.useRef<HTMLDivElement>(null);
    const { fullWidth, ...restBtnProps } = buttonProps;

    if (!options.length) {
        return null;
    }

    const [mainOption, ...dropdownOptions] = options;
    if (!mainOption) {
        return null;
    }

    const handleMainClick = (event: React.MouseEvent<HTMLElement>) => {
        event.preventDefault();
        event.stopPropagation();
        mainOption.clickAction(event);
    };

    const handleMenuItemClick = (
        event: React.MouseEvent<HTMLLIElement, MouseEvent>,
        option: SplitButtonOption,
    ) => {
        event.preventDefault();
        event.stopPropagation();
        option.clickAction(event);
        setOpen(false);
    };

    const handleToggle = (e: React.MouseEvent<HTMLElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setOpen((prevOpen) => !prevOpen);
    };

    const handleClose = (event: Event) => {
        if (
            anchorRef.current &&
            anchorRef.current.contains(event.target as HTMLElement)
        ) {
            return;
        }

        setOpen(false);
    };

    return (
        <React.Fragment>
            <ButtonGroup
                variant="contained"
                ref={anchorRef}
                aria-label="Button group with a nested menu"
            >
                <Button onClick={handleMainClick} {...buttonProps}>{mainOption.label}</Button>
                {dropdownOptions.length > 0 && (
                    <Button
                        aria-controls={open ? 'split-button-menu' : undefined}
                        aria-expanded={open ? 'true' : undefined}
                        aria-label="select merge strategy"
                        aria-haspopup="menu"
                        onClick={handleToggle}
                        {...restBtnProps}
                    >
                        <ArrowDropDownIcon />
                    </Button>
                )}
            </ButtonGroup>
            {dropdownOptions.length > 0 && (
                <Popper
                    sx={{
                        zIndex: 999999,
                        backgroundColor: "white",
                        border: "1px solid #e0e0e0",
                        position: 'relative'
                    }}
                    open={open}
                    anchorEl={anchorRef.current}
                    role={undefined}
                    transition
                    placement="bottom-start"
                    modifiers={[
                        {
                            name: 'preventOverflow',
                            enabled: true,
                            options: {
                                altAxis: true,
                                altBoundary: true,
                                tether: true,
                                rootBoundary: 'viewport',
                                padding: 8
                            }
                        }
                    ]}
                >
                    {({ TransitionProps, placement }) => (
                        <Grow
                            {...TransitionProps}
                            style={{
                                transformOrigin:
                                    placement === 'bottom' ? 'center top' : 'center bottom',
                            }}
                        >
                            <Paper sx={{
                                gap: 1,
                                boxShadow: "0 0 5px rgba(0, 0, 0, 0.2)",
                                padding: 1,
                            }}>
                                <ClickAwayListener onClickAway={handleClose}>
                                    <MenuList id="split-button-menu" autoFocusItem>
                                        {dropdownOptions.map((option) => (
                                            <MenuItem
                                                key={option.label}
                                                onClick={(event) => handleMenuItemClick(event, option)}
                                            >
                                                {option.label}
                                            </MenuItem>
                                        ))}
                                    </MenuList>
                                </ClickAwayListener>
                            </Paper>
                        </Grow>
                    )}
                </Popper>
            )}
        </React.Fragment>
    );
}