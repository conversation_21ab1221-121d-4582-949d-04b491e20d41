import { useState } from 'react';
import {
    Box,
    Button,
    CircularProgress,
    Divider,
    FormControl,
    FormControlLabel,
    FormGroup,
    FormHelperText,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    Switch,
    TextField,
    Typography,
} from '@mui/material';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useUpdateAddInSettings } from '../../hooks/useUpdateAddInSettings';
import Loading from '~/app/loading';

export interface SettingsFormData {
    insertTextColor: string;
    chatHeight: number;
    chatWidth: number;
    fontSize: number;
    bold: boolean;
    italic: boolean;
    underline: boolean;
}

export default function AddInSettings({
    insertTextColor,
    chatHeight,
    chatWidth,
    fontSize,
    bold,
    italic,
    underline,
}: SettingsFormData) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { mutateAsync: updateAddInSettings } = useUpdateAddInSettings();

    const { register, handleSubmit, formState: { errors }, watch, setValue } = useForm<SettingsFormData>({
        defaultValues: {
            insertTextColor: insertTextColor || '#000000',
            chatHeight: chatHeight || 50,
            chatWidth: chatWidth || 50,
            fontSize: fontSize || 12,
            bold: bold || false,
            italic: italic || false,
            underline: underline || false,
        }
    });

    const onSubmit = async (data: SettingsFormData) => {
        try {
            setIsSubmitting(true);
            await updateAddInSettings({
                msAddInSettings: {
                    insertTextColor: data.insertTextColor,
                    chatHeight: data.chatHeight,
                    chatWidth: data.chatWidth,
                    fontSize: data.fontSize,
                    bold: data.bold,
                    italic: data.italic,
                    underline: data.underline,
                }
            });
            toast.success('Settings saved successfully');
        } catch (error) {
            toast.error('Failed to save settings');
            console.error('Error saving settings:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <Stack spacing={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <Divider sx={{ flex: 1 }} />
                    <Typography variant="body2" sx={{ px: 2 }}>
                        Chat settings
                    </Typography>
                    <Divider sx={{ flex: 1 }} />
                </Box>
                <FormControl fullWidth>
                    <InputLabel>Chat Size</InputLabel>
                    <Select
                        label="Chat Size"
                        value={`${watch('chatHeight')},${watch('chatWidth')}`}
                        onChange={(e) => {
                            const [height, width] = e.target.value.split(',').map(Number);
                            setValue('chatHeight', height || 50);
                            setValue('chatWidth', width || 50);
                        }}
                    >
                        <MenuItem value="30,30">Small</MenuItem>
                        <MenuItem value="50,50">Medium</MenuItem>
                        <MenuItem value="70,70">Large</MenuItem>
                    </Select>
                </FormControl>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <Divider sx={{ flex: 1 }} />
                    <Typography variant="body2" sx={{ px: 2 }}>
                        Insert Text Settings
                    </Typography>
                    <Divider sx={{ flex: 1 }} />
                </Box>
                <TextField
                    sx={{
                        width: '150px',
                    }}
                    label="Insert Text Color"
                    type="color"
                    {...register('insertTextColor')}
                    error={!!errors.insertTextColor}
                    helperText={errors.insertTextColor?.message}
                />

                <FormControl fullWidth>
                    <InputLabel>Font Size</InputLabel>
                    <Select
                        label="Font Size"
                        value={watch('fontSize')}
                        error={!!errors.fontSize}
                        onChange={(e) => {
                            setValue('fontSize', Number(e.target.value) || 12);
                        }}
                    >
                        <MenuItem value={10}>X Small (10px)</MenuItem>
                        <MenuItem value={12}>Small (12px)</MenuItem>
                        <MenuItem value={14}>Medium (14px)</MenuItem>
                        <MenuItem value={16}>Large (16px)</MenuItem>
                        <MenuItem value={18}>Extra Large (18px)</MenuItem>
                    </Select>
                    {errors.fontSize && (
                        <FormHelperText error>{errors.fontSize.message}</FormHelperText>
                    )}
                </FormControl>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Switch
                                {...register('bold')}
                                checked={watch('bold')}
                            />
                        }
                        label="Bold"
                    />
                    <FormControlLabel
                        control={
                            <Switch
                                {...register('italic')}
                                checked={watch('italic')}
                            />
                        }
                        label="Italic"
                    />
                    <FormControlLabel
                        control={
                            <Switch
                                {...register('underline')}
                                checked={watch('underline')}
                            />
                        }
                        label="Underline"
                    />
                </FormGroup>

                <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={isSubmitting}
                    startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
                >
                    {isSubmitting ? 'Saving...' : 'Save Settings'}
                </Button>
            </Stack>
        </form>
    );
}
