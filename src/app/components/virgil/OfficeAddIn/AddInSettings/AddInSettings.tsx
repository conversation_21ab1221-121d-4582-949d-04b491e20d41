import { useUserMSAddInSettings } from '../../hooks/useUserMSAddInSettings';
import Loading from '~/app/loading';
import AddInSettingsForm from './AddInSettingsForm';
import { Stack, Typography } from '@mui/material';
import { Box } from '@mui/material';

export default function AddInSettings() {
    const { data: msAddInSettings, isLoading: userDataLoading } = useUserMSAddInSettings();
    if (userDataLoading) return <Loading />

    return (
        <Box sx={{ p: 3 }}>
            <Stack spacing={3}>
                <Typography variant="h3">
                    Settings
                </Typography>
                <AddInSettingsForm
                    insertTextColor={msAddInSettings.insertTextColor || '#000000'}
                    chatHeight={msAddInSettings.chatHeight || 40}
                    chatWidth={msAddInSettings.chatWidth || 40}
                    fontSize={msAddInSettings.fontSize || 12}
                    bold={msAddInSettings.bold || false}
                    italic={msAddInSettings.italic || false}
                    underline={msAddInSettings.underline || false}
                />
            </Stack>
        </Box>
    );
}
