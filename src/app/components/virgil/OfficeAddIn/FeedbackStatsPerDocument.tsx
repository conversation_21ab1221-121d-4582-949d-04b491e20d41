import { useSelector } from "react-redux";
import { selectDocumentId } from "~/lib/features/documentSelectors";
import { api } from "~/trpc/react";
import { Box, Button, LinearProgress, Skeleton, Typography, useTheme } from "@mui/material";
import { useMemo } from "react";
import { useDDQuestionsCount } from "../hooks/useDDQuestionsCount";
import { toast } from "~/components/snackbar";

export const FeedbackStatsPerDocument = () => {
  const documentId = useSelector(selectDocumentId);
  const theme = useTheme();

  const { data: feedbackStats, isLoading, refetch } = api.feedback.getDocumentFeedbackStats.useQuery(
    { documentId: documentId ?? "" },
    {
      enabled: !!documentId,
    }
  );
  const { mutate: resetFeedback, isPending: isResettingFeedback } = api.feedback.resetFeedbackPerDocument.useMutation({
    onSuccess: () => {
      toast.success("Feedback reset");
      refetch();
    },
    onError: (error) => {
      toast.error("Error resetting feedback");
    }
  });
  const { totalQuestions } = useDDQuestionsCount();

  const stats = useMemo(() => {
    if (!feedbackStats || !totalQuestions) return null;
    const { goodQuestions, badQuestions } = feedbackStats;
    const notRatedQuestions = totalQuestions - goodQuestions - badQuestions;

    return {
      good: (goodQuestions / totalQuestions) * 100,
      bad: (badQuestions / totalQuestions) * 100,
      notRated: (notRatedQuestions / totalQuestions) * 100,
      counts: {
        good: goodQuestions,
        bad: badQuestions,
        notRated: notRatedQuestions,
        total: totalQuestions
      }
    };
  }, [feedbackStats, totalQuestions]);

  if (isLoading) {
    return <Box sx={{ width: "100%", mt: 2 }}>
      <Typography variant="body2" color="text.secondary">
        Question Feedback Progress
      </Typography>
      <LinearProgress variant="indeterminate" />
    </Box>
  }

  if (!stats) {
    return null;
  }

  return (
    <Box sx={{ width: "100%", mt: 2 }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}>
        <Typography variant="body2" color="text.secondary">
          Question Feedback Progress
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {Math.floor(stats.counts.good / stats.counts.total * 100)}% good
        </Typography>
      </Box>
      <Box sx={{ position: 'relative', height: 8 }}>
        <LinearProgress
          variant="determinate"
          value={100}
          sx={{
            position: 'absolute',
            width: '100%',
            height: 8,
            borderRadius: 4,
            backgroundColor: 'transparent',
            "& .MuiLinearProgress-bar": {
              borderRadius: 4,
              backgroundColor: theme.palette.info.main,
            },
          }}
        />
        <LinearProgress
          variant="determinate"
          value={stats.good}
          sx={{
            position: 'absolute',
            width: '100%',
            height: 8,
            borderRadius: 4,
            backgroundColor: 'transparent',
            "& .MuiLinearProgress-bar": {
              borderRadius: 4,
              backgroundColor: theme.palette.success.main,
            },
          }}
        />
        <LinearProgress
          variant="determinate"
          value={stats.good + stats.bad}
          sx={{
            position: 'absolute',
            width: '100%',
            height: 8,
            borderRadius: 4,
            backgroundColor: 'transparent',
            "& .MuiLinearProgress-bar": {
              borderRadius: 4,
              backgroundColor: theme.palette.error.main,
              transform: `translateX(${stats.good}%)`,
              width: `${stats.bad}%`,
            },
          }}
        />
      </Box>
      <Box sx={{ display: "flex", justifyContent: "space-between", mt: 1 }}>
        <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: theme.palette.success.main }} />
            Good: {stats.counts.good}
          </Box>
          •
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: theme.palette.error.main }} />
            Bad: {stats.counts.bad}
          </Box>
          •
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: theme.palette.info.main }} />
            Not Rated: {stats.counts.notRated}
          </Box>
          •
          Total: {stats.counts.total}
        </Typography>
      </Box>
      <Button variant="outlined" color="primary" loading={isResettingFeedback} onClick={() => resetFeedback({ documentId: documentId ?? "" })}>
        Reset Feedback
      </Button>
    </Box>
  );
};
