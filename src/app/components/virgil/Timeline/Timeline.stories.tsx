import React from "react";
import { Meta, StoryFn } from "@storybook/react";
import { DocumentEditTimeline } from "./Timeline";
import { mockEdits } from "~/app/add-in-excel/mockEdits";

export default {
    title: "Components/DocumentEditTimeline",
    component: DocumentEditTimeline,
    parameters: {
        layout: "fullscreen", // Full-width layout to better showcase the timeline
    },
} as Meta;

const Template: StoryFn<typeof DocumentEditTimeline> = (args) => (
    <div style={{ maxWidth: "800px", margin: "0 auto", padding: "16px" }}>
        <DocumentEditTimeline {...args} />
    </div>
);

export const Default = Template.bind({});
Default.args = {
    edits: mockEdits, // Mock edits data
};

export const NoEdits = Template.bind({});
NoEdits.args = {
    edits: [], // Empty edits list
};
