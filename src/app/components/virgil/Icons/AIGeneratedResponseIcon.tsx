import React from "react";
import { CONFIG } from 'src/config-global';
import { GenericIcon, GenericIconWithBackground } from "./GenericIcon";

export const AIGeneratedResponseIcon = ({
    color = '#207EEC',
    width = 16,
}: {
    color?: string
    width?: number
}) => {
    return <GenericIcon
        src={`${CONFIG.site.basePath}/icons/magic.svg`}
        color={color}
        width={width}
    />
}

export const AIGeneratedResponseIconWithBackground = ({
    color = '#207EEC',
    width = 16,
}: {
    color?: string
    width?: number
}) => {
    return <GenericIconWithBackground
        color={color}
        width={width}
        src={`${CONFIG.site.basePath}/icons/magic.svg`}
    />
}