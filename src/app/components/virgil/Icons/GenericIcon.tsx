import React from "react";
import { SvgColor } from "~/components/svg-color/svg-color"
import { CONFIG } from 'src/config-global';
import Box from "@mui/material/Box/Box";

export const GenericIcon = ({
    color = '#8620EC',
    width = 16,
    src,
}: {
    color?: string,
    width?: number,
    src: string,
}) => {
    return <SvgColor
        src={src}
        sx={{
            width: width,
            color: color,
        }}
    />
}

export const GenericIconWithBackground = ({
    color = '#8620EC',
    width = 16,
    outerWidth = 28,
    src,
}: {
    color?: string,
    width?: number,
    outerWidth?: number,
    src: string
}) => {
    return <Box
        sx={{
            backgroundColor: `${color}1F`,
            m: 0,
            borderRadius: "6px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: outerWidth,
            height: outerWidth,
        }}
    >
        <GenericIcon color={color} width={width} src={src} />
    </Box>
}