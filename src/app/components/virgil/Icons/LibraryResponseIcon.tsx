import React from "react";
import { CONFIG } from 'src/config-global';
import { GenericIcon, GenericIconWithBackground } from "./GenericIcon";

export const LibraryResponseIcon = ({
    color = '#8620EC',
    width = 16,
}: {
    color?: string
    width?: number
}) => {
    return <GenericIcon
        src={`${CONFIG.site.basePath}/icons/response-library.svg`}
        color={color}
        width={width}
    />
}

export const LibraryResponseIconWithBackground = ({
    color = '#8620EC',
    width = 16,
}: {
    color?: string
    width?: number
}) => {
    return <GenericIconWithBackground
        color={color}
        width={width}
        src={`${CONFIG.site.basePath}/icons/response-library.svg`}
    />
}