import { Label } from "~/components/label/label";
import { ResponseStatus } from "@prisma/client";

const ResponseStatusLabel = ({ responseStatus }: { responseStatus: ResponseStatus }) => {
    return (
        <Label
            variant="soft"
            color={
                responseStatus === ResponseStatus.APPROVED
                    ? "success"
                    : responseStatus === ResponseStatus.REJECTED
                        ? "warning"
                        : responseStatus === ResponseStatus.PENDING_APPROVAL
                            ? "info"
                            : "default"
            }
            sx={{ width: "fit-content" }}
        >
            {responseStatus.toLowerCase()}
        </Label>
    );
};

export default ResponseStatusLabel;