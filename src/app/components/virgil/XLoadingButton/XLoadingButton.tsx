import { Button, ButtonProps } from "@mui/material";
import { FC } from "react";
type Props = ButtonProps & {
  style: "dark" | "light";
  fullWidth?: boolean;
  loading: boolean;
};
const XLoadingButton: FC<Props> = (props) => {
  const { style, fullWidth, loading, ...rest } = props;
  return (
    <Button
      loading={loading}
      sx={{
        paddingTop: 2.5,
        paddingBottom: 2.5,
        paddingLeft: 1,
        paddingRight: 1,
        width: fullWidth ? "100%" : "auto",
      }}
      variant={style === "dark" ? "contained" : "outlined"}
      size="small"
      {...rest}
    >
      {props.children}
    </Button>
  );
};

export default XLoadingButton;
