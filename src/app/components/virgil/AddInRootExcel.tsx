import DocumentDetails from "./OfficeAddIn/DocumentDetails";
import AddInSettings from "./OfficeAddIn/AddInSettings/AddInSettings";

import { useSelector } from "react-redux";
import { selectAddInNavigationPath } from "~/lib/features/addInUISelectors";
import DocumentStatusPage from "./OfficeAddIn/DocumentStatusPage";
import DDQPromptListContainer from "./OfficeAddIn/Excel/DDQPromptListContainer";
import DetailedPromptAnswerContainer from "./OfficeAddIn/Excel/DetailedPromptAnswerContainer";

const AddInRootExcel = () => {
    const addInNavigationPath = useSelector(selectAddInNavigationPath);
    switch (addInNavigationPath) {
        case 'document-details':
            return <DocumentDetails />;
        case 'settings':
            return <AddInSettings />;
        case 'collaboration':
            return <>Collaboration</>;
        case 'alerts':
            return <>Alerts</>;
        case 'detailed-answer':
            return <DetailedPromptAnswerContainer />;
        case 'document-status':
            return <DocumentStatusPage />;
        case 'ddq':
            return <DDQPromptListContainer />;
        default:
            return <></>;
    }
}

export default AddInRootExcel;