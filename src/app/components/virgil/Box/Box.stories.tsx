import React from "react";
import Box from "./Box"; // Adjust the path based on your project structure
import { Meta, StoryFn } from "@storybook/react";

// Meta information for the story
export default {
    title: "Components/Box", // This defines the category and name in Storybook
    component: Box,
} as Meta;

// Template for rendering the Box component
const Template: StoryFn<typeof Box> = (args) => <Box {...args} />;

// Default story
export const Default = Template.bind({});
Default.args = {
    children: "This is a Box component with default styles.",
};

// Example story with custom content
export const CustomContent = Template.bind({});
CustomContent.args = {
    children: (
        <div>
            <h2>Custom Content</h2>
            <p>This Box can hold any React elements as children!</p>
        </div>
    ),
};
