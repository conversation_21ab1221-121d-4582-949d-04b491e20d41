import { Box as BoxMUI, SxProps, Theme } from "@mui/material";
import React, { PropsWithChildren } from "react";

const Box: React.FC<PropsWithChildren & { sx?: SxProps<Theme> }> = ({ children, sx }) => {
    return (
        <BoxMUI
            sx={{
                margin: "0 auto",
                padding: 2,
                border: "1px solid #ddd",
                borderRadius: 2,
                marginTop: 2,
                ...sx,
            }}
        >
            {children}
        </BoxMUI>
    );
};

export default Box;
