import React, { useState } from "react";
import { Box, Typography, IconButton } from "@mui/material";
import ZoomOutMapIcon from "@mui/icons-material/ZoomOutMap";

interface ExpandableTextAreaProps {
    text: string;
    maxHeight?: number;
}

const ExpandableTextArea: React.FC<ExpandableTextAreaProps> = ({
    text,
    maxHeight = 60,
}) => {
    const [expanded, setExpanded] = useState(true); // Changed to true for default expanded state

    return (
        <Box
            sx={{
                width: "100%",
                margin: "0 auto",
                padding: 2,
                paddingRight: 4,
                border: "1px solid #ddd",
                borderRadius: 2,
                marginTop: 2,
                backgroundColor: "#f9f9f9",
                position: "relative",
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
                overflow: "hidden",
            }}
        >
            <Box
                sx={{
                    position: "relative",
                    maxHeight: expanded ? "none" : maxHeight,
                    overflow: "hidden",
                }}
            >
                <Typography
                    variant="body2"
                    sx={{
                        whiteSpace: "pre-line",
                        lineHeight: 1.6,
                    }}
                >
                    {text}
                </Typography>

                {!expanded && (
                    <Box
                        sx={{
                            position: "absolute",
                            bottom: 0,
                            left: 0,
                            right: 0,
                            height: 20, // Height of the fade effect
                            background: "linear-gradient(to bottom, rgba(255, 255, 255, 0), #f9f9f9)",
                        }}
                    />
                )}
            </Box>
            <IconButton
                onClick={() => setExpanded(!expanded)}
                sx={{
                    position: "absolute",
                    top: 8,
                    right: 8,
                    color: "#666",
                }}
                aria-label={expanded ? "Collapse" : "Expand"}
            >
                <ZoomOutMapIcon fontSize="small" />
            </IconButton>
        </Box>
    );
};

export default ExpandableTextArea;
