import React from "react";
import { Meta, StoryFn } from "@storybook/react";
import ExpandableTextArea from "./ExpandableTextArea";

export default {
    title: "Components/ExpandableTextArea",
    component: ExpandableTextArea,
    parameters: {
        layout: "centered",
    },
} as Meta;

const Template: StoryFn<typeof ExpandableTextArea> = (args) => <ExpandableTextArea {...args} />;

export const Default = Template.bind({});
Default.args = {
    text: `This is a short example of text. Expand the text area to see more content.`,
};

export const CustomHeight = Template.bind({});
CustomHeight.args = {
    text: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras sit amet nibh non nulla dignissim vehicula. Vestibulum ac facilisis sapien. Donec non justo mi. Etiam nec metus lorem. Phasellus quis gravida lorem, a tincidunt metus. Quisque pellentesque est vel dolor bibendum vulputate. In hac habitasse platea dictumst.
Donec rutrum, ipsum ac vehicula condimentum, orci nisl congue justo, a pulvinar nisl lacus non neque. Nam aliquet pellentesque magna, vel fringilla metus facilisis sed. Ut ac massa sit amet elit vehicula ultrices non vitae eros. Aenean at suscipit justo. Integer non mi at orci dignissim maximus nec ut elit. Proin pretium justo eget sapien suscipit, at pellentesque ipsum consectetur. Suspendisse consequat purus ac felis mattis, at rutrum elit facilisis.`,
    maxHeight: 100,
};

export const LongText = Template.bind({});
LongText.args = {
    text: `This is an example of very long text. It showcases how the ExpandableTextArea component behaves when the content is extensive. This can include multiple paragraphs, bullet points, or other text content. 
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum at enim a lorem consequat bibendum sit amet sit amet lorem. Praesent et urna eget tortor facilisis eleifend. Nullam sed dolor sem. Quisque et leo at sapien tincidunt consequat. Integer facilisis fermentum feugiat. Nam facilisis lorem et mauris varius, ac consequat eros feugiat. Nulla facilisi. Donec malesuada sem quis ultricies feugiat. Integer ac nibh sit amet nisi pharetra efficitur.
In aliquet justo in velit fermentum, vitae pulvinar libero tristique. Praesent ac fermentum lorem. Nulla lacinia lectus vel nunc sollicitudin, quis fermentum metus scelerisque. Aenean eget nisi nec justo vehicula sagittis. Curabitur vehicula ultrices nunc, ac ultrices lacus convallis non. Duis pharetra vehicula enim, sed scelerisque magna euismod sed.
Suspendisse ut velit at magna bibendum cursus a sit amet augue. Vestibulum venenatis enim et ornare luctus. Ut luctus, libero quis tristique tempor, lacus orci pharetra nisl, ac pharetra nulla lorem sit amet justo.`,
};
