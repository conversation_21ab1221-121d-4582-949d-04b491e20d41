import DocumentDetails from "./OfficeAddIn/DocumentDetails";
import AddInSettings from "./OfficeAddIn/AddInSettings/AddInSettings";

import { useSelector } from "react-redux";
import { selectAddInNavigationPath } from "~/lib/features/addInUISelectors";
import DDQListPaginatedContainer from "./OfficeAddIn/IndexView/DDQListPaginatedContainer";
import DetailedAnswerContainer from "./OfficeAddIn/DetailedView/DetailedAnswerContainer";
import DocumentStatusPage from "./OfficeAddIn/DocumentStatusPage";
import DocumentFeedbackStatusPage from "./OfficeAddIn/DocumentFeedbackStatusPage";
import DetailOverlay from "./OfficeAddIn/DetailOverlay";

const AddInRootWord = () => {
    return (
        <>
            <DDQListPaginatedContainer />
            <DetailOverlay />
        </>
    );
}

export default AddInRootWord;