import "src/global.css";

// ----------------------------------------------------------------------

import type { Viewport } from "next";

import { CONFIG } from "src/config-global";
import { LocalizationProvider } from "src/locales";
import { I18nProvider } from "src/locales/i18n-provider";
import { detectLanguage } from "src/locales/server";
import { primary } from "src/theme/core/palette";
import { ThemeProvider } from "src/theme/theme-provider";

import { ClerkProvider } from "@clerk/nextjs";
import InitColorSchemeScript from "@mui/material/InitColorSchemeScript";
import { MotionLazy } from "src/components/animate/motion-lazy";
import { ProgressBar } from "src/components/progress-bar";
import { defaultSettings, SettingsProvider } from "src/components/settings";
import { detectSettings } from "src/components/settings/server";
import { Snackbar } from "src/components/snackbar";
import { TRPCReactProvider } from "~/trpc/react";
import MuiXLicense from "./muixlicense";
import { OrgProvider } from "./providers/OrgProvider";

// ----------------------------------------------------------------------

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: primary.main,
};

type Props = {
  children: React.ReactNode;
};

export default async function RootLayout({ children }: Props) {
  const lang = CONFIG.isStaticExport ? "en" : await detectLanguage();
  const settings = CONFIG.isStaticExport
    ? defaultSettings
    : await detectSettings();

  return (
    <html lang={lang ?? "en"} suppressHydrationWarning>
      <body className=".mode-light">
        <ClerkProvider>
          <I18nProvider lang={CONFIG.isStaticExport ? undefined : lang}>
            <LocalizationProvider>
              <TRPCReactProvider>
                <SettingsProvider
                  settings={settings}
                  caches={CONFIG.isStaticExport ? "localStorage" : "cookie"}
                >
                  <InitColorSchemeScript
                    modeStorageKey="theme-mode"
                    attribute="class"
                    defaultMode="light"
                  />

                  <ThemeProvider>
                    <OrgProvider>
                      <MotionLazy>
                        <Snackbar />
                        <ProgressBar />
                        {children}
                        <MuiXLicense />
                      </MotionLazy>
                    </OrgProvider>
                  </ThemeProvider>
                </SettingsProvider>
              </TRPCReactProvider>
            </LocalizationProvider>
          </I18nProvider>
        </ClerkProvider>
      </body>
    </html>
  );
}
