"use client";

import { useEffect, useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Box,
  Typography,
  Switch,
  FormControlLabel,
} from "@mui/material";
import {
  MDXEditor,
  headingsPlugin,
  listsPlugin,
  quotePlugin,
  markdownShortcutPlugin,
  linkPlugin,
  linkDialogPlugin,
  tablePlugin,
  thematicBreakPlugin,
  frontmatterPlugin,
  codeBlockPlugin,
  codeMirrorPlugin,
  directivesPlugin,
  AdmonitionDirectiveDescriptor,
  diffSourcePlugin,
  imagePlugin,
  toolbarPlugin,
  BoldItalicUnderlineToggles,
  BlockTypeSelect,
  CreateLink,
  InsertTable,
  InsertThematicBreak,
  ListsToggle,
  UndoRedo,
  InsertImage,
} from "@mdxeditor/editor";
import "@mdxeditor/editor/style.css";

interface EditRowModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (editedRow: string[]) => void;
  headers: string[];
  row: string[];
  rowIndex: number;
}

export default function EditRowModal({
  open,
  onClose,
  onSave,
  headers,
  row,
  rowIndex,
}: EditRowModalProps) {
  const [editedRow, setEditedRow] = useState<string[]>(row);
  const [isEditMode, setIsEditMode] = useState(true);

  useEffect(() => {
    setEditedRow(row);
  }, [row]);

  const handleCellChange = (index: number, value: string) => {
    const newRow = [...editedRow];
    newRow[index] = value;
    setEditedRow(newRow);
  };

  const handleClose = () => {
    onSave(editedRow);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth={false}
      fullWidth
      PaperProps={{
        sx: {
          width: "100%",
          height: "100%",
          maxWidth: "100%",
          maxHeight: "100%",
          m: 0,
          borderRadius: 0,
        },
      }}
    >
      <DialogTitle
        sx={{
          borderBottom: "1px solid",
          borderColor: "divider",
          p: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography>Edit Row {rowIndex + 1}</Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={isEditMode}
                onChange={(e) => setIsEditMode(e.target.checked)}
                color="primary"
              />
            }
            label={isEditMode ? "Edit Mode" : "View Mode"}
          />
          <Button onClick={handleClose}>Close</Button>
        </Box>
      </DialogTitle>
      <DialogContent
        sx={{
          p: 2,
          height: "calc(100% - 130px)",
          overflow: "auto",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 3,
          }}
        >
          {headers.map((header, index) => (
            <Box key={index}>
              <Typography variant="subtitle2" gutterBottom>
                {header}
              </Typography>
              <Box
                sx={{
                  border: "1px solid",
                  borderColor: "divider",
                  borderRadius: 1,
                  overflow: "hidden",
                  "& .mdxeditor": {
                    minHeight: "200px",
                  },
                }}
              >
                {isEditMode ? (
                  <MDXEditor
                    markdown={editedRow[index] ?? ""}
                    onChange={(value) => handleCellChange(index, value)}
                    plugins={[
                      headingsPlugin(),
                      listsPlugin(),
                      quotePlugin(),
                      markdownShortcutPlugin(),
                      linkPlugin(),
                      linkDialogPlugin(),
                      tablePlugin(),
                      thematicBreakPlugin(),
                      frontmatterPlugin(),
                      codeBlockPlugin(),
                      codeMirrorPlugin(),
                      directivesPlugin({
                        directiveDescriptors: [AdmonitionDirectiveDescriptor],
                      }),
                      diffSourcePlugin(),
                      imagePlugin(),
                      toolbarPlugin({
                        toolbarContents: () => (
                          <>
                            <UndoRedo />
                            <BoldItalicUnderlineToggles />
                            <BlockTypeSelect />
                            <CreateLink />
                            <InsertTable />
                            <InsertThematicBreak />
                            <ListsToggle />
                            <InsertImage />
                          </>
                        ),
                      }),
                    ]}
                  />
                ) : (
                  <Box
                    sx={{
                      p: 2,
                      minHeight: "200px",
                      whiteSpace: "pre-wrap",
                      fontFamily: "monospace",
                      bgcolor: "grey.50",
                    }}
                  >
                    {editedRow[index]}
                  </Box>
                )}
              </Box>
            </Box>
          ))}
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          borderTop: "1px solid",
          borderColor: "divider",
          p: 2,
        }}
      ></DialogActions>
    </Dialog>
  );
}
