"use client";

import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Switch,
  FormControlLabel,
} from "@mui/material";
import {
  MDXEditor,
  headingsPlugin,
  listsPlugin,
  quotePlugin,
  markdownShortcutPlugin,
  linkPlugin,
  linkDialogPlugin,
  tablePlugin,
  thematicBreakPlugin,
  frontmatterPlugin,
  codeBlockPlugin,
  codeMirrorPlugin,
  directivesPlugin,
  AdmonitionDirectiveDescriptor,
  diffSourcePlugin,
  imagePlugin,
  toolbarPlugin,
  BoldItalicUnderlineToggles,
  BlockTypeSelect,
  CreateLink,
  InsertTable,
  InsertThematicBreak,
  ListsToggle,
  UndoRedo,
  InsertImage,
} from "@mdxeditor/editor";
import "@mdxeditor/editor/style.css";

interface EditCellModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (value: string) => void;
  header: string;
  value: string;
}

export default function EditCellModal({
  open,
  onClose,
  onSave,
  header,
  value,
}: EditCellModalProps) {
  const [editedValue, setEditedValue] = useState(value);
  const [isEditMode, setIsEditMode] = useState(true);

  useEffect(() => {
    setEditedValue(value);
  }, [value]);

  const handleClose = () => {
    onSave(editedValue);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle
        sx={{
          borderBottom: "1px solid",
          borderColor: "divider",
          p: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography>Edit {header}</Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={isEditMode}
                onChange={(e) => setIsEditMode(e.target.checked)}
                color="primary"
              />
            }
            label={isEditMode ? "Edit Mode" : "View Mode"}
          />
          <Button onClick={handleClose}>Close</Button>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ p: 2 }}>
        <Box
          sx={{
            border: "1px solid",
            borderColor: "divider",
            borderRadius: 1,
            overflow: "hidden",
            "& .mdxeditor": {
              minHeight: "200px",
            },
          }}
        >
          {isEditMode ? (
            <MDXEditor
              markdown={editedValue?.toString() || ""}
              onChange={(value) => setEditedValue(value)}
              plugins={[
                headingsPlugin(),
                listsPlugin(),
                quotePlugin(),
                markdownShortcutPlugin(),
                linkPlugin(),
                linkDialogPlugin(),
                tablePlugin(),
                thematicBreakPlugin(),
                frontmatterPlugin(),
                codeBlockPlugin(),
                codeMirrorPlugin(),
                directivesPlugin({
                  directiveDescriptors: [AdmonitionDirectiveDescriptor],
                }),
                diffSourcePlugin(),
                imagePlugin(),
                toolbarPlugin({
                  toolbarContents: () => (
                    <>
                      <UndoRedo />
                      <BoldItalicUnderlineToggles />
                      <BlockTypeSelect />
                      <CreateLink />
                      <InsertTable />
                      <InsertThematicBreak />
                      <ListsToggle />
                      <InsertImage />
                    </>
                  ),
                }),
              ]}
            />
          ) : (
            <Box
              sx={{
                p: 2,
                minHeight: "200px",
                whiteSpace: "pre-wrap",
                fontFamily: "monospace",
                bgcolor: "grey.50",
              }}
            >
              {editedValue}
            </Box>
          )}
        </Box>
      </DialogContent>
    </Dialog>
  );
}
