"use client";

import {
  <PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
} from "@mui/material";
import { useState } from "react";
import { createRoot } from "react-dom/client";

interface SheetSelectorProps {
  open: boolean;
  onClose: () => void;
  onSheetSelect: (sheetName: string) => void;
  sheetNames: string[];
}

export function openSheetSelection(sheetNames: string[]): Promise<string> {
  return new Promise((resolve) => {
    const container = document.createElement("div");
    document.body.appendChild(container);

    const root = createRoot(container);

    const handleClose = (result: string) => {
      root.unmount();
      container.remove();
      resolve(result);
    };

    root.render(
      <SheetSelector
        open={true}
        onClose={() => handleClose("")}
        onSheetSelect={(sheetName) => handleClose(sheetName)}
        sheetNames={sheetNames}
      />,
    );
  });
}

export default function SheetSelector({
  open,
  onClose,
  onSheetSelect,
  sheetNames,
}: SheetSelectorProps) {
  const [selected, setSelected] = useState(sheetNames[0]);

  const handleSelect = (sheetName: string) => {
    setSelected(sheetName);
    onSheetSelect(sheetName);
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Select Sheet</DialogTitle>
      <DialogContent>
        <Box sx={{ width: "100%", maxWidth: 360, bgcolor: "background.paper" }}>
          <List>
            {sheetNames.map((sheetName) => (
              <ListItem key={sheetName} disablePadding>
                <ListItemButton
                  selected={selected === sheetName}
                  onClick={() => handleSelect(sheetName)}
                >
                  <ListItemText primary={sheetName} />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={() => handleSelect(selected!)}
          variant="contained"
          disabled={!selected}
        >
          Select
        </Button>
      </DialogActions>
    </Dialog>
  );
}
