"use client";

import { useState, useRef, useEffect } from "react";
import { TableCell } from "@mui/material";

interface ResizableTableCellProps {
  children: React.ReactNode;
  width: number;
  onResize: (width: number) => void;
}

export default function ResizableTableCell({
  children,
  width,
  onResize,
}: ResizableTableCellProps) {
  const [isResizing, setIsResizing] = useState(false);
  const [startX, setStartX] = useState(0);
  const [startWidth, setStartWidth] = useState(0);
  const cellRef = useRef<HTMLTableCellElement>(null);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;

      const diff = e.clientX - startX;
      const newWidth = Math.max(50, startWidth + diff); // Minimum width of 50px
      onResize(newWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isResizing, startX, startWidth, onResize]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    setStartX(e.clientX);
    setStartWidth(width);
  };

  return (
    <TableCell
      ref={cellRef}
      style={{
        width,
        position: "relative",
        padding: "8px",
      }}
    >
      {children}
      <div
        style={{
          position: "absolute",
          right: 0,
          top: 0,
          bottom: 0,
          width: "4px",
          cursor: "col-resize",
          backgroundColor: isResizing ? "#1976d2" : "transparent",
        }}
        onMouseDown={handleMouseDown}
      />
    </TableCell>
  );
}
