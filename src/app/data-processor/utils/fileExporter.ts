import <PERSON> from "papaparse";
import * as XLSX from "xlsx";
import { CSVData } from "./fileReader";

export const exportFile = (
  data: CSVData,
  fileName: string,
  format: "csv" | "xlsx",
) => {
  if (format === "xlsx") {
    // Export as Excel
    const worksheet = XLSX.utils.aoa_to_sheet([data.headers, ...data.rows]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
    XLSX.writeFile(workbook, `${fileName}.xlsx`);
  } else {
    // Export as CSV
    const csv = Papa.unparse({
      fields: data.headers,
      data: data.rows,
    });

    // Create blob and download
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `${fileName}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};
