import Papa, { ParseResult } from "papaparse";
import * as XLSX from "xlsx";
import { openSheetSelection } from "../components/SheetSelector";

export interface CSVData {
  headers: string[];
  rows: string[][];
}

export const readFile = async (file: File): Promise<CSVData> => {
  const fileExtension = file.name.split(".").pop()?.toLowerCase();

  if (fileExtension === "xlsx") {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = e.target?.result;
          if (!data) {
            throw new Error("Failed to read file");
          }
          const workbook = XLSX.read(data, { type: "binary" });
          const sheetNames = workbook.SheetNames;
          if (!sheetNames.length) {
            throw new Error("No sheets found in Excel file");
          }
          const sheetName =
            (await openSheetSelection(sheetNames)) ?? sheetNames[0];
          const worksheet = workbook.Sheets[sheetName ?? "Sheet1"];
          if (!worksheet) {
            throw new Error("Worksheet not found in Excel file");
          }

          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
          }) as string[][];

          if (jsonData.length > 0 && Array.isArray(jsonData[0])) {
            const headers = jsonData[0] as string[];
            const rows = jsonData.slice(1) as string[][];
            const newRows: string[][] = [];

            for (const row of rows) {
              if (!row) continue;
              const newRow: string[] = [];
              for (let j = 0; j < headers.length; j++) {
                const cell = row[j];
                if (cell === undefined || cell === null) {
                  newRow.push("");
                } else {
                  newRow.push(cell);
                }
              }
              newRows.push(newRow);
            }

            resolve({ headers, rows: newRows });
          } else {
            throw new Error("Invalid Excel file format");
          }
        } catch (error) {
          reject(new Error(error as string));
        }
      };
      reader.onerror = () => {
        reject(new Error("Failed to read file"));
      };
      reader.readAsBinaryString(file);
    });
  } else if (fileExtension === "csv") {
    return new Promise((resolve, reject) => {
      Papa.parse(file, {
        complete: (results: ParseResult<string[]>) => {
          if (results.data && results.data.length > 0) {
            const headers = results.data[0] as string[];
            const rows = results.data.slice(1) as string[][];
            resolve({ headers, rows });
          } else {
            reject(new Error("Invalid CSV file format"));
          }
        },
        error: (error: Error) => {
          reject(error);
        },
      });
    });
  } else {
    throw new Error(
      "Unsupported file format. Please upload a CSV or Excel file.",
    );
  }
};
