"use client";

import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import {
  Box,
  Button,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import EditCellModal from "./components/EditCellModal";
import EditRowModal from "./components/EditRowModal";
import ResizableTableCell from "./components/ResizableTableCell";
import { exportFile } from "./utils/fileExporter";
import { CSVData, readFile } from "./utils/fileReader";

export default function DataProcessor() {
  const [file, setFile] = useState<File | null>(null);
  const [csvData, setCSVData] = useState<CSVData | null>(null);
  const [error, setError] = useState<string>("");
  const [columnWidths, setColumnWidths] = useState<{ [key: string]: number }>(
    {},
  );
  const [editingRow, setEditingRow] = useState<{
    index: number;
    data: string[];
  } | null>(null);

  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [exportFileName, setExportFileName] = useState("");
  const [exportFormat, setExportFormat] = useState<"csv" | "xlsx">("csv");
  const [editingCell, setEditingCell] = useState<{
    rowIndex: number;
    cellIndex: number;
    value: string;
  } | null>(null);
  const [editingHeader, setEditingHeader] = useState<{
    index: number;
    value: string;
  } | null>(null);

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (event.target.files && event.target.files[0]) {
      const selectedFile = event.target.files[0];
      setFile(selectedFile);
      setError("");
      setCSVData(null);
      setColumnWidths({});

      try {
        const data = await readFile(selectedFile);
        setCSVData(data);

        // Initialize column widths
        const initialWidths: { [key: string]: number } = {};
        data.headers.forEach((header) => {
          initialWidths[header] = 100;
        });
        setColumnWidths(initialWidths);
      } catch (error) {
        setError(error instanceof Error ? error.message : String(error));
      }
    }
  };

  const handleColumnResize = (header: string, width: number) => {
    setColumnWidths((prev) => ({
      ...prev,
      [header]: width,
    }));
  };

  const handleDeleteRow = (rowIndex: number) => {
    if (!csvData) return;

    const newRows = [...csvData.rows];
    newRows.splice(rowIndex, 1);
    setCSVData({
      ...csvData,
      rows: newRows,
    });
  };

  const handleEditRow = (rowIndex: number) => {
    if (!csvData) return;
    const rowData = csvData.rows[rowIndex];
    if (!rowData) return;
    setEditingRow({
      index: rowIndex,
      data: Array.from(rowData),
    });
  };

  const handleSaveRow = (editedRow: string[]) => {
    if (!csvData || !editingRow) return;

    const newRows = [...csvData.rows];
    newRows[editingRow.index] = editedRow;
    setCSVData({
      ...csvData,
      rows: newRows,
    });
    setEditingRow(null);
  };

  const handleExportClick = () => {
    // Set default filename based on original file or current date
    const defaultName = file
      ? file.name.replace(/\.[^/.]+$/, "") + "_edited"
      : `export_${new Date().toISOString().split("T")[0]}`;
    setExportFileName(defaultName);
    setExportDialogOpen(true);
  };

  const handleExportCSV = () => {
    if (!csvData) return;
    exportFile(csvData, exportFileName, exportFormat);
    setExportDialogOpen(false);
  };

  const handleCellClick = (
    rowIndex: number,
    cellIndex: number,
    value: string,
  ) => {
    setEditingCell({
      rowIndex,
      cellIndex,
      value,
    });
  };

  const handleCellSave = (value: string) => {
    if (!editingCell || !csvData) return;

    const newRows = [...csvData.rows];
    const row = newRows[editingCell.rowIndex];
    if (row && editingCell.cellIndex < row.length) {
      row[editingCell.cellIndex] = value;
      setCSVData({
        ...csvData,
        rows: newRows,
      });
    }
    setEditingCell(null);
  };

  const handleAddRow = () => {
    if (!csvData) return;

    const newRow = Array(csvData.headers.length).fill("");
    setCSVData({
      ...csvData,
      rows: [...csvData.rows, newRow],
    });
  };

  const handleAddColumn = () => {
    if (!csvData) return;

    const newColumnName = `Column ${csvData.headers.length + 1}`;
    const newHeaders = [...csvData.headers, newColumnName];
    const newRows = csvData.rows.map((row) => [...row, ""]);

    setCSVData({
      headers: newHeaders,
      rows: newRows,
    });

    // Initialize width for new column
    setColumnWidths((prev) => ({
      ...prev,
      [newColumnName]: 100,
    }));
  };

  const handleHeaderClick = (index: number, value: string) => {
    setEditingHeader({
      index,
      value,
    });
  };

  const handleHeaderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!editingHeader) return;
    setEditingHeader({
      ...editingHeader,
      value: event.target.value,
    });
  };

  const handleHeaderSave = () => {
    if (!editingHeader || !csvData) return;

    const newHeaders = [...csvData.headers];
    newHeaders[editingHeader.index] = editingHeader.value;

    // Update column widths with new header name
    const oldHeader = csvData.headers[editingHeader.index];
    if (oldHeader) {
      setColumnWidths((prev) => {
        const newWidths = { ...prev };
        if (oldHeader in newWidths) {
          const oldWidth = newWidths[oldHeader];
          newWidths[editingHeader.value] = oldWidth ?? 100;
          delete newWidths[oldHeader];
        }
        return newWidths;
      });
    }

    setCSVData({
      ...csvData,
      headers: newHeaders,
    });
    setEditingHeader(null);
  };

  const handleHeaderKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (event.key === "Enter") {
      handleHeaderSave();
    } else if (event.key === "Escape") {
      setEditingHeader(null);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Data Processor
        </Typography>
        <input
          type="file"
          accept=".csv,.xlsx"
          onChange={handleFileChange}
          style={{ display: "none" }}
          id="csv-file-input"
        />
        <label htmlFor="csv-file-input">
          <Button
            variant="contained"
            component="span"
            startIcon={<AddIcon />}
            sx={{ mr: 2 }}
          >
            Import File
          </Button>
        </label>
        {csvData && (
          <>
            <Button
              variant="outlined"
              onClick={handleAddRow}
              startIcon={<AddIcon />}
              sx={{ mr: 2 }}
            >
              Add Row
            </Button>
            <Button
              variant="outlined"
              onClick={handleExportClick}
              startIcon={<FileDownloadIcon />}
            >
              Export File
            </Button>
          </>
        )}
        {error && (
          <Typography color="error" sx={{ mt: 2 }}>
            {error}
          </Typography>
        )}
      </Box>

      {csvData && (
        <TableContainer
          component={Paper}
          sx={{
            maxWidth: "100%",
            overflowX: "auto",
            "& .MuiTable-root": {
              minWidth: 650,
            },
          }}
        >
          <Table>
            <TableHead>
              <TableRow>
                {csvData.headers.map((header, index) => (
                  <ResizableTableCell
                    key={index}
                    width={columnWidths[header] ?? 100}
                    onResize={(width) => handleColumnResize(header, width)}
                  >
                    {editingHeader?.index === index ? (
                      <TextField
                        value={editingHeader.value}
                        onChange={handleHeaderChange}
                        onKeyDown={handleHeaderKeyDown}
                        onBlur={handleHeaderSave}
                        autoFocus
                        fullWidth
                        size="small"
                      />
                    ) : (
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                        }}
                      >
                        <Typography
                          onClick={() => handleHeaderClick(index, header)}
                          sx={{ cursor: "pointer" }}
                        >
                          {header}
                        </Typography>
                      </Box>
                    )}
                  </ResizableTableCell>
                ))}
                <TableCell
                  width={100}
                  sx={{
                    position: "sticky",
                    right: 0,
                    backgroundColor: "background.paper",
                    borderLeft: "1px solid",
                    borderColor: "divider",
                    "&::after": {
                      content: '""',
                      position: "absolute",
                      left: 0,
                      top: 0,
                      bottom: 0,
                      width: "1px",
                      boxShadow: "2px 0 4px -2px rgba(0,0,0,0.2)",
                    },
                  }}
                >
                  Actions
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {csvData.rows.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {row.map((cell, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <Typography
                        component="div"
                        onClick={() =>
                          handleCellClick(rowIndex, cellIndex, cell)
                        }
                        sx={{
                          cursor: "pointer",
                          whiteSpace: "pre-wrap",
                          maxHeight: "10em",
                          overflowY: "auto",
                          display: "-webkit-box",
                          WebkitLineClamp: 5,
                          WebkitBoxOrient: "vertical",
                          minHeight: "2em",
                        }}
                      >
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {cell?.toString() || "_"}
                        </ReactMarkdown>
                      </Typography>
                    </TableCell>
                  ))}
                  <TableCell
                    sx={{
                      position: "sticky",
                      right: 0,
                      backgroundColor: "background.paper",
                      borderLeft: "1px solid",
                      borderColor: "divider",
                      "&::after": {
                        content: '""',
                        position: "absolute",
                        left: 0,
                        top: 0,
                        bottom: 0,
                        width: "1px",
                        boxShadow: "2px 0 4px -2px rgba(0,0,0,0.2)",
                      },
                    }}
                  >
                    <Tooltip title="Delete Row">
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteRow(rowIndex)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
      >
        <DialogTitle>Export File</DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Format
            </Typography>
            <Box sx={{ display: "flex", gap: 2 }}>
              <Button
                variant={exportFormat === "csv" ? "contained" : "outlined"}
                onClick={() => setExportFormat("csv")}
                size="small"
              >
                CSV
              </Button>
              <Button
                variant={exportFormat === "xlsx" ? "contained" : "outlined"}
                onClick={() => setExportFormat("xlsx")}
                size="small"
              >
                Excel
              </Button>
            </Box>
          </Box>
          <TextField
            autoFocus
            margin="dense"
            label="File Name"
            type="text"
            fullWidth
            value={exportFileName}
            onChange={(e) => setExportFileName(e.target.value)}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  {exportFormat === "xlsx" ? ".xlsx" : ".csv"}
                </InputAdornment>
              ),
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExportDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleExportCSV} variant="contained">
            Export
          </Button>
        </DialogActions>
      </Dialog>

      <EditRowModal
        open={!!editingRow}
        onClose={() => setEditingRow(null)}
        onSave={handleSaveRow}
        headers={csvData?.headers || []}
        row={editingRow?.data || []}
        rowIndex={editingRow?.index || 0}
      />

      <EditCellModal
        open={!!editingCell}
        onClose={() => setEditingCell(null)}
        onSave={handleCellSave}
        header={
          editingCell ? csvData?.headers[editingCell.cellIndex] || "" : ""
        }
        value={editingCell?.value || ""}
      />
    </Container>
  );
}
