import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { ResponseFeedbackType } from "@prisma/client";
import { FeedbackManager } from "../managers/feedbackManager";
import { TRPCError } from "@trpc/server";

export const feedbackRouter = createTRPCRouter({
  createorUpdate: protectedProcedure
    .input(
      z.object({
        questionId: z.string(),
        documentId: z.string(),
        type: z.nativeEnum(ResponseFeedbackType),
        feedback: z.string().optional(),
        reason: z.string().optional().default(""),
        responseText: z.string().optional().default(""),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { questionId, documentId, feedback, type, reason, responseText } =
        input;
      if (!ctx.org.id) {
        throw new Error("Org id is required");
      }
      if (!ctx.auth.id) {
        throw new Error("User id is required");
      }

      return FeedbackManager.createorUpdateFeedback({
        db: ctx.db,
        questionId,
        documentId,
        feedback,
        type,
        reason,
        responseText,
        orgId: ctx.org.id,
        userId: ctx.auth.id,
      });
    }),

  getAggregatedFeedback: protectedProcedure
    .input(
      z.object({
        questionId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { questionId } = input;
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }

      return FeedbackManager.getAggregatedFeedback({
        db: ctx.db,
        questionId,
        orgId: ctx.org.id,
        userId: ctx.auth.id,
      });
    }),

  getAggregatedFeedbackBatch: protectedProcedure
    .input(
      z.object({
        questionIds: z.array(z.string()),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { questionIds } = input;
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }

      return FeedbackManager.getAggregatedFeedbackBatch({
        db: ctx.db,
        questionIds,
        orgId: ctx.org.id,
        userId: ctx.auth.id,
      });
    }),

  getUserFeedback: protectedProcedure
    .input(
      z.object({
        responseId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { responseId } = input;
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }

      return FeedbackManager.getUserFeedback({
        db: ctx.db,
        responseId,
        orgId: ctx.org.id,
        userId: ctx.auth.id,
      });
    }),

  getDocumentFeedbackStats: protectedProcedure
    .input(
      z.object({
        documentId: z.string(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { documentId } = input;
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }

      return FeedbackManager.getDocumentFeedbackStats({
        db: ctx.db,
        documentId,
        orgId: ctx.org.id,
      });
    }),

  resetFeedbackPerDocument: protectedProcedure
    .input(
      z.object({
        documentId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { documentId } = input;
      if (!ctx.org.id) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Org id is required",
        });
      }

      return FeedbackManager.resetFeedbackPerDocument({
        db: ctx.db,
        documentId,
        orgId: ctx.org.id,
      });
    }),
});
