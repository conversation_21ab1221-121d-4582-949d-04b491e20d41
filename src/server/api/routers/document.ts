import { z } from "zod";

import {
  InvokeCommand,
  LambdaClient,
  type InvokeCommandInput,
} from "@aws-sdk/client-lambda";
import {
  DeleteObjectsCommand,
  PutObjectCommand,
  S3Client,
} from "@aws-sdk/client-s3";

import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import {
  AnswerGenerationType,
  DDQStatus,
  DocumentSource,
  DocumentStatus,
  DocumentType,
  ResponseStatus,
  type Prisma,
} from "@prisma/client";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";

// AI
import {
  type JsonObject,
  type JsonValue,
} from "@prisma/client/runtime/library";
import { EventEmitter, on } from "events";
import { type LambdaEvent } from "infra/lambda/vectorizer";
import pgvector from "pgvector";
import postgres from "postgres";
import { env } from "~/env";
import { type TableAnalysisResponse } from "~/lib/analyzer";
import {
  documentClassifier,
  documentFundClassifier,
  questionClassifier,
} from "~/lib/classifier";
import {
  generateAnswersByQuestionIds,
  selectBestMatchingResponse,
} from "~/lib/ddq";
import { getDocumentContents } from "~/lib/fileUtils";
import { getSharepointFileIdFromUrl } from "~/lib/integrations/azure/utils";
import { type GeneratedResponse } from "~/lib/rag-response";
import { VoyageAIEmbedding } from "~/lib/rag-response/utils/voyageai-embedding-openai-compat";
import { deleteCachedUnstructuredContent } from "~/lib/redis";
import { type Citation } from "~/lib/types-from-pydantic.zod";
import {
  generateChunksLambda,
  markDocumentStatusGeneratingAnswers,
  markDocumentStatusReady,
} from "~/lib/vectorizer";
import { getDirectConnectionUrl, type PrismaClientType } from "~/server/db";
import { convertFileToPDF, getPDFCitedPage } from "~/server/utils/pdfUtils";
import { checkOrCreatePublication } from "~/server/utils/postgres";
import { responseLibrarySemanticSearch } from "~/server/utils/search";
import { DocumentTypeWithoutFolder } from "~/types/ddq";
import { getDocumentTypeByExtension } from "~/utils/document";

const S3awsParams = {
  region: env.AWS_S3_REGION,
  credentials: {
    accessKeyId: env.AWS_S3_ACCESS_KEY,
    secretAccessKey: env.AWS_S3_SECRET_ACCESS_KEY,
  },
};

const lambdaParams = {
  region: env.AWS_LAMBDA_REGION,
  credentials: {
    accessKeyId: env.AWS_LAMBDA_ACCESS_KEY,
    secretAccessKey: env.AWS_LAMBDA_SECRET_ACCESS_KEY,
  },
};

const s3Client = new S3Client(S3awsParams);
const lambdaClient = new LambdaClient(lambdaParams);

export type DocumentStatusType = { filename: string; status: string };

export type AnalysisResponse = {
  sheet: string;
  analysis: TableAnalysisResponse[];
};

export const DeleteInput = z.object({
  list: z.array(z.object({ id: z.string(), name: z.string() })),
});

export type DeleteInputType = z.infer<typeof DeleteInput>;

const GetEditsForRangeInclude = { createdBy: true };

export type GetEditsForRangeType = Prisma.DocumentEditGetPayload<{
  include: typeof GetEditsForRangeInclude;
}>;

const GetDocumentsInclude = {
  createdBy: true,
  tags: {
    select: {
      id: true,
      name: true,
      color: true,
    },
  },
  funds: {
    select: {
      id: true,
      name: true,
      description: true,
    },
  },
  jsonContents: false,
  htmlContents: false,
  DDQMetadata: {
    select: {
      metadata: true,
    },
  },
};

export type GetDocumentsType = Prisma.DocumentGetPayload<{
  include: typeof GetDocumentsInclude;
}>;

const GetDDQsInclude = {
  DDQSummary: true,
  DDQMetadata: true,
  createdBy: true,
  tags: true,
  funds: true,
  jsonContents: false,
  htmlContents: false,
};

export type GetDDQsType = Prisma.DocumentGetPayload<{
  include: typeof GetDDQsInclude;
}>;

export const GetDocumentWithEmptyAnswersInclude = {
  response: {
    include: {
      responseContents: true,
      questions: {
        include: {
          questionContents: true,
        },
      },
    },
  },
};

export type GetDocumentWithEmptyAnswersType = Prisma.DocumentGetPayload<{
  include: {
    responses: {
      where: {
        documentId: string;
      };
      include: typeof GetDocumentWithEmptyAnswersInclude;
    };
  };
}>;

export const cleanupDocuments = async (
  db: PrismaClientType,
  orgId: string,
  deleteInput: DeleteInputType,
) => {
  try {
    await deleteCachedUnstructuredContent(
      deleteInput.list.map((file) => file.id),
    );

    await db.question.deleteMany({
      where: {
        response: {
          documents: {
            some: {
              documentId: { in: deleteInput.list.map((file) => file.id) },
            },
          },
        },
        orgId: orgId,
      },
    });

    await db.response.deleteMany({
      where: {
        documents: {
          some: { documentId: { in: deleteInput.list.map((file) => file.id) } },
        },
        orgId: orgId,
      },
    });

    await db.documentResponses.deleteMany({
      where: { documentId: { in: deleteInput.list.map((file) => file.id) } },
    });

    await db.documentEdit.deleteMany({
      where: {
        documentId: { in: deleteInput.list.map((file) => file.id) },
        orgId: orgId,
      },
    });

    await db.documentChunk.deleteMany({
      where: {
        documentId: { in: deleteInput.list.map((file) => file.id) },
        orgId: orgId,
      },
    });

    await db.documentSection.deleteMany({
      where: {
        documentId: { in: deleteInput.list.map((file) => file.id) },
        orgId: orgId,
      },
    });

    return await db.document.deleteMany({
      where: {
        id: { in: deleteInput.list.map((file) => file.id) },
        orgId: orgId,
      },
    });
  } catch (error) {
    console.error("Error deleting documents", error);
  }
};

export type AnswersAndResponsesType = Array<{
  answer: string;
  reason: string;
  citations: Citation[];
  response:
    | {
        response: {
          id: string;
          questions: Array<{
            id: string;
            questionContents: Array<{
              id: string;
              content: JsonValue;
            }>;
          }>;
          responseContents: Array<{
            id: string;
            content: JsonValue;
          }>;
        };
      }
    | undefined;
}>;

export type ExistingAnswerType = {
  questionId: string | undefined;
  existingAnswers: Array<{
    type: "question" | "response";
    id: string;
    content: {
      text?: string;
      [key: string]: any;
    } | null;
    vector_distance: number;
    bm25_score: number;
    combined_score: number;
    question_id: string | null;
    response_id: string | null;
  }>;
  reasoning: string | null;
} | null;

const addExistingAnswersToResponseContent = async (
  db: PrismaClientType,
  documentId: string,
  existingAnswers: ExistingAnswerType[],
) => {
  for (const answer of existingAnswers) {
    console.log("Updating answer", JSON.stringify(answer, null, 2));

    try {
      // Find the response for answer.questionId
      const response = await db.response.findFirst({
        where: {
          questions: {
            some: { id: answer?.questionId ?? "" },
          },
        },
      });

      if (!response) {
        console.error(`No response found for question ${answer?.questionId}`);
        continue;
      }

      // First find the ResponseContent for this response
      const responseContent = await db.responseContent.findFirst({
        where: {
          responseId: response?.id ?? "",
          // Make sure we're updating the current document
          response: {
            documents: {
              some: { documentId: documentId },
            },
          },
        },
      });

      if (!responseContent) {
        console.error(`No ResponseContent found for response ${response?.id}`);
        continue;
      }

      const documentNameForResponseContent = await db.document.findFirst({
        where: {
          responses: {
            some: {
              response: {
                responseContents: {
                  some: {
                    id: responseContent.id,
                  },
                },
              },
            },
          },
        },
        select: {
          id: true,
          name: true,
        },
      });

      console.log(
        "documentNameForResponseContent",
        documentNameForResponseContent,
      );

      // Update the ResponseContent with the existing answers from the response library
      const updatedResponseContent = await db.responseContent.update({
        where: {
          id: responseContent.id,
          response: {
            documents: {
              some: {
                documentId: documentId,
              },
            },
          },
        },
        data: {
          content: {
            text: "",
            existingResponses: answer?.existingAnswers.map((a) => ({
              responseId: a.response_id ?? "",
              questionId: a.question_id ?? "",
              vector_distance: a.vector_distance,
              bm25_score: a.bm25_score,
            })),
            reason: answer?.reasoning,
          },
          answerGenerationType: AnswerGenerationType.EXTRACTED,
        },
      });

      console.log(
        "updatedResponseContent",
        JSON.stringify(updatedResponseContent, null, 2),
      );
    } catch (error) {
      console.error(
        `Failed to add existing answers to response (${answer?.questionId}) content: ${error}`,
      );
    }
  }
};

export const overwriteExistingResponseContent = async (
  db: PrismaClientType,
  answersAndResponses: AnswersAndResponsesType,
  userId: string,
  orgId: string,
  secret?: Record<string, string>,
) => {
  return await Promise.all(
    answersAndResponses.map(async (answer) => {
      if (answer?.answer && answer.answer !== "" && answer.response) {
        const response = answer.response;

        if (!response) {
          throw new Error("Response not found");
        }

        const responseContentId = response.response.responseContents[0]?.id;

        if (!responseContentId) {
          console.log("Response content ID not found", response);
          return;
        }

        console.log("Updating response content", responseContentId);

        const updatedResponseContent = await db.responseContent.update({
          where: { id: responseContentId },
          data: {
            content: {
              text: answer.answer,
              reason: answer.reason,
              citations: answer.citations,
            },
            createdById: userId,
            orgId: orgId,
            answerGenerationType: AnswerGenerationType.GENERATED,
          },
        });

        // console.log("updatedResponseContent", updatedResponseContent);

        const embeddings = new VoyageAIEmbedding(secret);

        try {
          const vector = pgvector.toSql(
            await embeddings.embedQuery(answer.answer),
          );
          await db.$executeRaw`UPDATE "ResponseContent"
                                     SET vector = ${vector}::vector
                                     WHERE id = ${responseContentId}`;

          const responseId = response.response.id;
          await db.response.update({
            where: {
              id: responseId,
            },
            data: {
              status: ResponseStatus.DRAFT,
            },
          });
        } catch (error) {
          console.error(
            "generateAnswers: Unable to vectorize response content:",
            answer.answer,
            "type:",
            typeof answer.answer,
            error,
          );

          throw error;
        }

        return updatedResponseContent;
      }
    }),
  );
};

export const documentRouter = createTRPCRouter({
  getDocumentByFilename: protectedProcedure
    .input(z.object({ filename: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.document.findFirst({
        where: { name: { contains: input.filename }, orgId: ctx.org.id ?? "" },
      });
    }),
  getDocumentByUrl: protectedProcedure
    .input(z.object({ url: z.string() }))
    .query(async ({ ctx, input }) => {
      if (input.url.includes("sharepoint.com")) {
        const org = await ctx.db.org.findFirstOrThrow({
          where: { id: ctx.org.id ?? "" },
          include: { azureAccessToken: true, AzureDrive: true },
        });

        if (!org.azureAccessToken) {
          throw new Error("AccessToken not found");
        }

        const id = await getSharepointFileIdFromUrl(
          org.azureAccessToken,
          org.AzureDrive[0]?.azureId ?? "",
          input.url,
          ctx.db,
        );

        return await ctx.db.document.findFirstOrThrow({
          where: { azureItemId: id },
          include: GetDocumentsInclude,
        });
      } else {
        return await ctx.db.document.findFirst({
          where: { url: input.url },
          include: GetDocumentsInclude,
        });
      }
    }),
  getAll: protectedProcedure
    .input(
      z.object({
        status: z.nativeEnum(DocumentStatus).array(),
        ddqStatus: z.nativeEnum(DDQStatus).array().optional(),
        nameFilter: z.string().optional(),
        folder: z.string().nullish(),
        documentSource: z.nativeEnum(DocumentSource).nullish(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        tagIds: z.string().array().optional(),
        fundIds: z.string().array().optional(),
        // Paging
        // limit: z.number().min(1).max(100).nullish(),
        // cursor: z.string().nullish(),
        // skip: z.number().optional(),
        // direction: z.enum(["forward", "backward"]),
      }),
    )
    .query(async ({ ctx, input }) => {
      // const limit = input.limit ?? 100;
      // const { cursor } = input;

      const documents = await ctx.db.document.findMany({
        // take: limit + 1, // get an extra item at the end which we'll use as next cursor
        // skip: input.skip,
        where: {
          orgId: ctx.org.id ?? "",
          status: { in: input.status },
          ddqStatus: input.ddqStatus ? { in: input.ddqStatus } : undefined,
          AND: [
            {
              name: input.nameFilter
                ? { contains: input.nameFilter, mode: "insensitive" }
                : undefined,
            },
            {
              name: input.folder
                ? { contains: input.folder, mode: "insensitive" }
                : undefined,
            },
          ],
          source: input.documentSource
            ? { equals: input.documentSource }
            : undefined,
          createdAt: { gte: input.startDate, lte: input.endDate },
          tags:
            input.tagIds && input.tagIds?.length > 0
              ? { some: { id: { in: input.tagIds } } }
              : undefined,
          funds:
            input.fundIds && input.fundIds?.length > 0
              ? { some: { id: { in: input.fundIds } } }
              : undefined,
          // {
          //   name: input.folder ? { contains: `${input.folder}/` } : undefined,
          // },
        },
        // @ts-ignore
        // cursor: cursor ? { id: cursor } : undefined,

        orderBy: { name: "asc" },

        include: GetDocumentsInclude,
      });

      // let nextCursor: typeof cursor | undefined = undefined;
      // if (documents.length > limit) {
      //   const nextItem = documents.pop();
      //   // @ts-ignore
      //   nextCursor = nextItem?.id;
      // }

      const currentDepth = input.folder?.split("/").length ?? 0;

      const tree = documents
        .map((doc) => {
          const path = doc.name.split("/").slice(currentDepth, -1);

          return {
            path,
            ...doc,
            name: doc.name.replace(input.folder ?? "", ""),
          };
        })
        .sort((a, b) => {
          if (a.path.length === b.path.length) {
            return b.name.localeCompare(a.name);
          }
          return b.path.length - a.path.length;
        });

      // const count = await ctx.db.document.count({
      //   where: {
      //     orgId: ctx.org.id ?? "",
      //   },
      // });

      // return { tree, nextCursor, count };
      return tree;
    }),
  getFilteredDocumentsCount: protectedProcedure
    .input(
      z.object({
        status: z.nativeEnum(DocumentStatus).array(),
        tagIds: z.string().array().optional(),
        fundIds: z.string().array().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      return await ctx.db.document.count({
        where: {
          orgId: ctx.org.id ?? "",
          status: { in: input.status },
          tags:
            input.tagIds && input.tagIds?.length > 0
              ? { some: { id: { in: input.tagIds } } }
              : undefined,
          funds:
            input.fundIds && input.fundIds?.length > 0
              ? { some: { id: { in: input.fundIds } } }
              : undefined,
        },
      });
    }),
  getAllDDQs: protectedProcedure
    .input(
      z.object({
        nameFilter: z.string().optional(),
        status: z
          .nativeEnum(DDQStatus)
          .array()
          .default([DDQStatus.PENDING, DDQStatus.APPROVED, DDQStatus.REVIEW]),
        fileType: z.nativeEnum(DocumentTypeWithoutFolder).array().optional(),
        startDate: z.date().optional(),
        endDate: z.date().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      console.log("input", input);

      if (input.startDate) {
        input.startDate.setHours(0, 0, 0, 0);
      }
      if (input.endDate) {
        input.endDate.setHours(23, 59, 59, 999);
      }

      // Since we don't have a way to differentiate between DDQs and other documents,
      // we just filter by name for now
      const ddqs = await ctx.db.document.findMany({
        where: {
          orgId: ctx.org.id ?? "",
          name: { mode: "insensitive", contains: input.nameFilter },
          ddqStatus: { in: input.status },
          status: { in: [DocumentStatus.READY] },
          updatedAt: { gte: input.startDate, lte: input.endDate },
          type:
            input.fileType && input.fileType.length > 0
              ? {
                  in: input.fileType.map(
                    (t) => t.toUpperCase() as DocumentType,
                  ),
                }
              : undefined,
        },
        orderBy: { name: "asc" },
        include: GetDDQsInclude,
      });

      return ddqs;
    }),
  updateDDQ: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        dueDate: z.date().optional(),
        ddqStatus: z.nativeEnum(DDQStatus).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        return await ctx.db.document.update({
          where: { id: input.id, orgId: ctx.org.id ?? "" },
          data: { dueDate: input.dueDate, ddqStatus: input.ddqStatus },
        });
      } catch (error) {
        console.error(error);
        throw error;
      }
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        funds: z.string().array().optional(),
        tags: z.string().array().optional(),
        type: z.nativeEnum(DocumentType).optional(),
        size: z.number().optional(),
        status: z.nativeEnum(DocumentStatus).optional(),
        source: z.nativeEnum(DocumentSource).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        if (input.funds) {
          await ctx.db.document.update({
            where: { id: input.id, orgId: ctx.org.id ?? "" },
            data: {
              funds: {
                set: input.funds.map((id) => ({ id })),
              },
            },
          });
        }

        if (input.tags) {
          // Get current connections, only delete if they are not in the new tags
          const currentConnections = await ctx.db.tagEntityConnection.findMany({
            where: {
              documentId: input.id,
              orgId: ctx.org.id ?? "",
            },
          });

          const currentConnectionsToDelete = currentConnections.filter(
            (connection) => !input.tags?.includes(connection.tagId),
          );

          await ctx.db.tagEntityConnection.deleteMany({
            where: {
              id: { in: currentConnectionsToDelete.map((c) => c.id) },

              orgId: ctx.org.id ?? "",
            },
          });

          await ctx.db.document.update({
            where: { id: input.id, orgId: ctx.org.id ?? "" },
            data: {
              tags: {
                set: input.tags.map((id) => ({ id })),
              },
            },
          });

          try {
            await ctx.db.tagEntityConnection.createMany({
              data:
                input.tags?.map((t) => ({
                  tagId: t,
                  documentId: input.id,
                  orgId: ctx.org.id ?? "",
                  connectionReason: `Tag added by ${ctx.auth.name}`,
                })) ?? [],
            });
          } catch (error) {
            console.error("Error creating tag entity connections", error);
            throw error;
          }
        }

        return await ctx.db.document.update({
          where: { id: input.id, orgId: ctx.org.id ?? "" },
          data: {
            // funds: {
            //   connect: input.funds?.map((id) => ({ id })),
            // },
            // tags: {
            //   connect: input.tags?.map((id) => ({ id })),
            // },
            type: input.type,
            size: input.size,
            status: input.status,
            source: input.source,
          },
        });
      } catch (error) {
        console.error(error);
        throw error;
      }
    }),
  onChanges: protectedProcedure.subscription(async function* ({ ctx }) {
    console.log("Initializing psql listener");
    const ee = new EventEmitter();

    await checkOrCreatePublication();

    const connection = postgres(getDirectConnectionUrl(), {
      publications: "documents",
    });

    try {
      console.log("Listening to postgres");

      const { unsubscribe } = await connection.subscribe(
        "*",
        (row, info) => {
          // Callback function for each row change
          // console.log("row", row);
          // console.log("info", info);

          ee.emit("document_notification", { row, info });
        },

        () => {
          console.log("Subscribed to postgres");
          // Callback on initial connect and potential reconnects
        },
      );

      setTimeout(() => {
        console.log("Emitting timeout event after 300 seconds");
        ee.emit("document_notification", { timeout: true });
      }, 300000);

      // Use Node's async iterator to yield events as they come in.
      // The `on(ee, "document_notification")` returns an async iterator that yields an array of arguments,
      // so in this case we destructure it to get the payload directly.
      for await (const [payload] of on(ee, "document_notification")) {
        if (payload.timeout !== undefined) {
          console.log("Unsubscribing from postgres after 120 seconds");
          unsubscribe();
          break;
        }

        yield payload;
      }

      console.log("Closing psql listener");
      await connection.end();
    } catch (error) {
      console.error("Error in psql listener", error);
      await connection.end();
      throw error;
    }
  }),
  getChunks: protectedProcedure
    .input(z.object({ chunkIds: z.string().array() }))
    .query(async ({ ctx, input }) => {
      const chunks = await ctx.db.documentChunk.findMany({
        where: { id: { in: input.chunkIds } },
        include: { document: true },
      });

      return chunks;
    }),

  generatePresignedUrls: protectedProcedure
    .input(z.object({ name: z.string(), contentType: z.string() }).array())
    .mutation(async ({ ctx, input }) => {
      try {
        return Promise.all(
          input.map(async (i) => {
            const command = new PutObjectCommand({
              Bucket: process.env.AWS_S3_BUCKET_NAME!,
              Key: i.name.replace(/^\.\//, ""),
              ContentType: i.contentType,
            });

            console.log("i.name", i.name);
            console.log("i.contentType", i.contentType);

            const fileLink = `https://${process.env.AWS_S3_BUCKET_NAME!}.s3.${process.env.AWS_S3_REGION!}.amazonaws.com/${i.name}`;
            const signedUrl = await getSignedUrl(s3Client, command, {
              expiresIn: 5 * 60, // 5 minutes - default is 15 mins
            });

            return { inputFile: i, fileLink, signedUrl };
          }),
        );
      } catch (error) {
        console.error(error);
        throw error;
      }
    }),

  open: protectedProcedure
    .input(z.object({ name: z.string(), url: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const type = getDocumentTypeByExtension(input.url.split(".")[1] ?? "");

        const document = await ctx.db.document.findFirst({
          where: { name: { contains: input.name } },
        });

        return await ctx.db.document.upsert({
          where: { id: document?.id },
          update: { status: DocumentStatus.EXTERNAL },
          create: {
            url: input.url,
            status: DocumentStatus.EXTERNAL,
            orgId: ctx.org.id ?? "",
            createdById: ctx.auth.id,
            name: input.name,
            type,
            size: 0,
          },
        });
      } catch (error) {
        console.error(error);
        throw error;
      }
    }),
  edit: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        edit: z.string().transform((val) => JSON.parse(val)),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const document = await ctx.db.documentEdit.create({
        data: {
          documentId: input.id,
          content: input.edit,
          orgId: ctx.org.id ?? "",
          createdById: ctx.auth.id,
        },
      });

      return document;
    }),
  getEditsForRange: protectedProcedure
    .input(z.object({ documentId: z.string(), range: z.string() }))
    .query(async ({ ctx, input }) => {
      const edits = await ctx.db.documentEdit.findMany({
        where: {
          documentId: input.documentId,
          content: { path: ["range"], equals: input.range },
        },
        include: GetEditsForRangeInclude,
        orderBy: { createdAt: "desc" },
      });

      return edits;
    }),
  getPluginResponseForRange: protectedProcedure
    .input(z.object({ documentId: z.string(), range: z.string() }))
    .query(async ({ ctx, input }) => {
      const originLocation = { address: input.range };

      const pluginResponse = await ctx.db.pluginResponse.findFirst({
        where: {
          documentId: input.documentId,
          originLocation: { equals: originLocation },
        },
        include: { response: { include: { responseContents: true } } },
      });

      return pluginResponse;
    }),
  generateInputQuestionForRange: protectedProcedure
    .input(z.object({ documentId: z.string(), range: z.string() }))
    .query(async ({ ctx, input }) => {
      return "Sample generated question for " + input.range;
    }),
  getAllModifiedCells: protectedProcedure
    .input(z.object({ documentId: z.string() }))
    .query(async ({ ctx, input }) => {
      const edits = await ctx.db.documentEdit.findMany({
        where: { documentId: input.documentId },
      });

      const modifiedCells = edits
        .map((edit) => {
          const content = edit.content as JsonObject;
          return content.range as string;
        })
        .filter((cell, idx, self) => self.indexOf(cell) === idx);

      return modifiedCells;
    }),
  getDocumentAnalysis: protectedProcedure
    .input(z.object({ documentId: z.string() }))
    .query(async ({ ctx, input }) => {
      const document = await ctx.db.document.findUnique({
        where: { id: input.documentId },
      });

      return document?.jsonContents as AnalysisResponse[];
    }),
  create: protectedProcedure
    .input(
      z.object({
        name: z.string(),
        url: z.string(),
        type: z.nativeEnum(DocumentType),
        size: z.number(),
        createdById: z.string(),
        source: z.nativeEnum(DocumentSource).nullish(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      console.log("document.create: orgId", ctx.org.id);

      const document = await ctx.db.document.create({
        data: {
          name: input.name.replace(/^\.\//, ""),
          url: input.url,
          type: input.type,
          size: input.size,
          plainTextContents: "",
          orgId: ctx.org.id ?? "",
          createdById: input.createdById,
          ddqStatus: /DDQ/i.exec(input.name) ? DDQStatus.PENDING : undefined,
          source: input.source ?? DocumentSource.LOCAL,
        },
      });

      // Run vectorizer locally in dev environment
      if (process.env.NODE_ENV === "development") {
        console.log("Running vectorizer locally");

        const vectorizedDocument = await generateChunksLambda({
          db: ctx.db,
          orgId: ctx.org.id ?? "",
          bucket: process.env.AWS_S3_BUCKET_NAME!,
          documentId: document.id,
        });

        console.log("Post-processing document", document.name);
        await Promise.all([
          questionClassifier({
            db: ctx.db,
            orgId: ctx.org.id ?? "",
            documentId: document.id,
          }),
          documentClassifier({
            db: ctx.db,
            orgId: ctx.org.id ?? "",
            documentId: document.id,
          }),
          documentFundClassifier({
            db: ctx.db,
            orgId: ctx.org.id ?? "",
            documentId: document.id,
          }),
        ]);
        return vectorizedDocument;
      } else {
        // Run vectorizer in Lambda in production environment
        console.log("Invoking Lambda");

        try {
          const invokeParams: InvokeCommandInput = {
            FunctionName: `vectorizer-${env.LAMBDA_ENV}`,
            InvocationType: "Event",
            Payload: JSON.stringify({
              documentId: document.id,
              orgId: ctx.org.id ?? "",
              bucket: process.env.AWS_S3_BUCKET_NAME!,
              secretId: env.AWS_LAMBDA_SECRET_ID,
              stage: "GENERATE_CHUNKS",
            } as LambdaEvent),
          };

          const command = new InvokeCommand(invokeParams);
          const response = await lambdaClient.send(command);
          console.log("Lambda invoked", response);
        } catch (error) {
          console.error(error);
          throw error;
        }
      }

      return input.name;
    }),

  vectorize: protectedProcedure
    .input(
      z
        .object({
          id: z.string(),
          name: z.string(),
        })
        .array(),
    )
    .mutation(async ({ ctx, input }) => {
      console.time(
        `Processing documents: ${input.map((d) => d.name).join(", ")}`,
      );

      const BATCH_SIZE = process.env.NODE_ENV === "development" ? 5 : 20;

      // Process documents in batches
      for (let i = 0; i < input.length; i += BATCH_SIZE) {
        const batch = input.slice(i, i + BATCH_SIZE);
        console.log(
          `Processing batch ${i / BATCH_SIZE + 1} of ${Math.ceil(input.length / BATCH_SIZE)}`,
        );

        await Promise.all(
          batch.map(async (doc) => {
            await ctx.db.documentChunk.deleteMany({
              where: { documentId: doc.id },
            });

            // Run vectorizer locally in dev environment
            if (process.env.NODE_ENV === "development") {
              console.log(`Processing document ${doc.name}`);
              console.time(`Processing document ${doc.name}`);

              // 1. Create document embeddings and store chunks in the database
              await generateChunksLambda({
                db: ctx.db,
                orgId: ctx.org.id ?? "",
                bucket: env.AWS_S3_BUCKET_NAME,
                documentId: doc.id,
              });

              console.timeEnd(`Processing document ${doc.name}`);
            } else {
              // Run vectorizer in Lambda on vercel
              console.log("Invoking Lambda");

              try {
                const invokeParams: InvokeCommandInput = {
                  FunctionName: `vectorizer-${env.LAMBDA_ENV}`,
                  InvocationType: "Event",
                  Payload: JSON.stringify({
                    documentId: doc.id,
                    orgId: ctx.org.id ?? "",
                    bucket: env.AWS_S3_BUCKET_NAME,
                    secretId: env.AWS_LAMBDA_SECRET_ID,
                    stage: "GENERATE_CHUNKS",
                  } as LambdaEvent),
                };

                const command = new InvokeCommand(invokeParams);
                const response = await lambdaClient.send(command);
                console.log("Lambda invoked", response);
              } catch (error) {
                console.error("Error Invoking vectorizer Lambda", error);
                throw error;
              }
            }
          }),
        );

        if (process.env.NODE_ENV === "development") {
          if (i < input.length - 1) {
            console.log("Waiting for 120 seconds before next batch");
            await new Promise((resolve) => setTimeout(resolve, 120000));
          }
        }
      }

      console.timeEnd(
        `Processing documents: ${input.map((d) => d.name).join(", ")}`,
      );
    }),

  generateAnswers: protectedProcedure
    .input(
      z.object({
        documents: z
          .object({
            id: z.string(),
            name: z.string(),
          })
          .array(),
        onlyEmptyAnswers: z.boolean().optional().default(false),
        onlyErroredResponses: z.boolean().optional().default(false),
        responseLibraryMatching: z.boolean().optional().default(false),
      }),
    )
    .mutation(async function* ({ ctx, input }) {
      console.log("Generating answers for documents", input.documents);

      console.time(
        `Generating answers for documents: ${input.documents.map((d) => d.name).join(", ")}`,
      );

      async function* processDocument(doc: { id: string; name: string }) {
        console.time(`Generating answers for document ${doc.name}`);

        if (!input.onlyErroredResponses) {
          await markDocumentStatusGeneratingAnswers({
            db: ctx.db,
            documentId: doc.id,
            orgId: ctx.org.id ?? "",
          });
        }

        const documentWithQuestionsAndEmptyAnswers: GetDocumentWithEmptyAnswersType =
          await ctx.db.document.findFirstOrThrow({
            where: {
              id: doc.id,
              orgId: ctx.org.id ?? "",
            },
            include: {
              responses: {
                where: {
                  documentId: doc.id,
                },

                include: GetDocumentWithEmptyAnswersInclude,
              },
            },
          });

        console.log(
          "documentWithQuestionsAndEmptyAnswers",
          documentWithQuestionsAndEmptyAnswers.responses.map(
            (r) => (r.response.responseContents[0]?.content as JsonObject).text,
          ),
        );

        console.log("input.onlyEmptyAnswers", input.onlyEmptyAnswers);
        console.log("input.onlyErroredResponses", input.onlyErroredResponses);

        const questionIds = documentWithQuestionsAndEmptyAnswers.responses
          .filter((response) => {
            return input.onlyErroredResponses
              ? response.response.responseContents.some(
                  (content) =>
                    (content.content as JsonObject).text ===
                    "Virgil is unable to answer this question",
                )
              : true;
          })
          .filter((response) => {
            return input.onlyEmptyAnswers
              ? (response.response.responseContents[0]?.content as JsonObject)
                  .text === ""
              : true;
          })
          .map((response) => {
            console.log("filtered response", response);
            return response;
          })
          .map((response) =>
            response.response.questions.map((question) => question.id),
          )
          .flat();

        console.log(
          "questionIds",
          questionIds,
          questionIds.length,
          documentWithQuestionsAndEmptyAnswers.responses
            .filter((r) =>
              questionIds.includes(r.response.questions[0]?.id ?? ""),
            )
            .map((response) =>
              response.response.responseContents.map(
                (content) => (content.content as JsonObject).text,
              ),
            ),
        );

        yield {
          status: "searching_responses",
          documentId: doc.id,
          totalResponses: questionIds.length,
          totalResponsesGenerated: 0,
          responses: [],
        };

        const processResponseBatch = async (
          responses: typeof documentWithQuestionsAndEmptyAnswers.responses,
        ) => {
          return Promise.all(
            responses.map(async (response) => {
              const questionContents = (
                response.response.questions[0]?.questionContents[0]
                  ?.content as { text: string }
              )?.text;

              const questionType = response.response.questions[0]?.type;
              const answerTemplate =
                response.response.questions[0]?.answerTemplate;

              const similarResults = await responseLibrarySemanticSearch({
                db: ctx.db,
                orgId: ctx.org.id ?? "",
                query: questionContents,
                currentDocumentId: doc.id,
                returnEmptyResponses: false,
                limit: 20,
              });

              const selectedAnswer = await selectBestMatchingResponse(
                similarResults,
                questionContents,
                questionType,
                answerTemplate ?? undefined,
                ctx.org.name ?? "",
                {},
                doc.id,
              );

              console.log("similarResults", similarResults);
              console.log("selectedAnswer", selectedAnswer);

              if (selectedAnswer.selectedAnswerId !== "") {
                return {
                  questionId: response.response.questions[0]?.id,
                  existingAnswers: similarResults.filter(
                    (r) => r.id === selectedAnswer.selectedAnswerId,
                  ),
                  reasoning: selectedAnswer.reasoning,
                } as ExistingAnswerType;
              }

              return null;
            }),
          );
        };

        const BATCH_SIZE = 20;
        const responses = documentWithQuestionsAndEmptyAnswers.responses;
        const existingAnswers: ExistingAnswerType[] = [];

        // Process responses in batches to avoid overwhelming the LLM
        if (input.responseLibraryMatching) {
          for (let i = 0; i < responses.length; i += BATCH_SIZE) {
            const batch = responses.slice(i, i + BATCH_SIZE);
            const batchResults = await processResponseBatch(batch);
            existingAnswers.push(...batchResults);
          }
        }

        const totalDocumentQuestions = questionIds.length;
        const totalExistingAnswers = existingAnswers.filter(
          (answer) => answer !== null,
        ).length;

        console.log(
          "totalDocumentQuestions",
          totalDocumentQuestions,
          "totalExistingAnswers",
          totalExistingAnswers,
        );

        const questionsToProcess = questionIds.filter((questionId) => {
          return input.responseLibraryMatching
            ? !existingAnswers.some(
                (answer) => answer?.questionId === questionId,
              )
            : true;
        });

        console.log("questionsToProcess", questionsToProcess);

        yield {
          status: "searching_responses_completed",
          documentId: doc.id,
          totalResponses: questionsToProcess.length,
          totalExistingAnswers: totalExistingAnswers,
          questionsToProcess: questionsToProcess.length,
        };

        if (input.responseLibraryMatching) {
          console.log("Adding existing answers to response content");
          await addExistingAnswersToResponseContent(
            ctx.db,
            doc.id,
            existingAnswers,
          );
        }

        const answersIterator = generateAnswersByQuestionIds({
          db: ctx.db,
          orgId: ctx.org.id ?? "",
          documentId: doc.id,
          questionIdsWithCustomPrompt: questionsToProcess.map((q) => ({
            questionId: q,
            customPrompt: "",
          })),
          userId: ctx.auth.id,
        });

        const answers: GeneratedResponse[] = [];
        for await (const {
          status,
          totalResponses,
          totalResponsesGenerated,
          responses,
        } of answersIterator) {
          console.log(
            "generateAnswers: Generated new answers",
            status,
            totalResponses,
            totalResponsesGenerated,
            responses?.length,
          );
          if (status === "error") {
            yield {
              status: "error",
              documentId: doc.id,
              totalResponses: totalResponses ?? 0,
            };
          }

          if (status === "generating_answers") {
            yield {
              status: "generating_answers",
              documentId: doc.id,
              totalResponses: totalResponses ?? 0,
              totalResponsesGenerated: totalResponsesGenerated ?? 0,
              responses: responses ?? [],
            };

            const answersAndResponses = responses.map((response) => ({
              answer: response?.answer ?? "",
              reason: response?.reason ?? "",
              citations: response?.citations ?? [],
              response: documentWithQuestionsAndEmptyAnswers.responses.find(
                (r) =>
                  r.response.questions.find(
                    (q) => q.id === response?.questionId,
                  ),
              ),
            }));

            if (answersAndResponses.length > 0) {
              console.log(
                "Overwriting existing response contents",
                answersAndResponses,
              );

              console.time(
                `Overwriting existing response contents for ${doc.name}. Total responses generated: ${totalResponsesGenerated}`,
              );
              const updatedResponseContents =
                await overwriteExistingResponseContent(
                  ctx.db,
                  answersAndResponses,
                  ctx.auth.id,
                  ctx.org.id ?? "",
                );

              // console.log("updatedResponseContents", updatedResponseContents);
              console.timeEnd(
                `Overwriting existing response contents for ${doc.name}. Total responses generated: ${totalResponsesGenerated}`,
              );
            }
          }

          if (status === "completed") {
            const validResponses = responses.filter(
              (r): r is GeneratedResponse => r !== null,
            );
            yield {
              status: "completed",
              documentId: doc.id,
              totalResponses: totalResponses ?? 0,
              totalResponsesGenerated: totalResponsesGenerated ?? 0,
              responses: validResponses,
            };
            answers.push(...validResponses);
          }
        }

        console.timeEnd(`Generating answers for document ${doc.name}`);
        console.time(`Creating questions and answers for ${doc.name}`);

        console.log("Marking document as ready", doc.name);
        await markDocumentStatusReady({
          db: ctx.db,
          documentId: doc.id,
          orgId: ctx.org.id ?? "",
        });

        console.timeEnd(`Creating questions and answers for ${doc.name}`);
      }

      for (const doc of input.documents) {
        for await (const event of processDocument(doc)) {
          yield event;
        }
      }

      console.timeEnd(
        `Generating answers for documents: ${input.documents.map((d) => d.name).join(", ")}`,
      );
    }),

  delete: protectedProcedure
    .input(DeleteInput)
    .mutation(async ({ ctx, input }) => {
      try {
        // Delete objects from S3 store. Currently deleting from S3 by filename
        // and from DB by id.

        const deleteInput = {
          // DeleteObjectsRequest
          Bucket: process.env.AWS_S3_BUCKET_NAME!,
          Delete: {
            Objects: input.list.map((file) => ({ Key: file.name })),
            Quiet: false,
          },
        };

        const command = new DeleteObjectsCommand(deleteInput);

        const response = await s3Client.send(command);

        console.log(response);

        await cleanupDocuments(ctx.db, ctx.org.id ?? "", input);
      } catch (error) {
        console.error(error);
        throw error;
      }
    }),
  getDocumentContents: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        pageNumber: z.number().optional(),
        citation: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      try {
        const document = await ctx.db.document.findUniqueOrThrow({
          where: { id: input.id, orgId: ctx.org.id ?? "" },
        });

        const documentContents = await getDocumentContents(
          document,
          ctx.db,
          process.env.AWS_S3_BUCKET_NAME,
        );

        const body =
          document.type === DocumentType.PDF
            ? documentContents.body
            : await convertFileToPDF(documentContents.body, document.name);

        console.log("body", body);
        const citedPage = await getPDFCitedPage(body, input.citation ?? "");
        console.log("citedPage", citedPage);

        return citedPage;
      } catch (error) {
        console.error("Error getting document contents", error);
        throw error;
      }
    }),
  getDocumentSections: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        includeResponses: z.boolean().default(false),
      }),
    )
    .query(async ({ ctx, input }) => {
      const sections = await ctx.db.documentSection.findMany({
        where: { documentId: input.id, orgId: ctx.org.id ?? "" },
        include: {
          responses: input.includeResponses,
        },
      });

      return sections;
    }),
});
