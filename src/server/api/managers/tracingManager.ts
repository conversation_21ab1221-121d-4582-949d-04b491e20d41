import { LangfuseTraceClient, LangfuseSpanClient } from "langfuse";
import { CallbackHand<PERSON>, Langfuse } from "langfuse-langchain";
import { env } from "~/env";

export class Tracing {
  private static instance: Tracing;
  private client?: Langfuse;

  private constructor() {
    if (!env.LANGFUSE_PUBLIC_KEY || !env.LANGFUSE_HOST) {
      return;
    }

    this.client = new Langfuse({
      publicKey: env.LANGFUSE_PUBLIC_KEY,
      baseUrl: env.LANGFUSE_HOST,
    });
  }

  public static getInstance(): Tracing {
    if (!Tracing.instance) {
      Tracing.instance = new Tracing();
    }
    return Tracing.instance;
  }

  public score({
    value,
    traceId,
    comment,
    dataType,
  }: {
    value: number;
    traceId: string;
    comment: string;
    dataType: "NUMERIC" | "BOOLEAN" | "CATEGORICAL";
  }) {
    return this.client?.score({
      name: "user-feedback",
      value: value,
      traceId: traceId,
      comment: comment,
      dataType: dataType,
    })
  }

  public trace({ name, sessionId, input, id }: { name: string; sessionId?: string; input?: Record<string, unknown>, id?: string }) {
    return this.client?.trace({ name, sessionId, input, id });
  }

  public getHandlerForTrace(
    trace?: LangfuseTraceClient | LangfuseSpanClient,
    sessionId?: string,
  ) {
    if (!this.client) {
      return null;
    }

    if (!trace) {
      const rootTrace = this.client.trace();
      return new CallbackHandler({ root: rootTrace, sessionId });
    }
    return new CallbackHandler({ root: trace, sessionId });
  }

  public shutdown() {
    return this.client?.shutdown();
  }
}

export const tracing = Tracing.getInstance();
