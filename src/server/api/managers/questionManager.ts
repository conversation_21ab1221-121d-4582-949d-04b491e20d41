import {
  Prisma,
  QuestionCategory,
  QuestionStatusType,
  ResponseFeedbackType,
} from "@prisma/client";
import { PrismaClientType } from "~/server/db";
import { GetQuestionsInclude, GetQuestionsType } from "../routers/question";
import { AggregatedFeedback, FeedbackManager } from "./feedbackManager";

export interface DDQuestion {
  id: string;
  text: string;
  selected: boolean;
  isLoading: boolean;
  response: GetQuestionsType["response"];
  status: QuestionStatusType;
}

export interface DDQuestionWithFeedback extends DDQuestion {
  feedback: AggregatedFeedback | null;
}
export interface DDQuestionWithIndexAndFeedback extends DDQuestionWithFeedback {
  index: number;
}
export class QuestionManager {
  static normalizeQuestions = (questions: GetQuestionsType[]): DDQuestion[] => {
    return questions.map((question) => ({
      id: question.id,
      text: (question.questionContents[0]?.content as { text: string }).text,
      selected: false,
      isLoading: false,
      response: question.response,
      status: question.status,
    }));
  };
  static whereClause = (
    orgId: string,
    documentId: string,
  ): Prisma.QuestionWhereInput => {
    return {
      orgId,
      category: {
        in: [
          QuestionCategory.OTHER,
          QuestionCategory.DATA_ASSURANCE,
          QuestionCategory.STANDARDS_AND_FRAMEWORKS,
          QuestionCategory.INVESTMENT_PROCESS,
        ],
      },

      response: {
        AND: [
          {
            documents: {
              every: {
                documentId,
              },
            },
          },
          {
            NOT: {
              documents: {
                none: {},
              },
            },
          },
        ],
      },
    };
  };
  static async getQuestions({
    db,
    orgId,
    documentId,
    limit,
    cursor,
  }: {
    db: PrismaClientType;
    orgId: string;
    documentId: string;
    limit: number;
    cursor?: string; // react query needs string
  }): Promise<{
    items: DDQuestion[];
    nextCursor?: string;
  }> {
    const whereClause: Prisma.QuestionWhereInput = this.whereClause(
      orgId,
      documentId,
    );
    const cursorInt = cursor ? parseInt(cursor) : 0;
    const questions = await db.question.findMany({
      where: whereClause,
      include: GetQuestionsInclude,
      take: limit + 1,
      skip: cursorInt,
      orderBy: {
        index: "asc",
      },
    });

    const hasMore = questions.length > limit;
    const items: GetQuestionsType[] = hasMore
      ? questions.slice(0, -1)
      : questions;
    const nextCursor = hasMore ? (cursorInt + limit).toString() : undefined;

    return {
      items: this.normalizeQuestions(items),
      nextCursor,
    };
  }

  static async getQuestionsWithFeedback({
    db,
    orgId,
    documentId,
    limit,
    cursor,
    userId,
  }: {
    db: PrismaClientType;
    orgId: string;
    documentId: string;
    limit: number;
    cursor?: string;
    userId: string;
  }): Promise<{
    items: DDQuestionWithFeedback[];
    nextCursor?: string;
  }> {
    const questions = await this.getQuestions({
      db,
      orgId,
      documentId,
      limit,
      cursor,
    });
    const feedbackBatch = await FeedbackManager.getAggregatedFeedbackBatch({
      db,
      questionIds: questions.items.map((question) => question.id),
      orgId,
      userId,
    });
    const items = questions.items.map((question) => ({
      ...question,
      feedback: feedbackBatch.aggregatedByQuestionId[question.id] ?? null,
    }));
    return { items, nextCursor: questions.nextCursor };
  }
}
