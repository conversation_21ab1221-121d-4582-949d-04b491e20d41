import { ResponseFeedbackType, ResponseFeedback } from "@prisma/client";
import { type PrismaClientType } from "~/server/db";

export type AggregatedFeedback = {
  good: number;
  bad: number;
  self: ResponseFeedbackType | null;
};

export type AggregatedFeedbackBatch = {
  aggregatedByQuestionId: Record<string, AggregatedFeedback>;
};

type DocumentFeedbackStats = {
  goodQuestions: number;
  badQuestions: number;
  questionStats: Array<{
    questionId: string;
    good: number;
    bad: number;
  }>;
};

export class FeedbackManager {
  static async createorUpdateFeedback({
    db,
    questionId,
    documentId,
    feedback,
    type,
    reason,
    responseText,
    orgId,
    userId,
  }: {
    db: PrismaClientType;
    orgId: string;
    userId: string;
    questionId: string;
    documentId: string;
    type: ResponseFeedbackType;
    feedback?: string | null;
    reason?: string | null;
    responseText?: string | null;
  }): Promise<ResponseFeedback> {
    const existingFeedback = await db.responseFeedback.findFirst({
      where: {
        createdById: userId,
        orgId,
        questionId,
        documentId,
      },
    });

    if (existingFeedback) {
      return db.responseFeedback.update({
        where: { id: existingFeedback.id },
        data: {
          type,
          feedback,
          reason,
          responseText,
        },
      });
    }

    return db.responseFeedback.create({
      data: {
        orgId,
        type,
        feedback,
        questionId,
        documentId,
        createdById: userId,
        reason,
        responseText,
      },
    });
  }

  static async getAggregatedFeedback({
    db,
    questionId,
    orgId,
    userId,
  }: {
    db: PrismaClientType;
    questionId: string;
    orgId: string;
    userId: string;
  }): Promise<AggregatedFeedback> {
    const feedbackGoodCount = await db.responseFeedback.count({
      where: {
        questionId,
        orgId,
        type: ResponseFeedbackType.GOOD,
      },
    });

    const feedbackBadCount = await db.responseFeedback.count({
      where: {
        questionId,
        orgId,
        type: ResponseFeedbackType.BAD,
      },
    });

    const hasSelfFeedback = await db.responseFeedback.findFirst({
      where: {
        questionId,
        orgId,
        createdById: userId,
      },
    });

    return {
      good: feedbackGoodCount,
      bad: feedbackBadCount,
      self: hasSelfFeedback?.type ?? null,
    };
  }

  static async getAggregatedFeedbackBatch({
    db,
    questionIds,
    orgId,
    userId,
  }: {
    db: PrismaClientType;
    questionIds: string[];
    orgId: string;
    userId: string;
  }): Promise<AggregatedFeedbackBatch> {
    const feedbacks = await db.responseFeedback.findMany({
      where: {
        questionId: { in: questionIds },
        orgId,
      },
    });

    const aggregatedFeedbackByQuestionId: Record<string, AggregatedFeedback> =
      feedbacks.reduce(
        (acc, feedback) => {
          const questionId: string | null = feedback.questionId;
          if (!questionId) return acc;
          if (!acc[questionId]) {
            acc[questionId] = {
              good: 0,
              bad: 0,
              self: null,
            };
          }

          if (feedback.type === ResponseFeedbackType.GOOD) {
            acc[questionId].good++;
          } else if (feedback.type === ResponseFeedbackType.BAD) {
            acc[questionId].bad++;
          }

          if (feedback.createdById === userId) {
            acc[questionId].self = feedback.type;
          }

          return acc;
        },
        {} as Record<
          string,
          {
            good: number;
            bad: number;
            self: ResponseFeedbackType | null;
          }
        >,
      );

    return {
      aggregatedByQuestionId: aggregatedFeedbackByQuestionId,
    };
  }

  static async getUserFeedback({
    db,
    responseId,
    orgId,
    userId,
  }: {
    db: PrismaClientType;
    responseId: string;
    orgId: string;
    userId: string;
  }): Promise<ResponseFeedback | null> {
    return db.responseFeedback.findFirst({
      where: {
        responseId,
        orgId,
        createdById: userId,
      },
    });
  }

  static async getDocumentFeedbackStats({
    db,
    documentId,
    orgId,
  }: {
    db: PrismaClientType;
    documentId: string;
    orgId: string;
  }): Promise<DocumentFeedbackStats> {
    const feedbacks = await db.responseFeedback.findMany({
      where: {
        documentId,
        orgId,
      },
      include: {
        question: true,
      },
    });

    const questionFeedback = new Map<string, { good: number; bad: number }>();

    feedbacks.forEach((feedback) => {
      if (!feedback.questionId) return;

      const current = questionFeedback.get(feedback.questionId) || {
        good: 0,
        bad: 0,
      };
      if (feedback.type === ResponseFeedbackType.GOOD) {
        current.good++;
      } else {
        current.bad++;
      }
      questionFeedback.set(feedback.questionId, current);
    });

    let goodQuestions = 0;
    let badQuestions = 0;

    questionFeedback.forEach((stats) => {
      if (stats.bad > 0) {
        badQuestions++;
      } else if (stats.good > 0) {
        goodQuestions++;
      }
    });

    return {
      goodQuestions,
      badQuestions,
      questionStats: Array.from(questionFeedback.entries()).map(
        ([questionId, stats]) => ({
          questionId,
          ...stats,
        }),
      ),
    };
  }

  static async resetFeedbackPerDocument({
    db,
    documentId,
    orgId,
  }: {
    db: PrismaClientType;
    documentId: string;
    orgId: string;
  }): Promise<void> {
    await db.responseFeedback.deleteMany({
      where: {
        documentId,
        orgId,
      },
    });
  }
}
