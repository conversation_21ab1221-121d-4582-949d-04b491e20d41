// jest.config.js
module.exports = {
  testEnvironment: "jsdom",
  setupFilesAfterEnv: ["<rootDir>/jest.setup.js"],
  transform: {
    "^.+\\.(js|jsx|ts|tsx|mjs)$": [
      "babel-jest",
      { configFile: "./.babel.jest.js" },
    ],
  },
  // This is a list of packages are dependencies of react-markdown and need to be specified
  transformIgnorePatterns: [
    "node_modules/(?!(" +
      [
        "react-markdown",
        "remark-gfm",
        "unified",
        "remark-parse",
        "remark-rehype",
        "rehype-raw",
        "hast-util-.*",
        "vfile-*",
        "trough",
        "unist-util-.*",
        "bail",
        "is-plain-obj",
        "micromark.*",
        "decode-named-character-reference",
        "character-entities",
        "property-information",
        "space-separated-tokens",
        "comma-separated-tokens",
        "mdast-util-.*",
        "ccount",
        "escape-string-regexp",
        "devlop",
        "estree-util-is-identifier-name",
        "estree-util-visit",
        "estree-util-visit-parents",
        "estree-util-stringify",
        "estree-util-to-fast-path",
        "estree-util-to-fast-path",
        "vfile-.*",
        "html-url-attributes",
        "html-void-elements",
        "html-whitespace-capture",
        "html-void-elements",
        "trim-lines",
        "markdown-table",
        "zwitch",
        "longest-streak",
      ].join("|") +
      ")/)",
  ],
  moduleFileExtensions: ["js", "jsx", "ts", "tsx", "mjs"],
  moduleNameMapper: {
    "^(\\.{1,2}/.*)\\.js$": "$1",
  },
};
