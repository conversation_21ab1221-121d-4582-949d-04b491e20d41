name: Vercel Dev Deployment
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
  DATABASE_URL: ${{ secrets.DEV_DATABASE_URL }}
  ECR_HOST: ${{ secrets.DEV_ECR_HOST }}
  ECR_PATH: ${{ secrets.DEV_ECR_PATH }}
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_REGION: ${{ secrets.AWS_REGION }}
  NODE_OPTIONS: --max-old-space-size=16384
on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  Deploy-Lambda-Functions:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Deploy Lambda Functions
        run: |
          npm install esbuild --legacy-peer-deps
          ./scripts/deploy-lambda.sh dev

  Deploy-Docker-Image:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@62f4f872db3836360b72999f4b87f1ff13310f3a

      - name: Deploy Docker Image
        id: build-docker-image
        run: |
          docker buildx build --platform=linux/amd64 -f infra/containers/pyrpc/Dockerfile -t ${{ env.ECR_HOST }}/${{ env.ECR_PATH }}:latest infra/containers/pyrpc
          docker tag ${{ env.ECR_HOST }}/${{ env.ECR_PATH }}:latest ${{ env.ECR_HOST }}/${{ env.ECR_PATH }}:${{ github.sha }}
          docker push ${{ env.ECR_HOST }}/${{ env.ECR_PATH }}:latest

  Deploy-Production:
    runs-on:
      ubuntu-latest-m
      # To use Turborepo Remote Caching, set the following environment variables for the job.
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
    steps:
      - name: Setup DIRECT_URL
        run: echo DIRECT_URL=${DATABASE_URL/-pooler//} >> $GITHUB_ENV
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5

      - name: Cache turbo build setup
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: "npm"

      - name: Install Vercel CLI
        run: npm install --global vercel@latest --legacy-peer-deps
      - name: Apply all pending migrations to the database
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: ${{ secrets.DEV_DATABASE_URL }}

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      - name: Build Project Artifacts
        run: |
          npm install --legacy-peer-deps
          vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }} --archive=tgz
