name: Deploy Alpine (Manual)
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.ALPINE_VERCEL_PROJECT_ID }}
  DATABASE_URL: ${{ secrets.ALPINE_DATABASE_URL }}
  NODE_OPTIONS: --max-old-space-size=16384

on: workflow_dispatch

jobs:
  Deploy-Alpine:
    runs-on: ubuntu-latest-m
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
      - name: Install Vercel CLI
        run: npm install --global vercel@latest --legacy-peer-deps
      - name: Apply all pending migrations to the database
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: ${{ secrets.ALPINE_DATABASE_URL }}
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }} --archive=tgz
