name: E2E Test on Dev

description: |
  This workflow runs E2E tests on the dev environment when code is pushed to the main branch.

on:
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest-m
    defaults:
      run:
        working-directory: e2e/

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install dependencies
        run: npm ci

      - name: Validate secrets
        run: |
          if [ -z "${{ secrets.E2E_PLAYWRIGHT_USER_NAME }}" ]; then
            echo "Error: E2E_PLAYWRIGHT_USER_NAME secret is not set."
            exit 1
          fi
          if [ -z "${{ secrets.E2E_PLAYWRIGHT_USER_PASSWORD }}" ]; then
            echo "Error: E2E_PLAYWRIGHT_USER_PASSWORD secret is not set."
            exit 1
          fi
          if [ -z "${{ secrets.E2E_PLAYWRIGHT_BASEURL }}" ]; then
            echo "Error: E2E_PLAYWRIGHT_BASEURL secret is not set."
            exit 1
          fi

      - name: Run Playwright tests
        env:
          userEmail: ${{ secrets.E2E_PLAYWRIGHT_USER_NAME }}
          userPassword: ${{ secrets.E2E_PLAYWRIGHT_USER_PASSWORD }}
          baseURL: ${{ secrets.E2E_PLAYWRIGHT_BASEURL }}
        run: npx playwright test

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: e2e/playwright-report/
          retention-days: 30
