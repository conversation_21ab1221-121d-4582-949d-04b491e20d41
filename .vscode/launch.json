{"version": "0.2.0", "configurations": [{"name": "Python Debugger: FastAPI", "type": "debugpy", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["pyrpc.index:app", "--reload", "--port", "5328"], "cwd": "${workspaceFolder}/infra/containers/pyrpc/app", "jinja": true, "env": {"PYTHONPATH": "${workspaceFolder}/infra/containers/pyrpc/app"}}, {"type": "node", "request": "launch", "runtimeExecutable": "node", "name": "Node Debugger: NextJS", "env": {"NODE_OPTIONS": "--max-old-space-size=16000"}}]}