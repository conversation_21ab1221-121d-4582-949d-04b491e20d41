<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp xmlns="http://schemas.microsoft.com/office/appforoffice/1.1"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0"
  xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="TaskPaneApp">
  <Id>{{excelId}}</Id>
  <Version>{{excelVersion}}</Version>
  <ProviderName>{{providerName}}</ProviderName>
  <DefaultLocale>{{defaultLocale}}</DefaultLocale>
  <DisplayName DefaultValue="{{displayName}}" />
  <Description DefaultValue="{{description}}" />
  <IconUrl DefaultValue="{{assetsHostname}}/logo/virgil-square-64.png" />
  <HighResolutionIconUrl DefaultValue="{{assetsHostname}}/logo/virgil-square.png" />
  <SupportUrl DefaultValue="{{supportUrl}}" />
  <AppDomains>
    {{#each appDomains}}
    <AppDomain>{{this}}</AppDomain>
    {{/each}}
  </AppDomains>
  <Hosts>
    <Host Name="Workbook" />
  </Hosts>
  <DefaultSettings>
    <SourceLocation DefaultValue="{{entryPointFullUrlExcel}}" />
  </DefaultSettings>
  <Permissions>ReadWriteDocument</Permissions>
  <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides"
    xsi:type="VersionOverridesV1_0">
    <Hosts>
      <Host xsi:type="Workbook">
        <DesktopFormFactor>
          <GetStarted>
            <Title resid="GetStarted.Title" />
            <Description resid="GetStarted.Description" />
            <LearnMoreUrl resid="GetStarted.LearnMoreUrl" />
          </GetStarted>
          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <OfficeTab id="TabHome">
              <Group id="CommandsGroup">
                <Label resid="CommandsGroup.Label" />
                <Icon>
                  <bt:Image size="16" resid="Icon.16x16" />
                  <bt:Image size="32" resid="Icon.32x32" />
                  <bt:Image size="80" resid="Icon.80x80" />
                </Icon>

                <Control xsi:type="Button" id="TaskpaneButton">
                  <Label resid="TaskpaneButton.Label" />
                  <Supertip>
                    <Title resid="TaskpaneButton.SupertipTitle" />
                    <Description resid="TaskpaneButton.SupertipText" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Icon.16x16" />
                    <bt:Image size="32" resid="Icon.32x32" />
                    <bt:Image size="80" resid="Icon.80x80" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>ButtonId1</TaskpaneId>
                    <SourceLocation resid="Taskpane.Url" />
                  </Action>
                </Control>
              </Group>
            </OfficeTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>
    </Hosts>
    <Resources>
      <bt:Images>
        <bt:Image id="Icon.16x16" DefaultValue="{{assetsHostname}}/logo/virgil-square-16.png" />
        <bt:Image id="Icon.32x32" DefaultValue="{{assetsHostname}}/logo/virgil-square-32.png" />
        <bt:Image id="Icon.80x80" DefaultValue="{{assetsHostname}}/logo/virgil-square-80.png" />
      </bt:Images>
      <bt:Urls>
        <bt:Url id="GetStarted.LearnMoreUrl" DefaultValue="{{entryPointHostname}}/ms-office-addin-learn-more" />
        <bt:Url id="Taskpane.Url" DefaultValue="{{entryPointFullUrlExcel}}" />
      </bt:Urls>
      <bt:ShortStrings>
        <bt:String id="GetStarted.Title" DefaultValue="Get started with the Virgil AI add-in!" />
        <bt:String id="CommandsGroup.Label" DefaultValue="{{commandsGroupLabel}}" />
        <bt:String id="TaskpaneButton.Label" DefaultValue="{{taskpaneButtonLabel}}" />
        <bt:String id="TaskpaneButton.SupertipTitle" DefaultValue="{{taskpaneButtonSupertipTitle}}" />
      </bt:ShortStrings>
      <bt:LongStrings>
        <bt:String id="GetStarted.Description" DefaultValue="{{getStartedDescription}}" />
        <bt:String id="TaskpaneButton.SupertipText" DefaultValue="{{taskpaneButtonSupertipText}}" />
      </bt:LongStrings>
    </Resources>
  </VersionOverrides>
</OfficeApp>