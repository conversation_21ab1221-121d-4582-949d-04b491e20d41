[{"name": "excel-add-in-hello-world", "reponame": "office-add-in-samples", "source": "officedev", "title": "Create an Excel add-in that displays hello world", "shortDescription": "Create a simple Excel add-in that displays hello world.", "url": "https://github.com/OfficeDev/Office-Add-in-samples/tree/main/Samples/hello-world/excel-hello-world", "longDescription": ["Learn how to build the simplest Office Add-in with only a manifest, HTML web page, and a logo. Understand the fundamental parts of an Office Add-in."], "creationDateTime": "2021-10-11", "updateDateTime": "2021-10-11", "products": ["Office"], "metadata": [{"key": "CLIENT-SIDE-DEV", "value": "JavaScript"}], "thumbnails": [{"type": "Image", "order": 100, "url": "https://raw.githubusercontent.com/OfficeDev/Office-Add-in-samples/main/Samples/hello-world/images/hello-world-introduction.png", "alt": "Diagram showing a hello project consists of a manifest, HTML page, and image assets."}], "authors": [{"gitHubAccount": "microsoft", "pictureUrl": "https://github.com/microsoft.png", "name": "Microsoft"}], "references": [{"name": "Office Add-ins platform overview", "description": "You can use the Office Add-ins platform to build solutions that extend Office applications and interact with content in Office documents.", "url": "https://learn.microsoft.com/office/dev/add-ins/overview/office-add-ins"}]}]